import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ViewDocumentRequest } from 'app/model/api/view.document.request';
import { ViewDocumentResponse } from 'app/model/api/view.document.response';
import { Document } from 'app/model/document';
import { Monitoring } from 'app/model/monitoring';
import { MonitoringDocument } from 'app/model/monitoring-document';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDate, QuestionDropdown, QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { Align } from 'app/shared/components/msx-datatable/enums/align';
import { ColumnType } from 'app/shared/components/msx-card-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-card-datatable/models/table';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { TenantSettingRequest } from 'app/shared/dto/tenant-setting/tenant-setting.request';
import { DeviceDetectorService } from 'ngx-device-detector';
import { SignerComponent } from '../inquiry/modal/signer/signer.component';
import * as swalFunction from '../../shared/data/sweet-alerts';
import { BaseResponse } from 'app/shared/dto/base.response';
import { StartStampingRequest } from 'app/shared/dto/inquiry/start-stamping.request';
import { RetryStampingMeteraiRequest } from 'app/shared/dto/inquiry/retry-stamping.request';
import { DocumentService } from 'app/services/api/document.service';
import { CancelSignNormalRequest } from 'app/model/api/document/cancel-sign-normal.request';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-monitoring-document',
  templateUrl: './monitoring-document.component.html',
  styleUrls: ['./monitoring-document.component.scss']
})
export class MonitoringDocumentComponent implements OnInit {
  isMobile = false;
  view: MsxView;
  serviceUrl = URLConstant.MonitoringDocument;
  refNumberLabel: string;
  searchFormObj: FormModel<any>;
  table: Table<MonitoringDocument>;
  swal = swalFunction;
  isSubmitting: boolean = false;

  constructor(private global: GlobalService, private http: HttpClient,
    private deviceService: DeviceDetectorService, private modalService: NgbModal,
    private router: Router, private documentService: DocumentService) {
      this.isMobile = deviceService.isMobile();
    }

  async ngOnInit() {
    await this.getRefNumLabel();
    this.initView();
  }

  openSignerList(data) {
    const modal = this.modalService.open(SignerComponent, {size: 'lg'});
    modal.componentInstance.documentId = data.documentId;
  }

  openDocument(data, download: boolean) {
    const request = new ViewDocumentRequest();
    request.documentId = data.documentId;
    this.http.post<ViewDocumentResponse>(URLConstant.ViewDocument, request).subscribe(
      (response) => {
        if (response.status.code !== 0) {
          return;
        }

        // Mobile device will only download document (no viewing)
        // if (this.isMobile) {
        //   console.log('Mobile device detected. Downloading document');
        //   const filename = data.docTemplateName + '.pdf';
        //   const element = document.createElement('a');
        //   element.href = `data:application/pdf;base64,${response.pdfBase64}`;
        //   element.download = filename;
        //   element.click();
        //   return;
        // }

        if (download) {
          const filename = data.docTemplateName + '.pdf';
          const element = document.createElement('a');
          element.href = `data:application/pdf;base64,${response.pdfBase64}`;
          element.download = filename;
          element.click();
          return;
        }
        const extras = { pdfBase64: response.pdfBase64, refNumber: data.refNumber };
        data = { ...data, ...extras };
        this.router.navigate([PathConstant.VIEW_DOCUMENT_INQUIRY], { state: data });
      }
    )
  }

  resendNotification(data) {
    this.swal.Confirm('Akan mengirimkan notifikasi tanda tangan ke user yang belum tanda tangan di dokumen dengan ref number ' + data.refNumber, 'Apakah Anda yakin?').then(
      (result) => {
        if (result.isConfirmed) {
          if (this.isSubmitting) {
            return;
          }
          this.isSubmitting = true;
          this.http.post(URLConstant.ResendNotificationNormal, { documentId: data.documentId }).subscribe(
            (response) => {
              if (response['status']['code'] === 0) {
                this.swal.Success('Kirim notifikasi ulang sukses');
              }
            }
          ).add(() => { 
            this.isSubmitting = false;
          });
        }
      }
    );
  }

  startDocumentStamping(data) {
    this.swal.Confirm('Apakah Anda yakin ingin memulai stamping?').then(
      (result) => {
        if (result.isConfirmed) {
          const request = new StartStampingRequest();
          request.tenantCode = this.global.user.role.tenantCode;
          request.refNumber = data.refNumber;
          this.http.post<BaseResponse>(URLConstant.StartStampingDocument, request).subscribe(
            (response) => {
              if (response.status.code === 0) {
                this.swal.Success('Proses stamping berhasil dimulai');
              }
            }
          )
        }
      }
    );
  }

  retryStampingDocument(data){
    this.swal.Confirm('Apakah Anda yakin ingin retry stamping?').then(
      (result) => {
        if (result.isConfirmed) {
          const request = new RetryStampingMeteraiRequest;
          request.refNumber = data.refNumber;
          request.tenantCode = this.global.user.role.tenantCode;
          this.http.post<BaseResponse>(URLConstant.RetryStampingMeterai, request).subscribe(
            (response) => {
              if (response.status.code === 0) {
                this.swal.Success('Retry stamping berhasil dimulai').then(() => {
                  window.location.reload();
                });

              }
            }
          )
        }
      }
    );
  }



  onItemClickListener(result: any) {
    const data = result['data'];
    console.log('On click', data);

    switch (result['act']['type']) {
      case Act.ViewSigner:
        return this.openSignerList(data);
      case Act.Download:
        return this.openDocument(data, true);
      case Act.View:
        return this.openDocument(data, false);
      case Act.Resend:
        return this.resendNotification(data);
      case Act.StartStamping:
        return this.startDocumentStamping(data);
      case Act.RetryStamping:
        return this.retryStampingDocument(data);
      case Act.Cancel:
        return this.cancelSign(data);
      default:
        return this.openDocument(data, false);
    }
  }

  cancelSign(data) {
    const refNumber = data.refNumber;
    const confirmationText = 'Apakah Anda yakin ingin membatalkan ' + refNumber + '?';

    this.swal.Confirm(confirmationText).then((result) => {
      if (!result.isConfirmed) {
        return;
      }

      const request = new CancelSignNormalRequest();
      request.refNumber = data.refNumber;
      
      this.documentService.cancelSignNormal(request).subscribe((response) => {
        if (response.status.code !== 0) {
          return;
        }

        this.swal.SuccessWithRedirect('Dokumen berhasil dibatalkan', this.router.url);

      });
    });
  }

  initView() {
    this.searchFormObj = {
      name: 'monitoringDocumentSearchForm',
      direction: FormConstant.DIRECTION_HORIZONTAL,
      colSize: 6,
      exportExcel: false,
      components: [
        new QuestionTextbox({
          key: 'customerName',
          label: CommonConstant.LABEL_CUSTOMER_NAME,
          placeholder: 'Type customer here',
          value: ''
        }),
        new QuestionTextbox({
          key: 'refNumber',
          label: this.refNumberLabel,
          placeholder: 'Type ref number here',
          value: ''
        }),
        new QuestionDate({
          key: 'requestedDateStart',
          label: 'Request Date From',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDate({
          key: 'requestedDateEnd',
          label: 'Request Date To',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDate({
          key: 'completedDateStart',
          label: 'Completed Date From',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDate({
          key: 'completedDateEnd',
          label: 'Completed Date To',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDropdown({
          key: 'docType',
          label: 'Doc Type',
          placeholder: 'Select document type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'DOC_TYPE'
          }
        }),
        new QuestionDropdown({
          key: 'transactionStatus',
          label: 'Status TTD',
          placeholder: 'Select status tanda tangan',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'LOV_SIGN_STATUS'
          }
        }),
        new QuestionDropdown({
          key: 'regionCode',
          label: 'Region',
          placeholder: 'Select region',
          serviceUrl: URLConstant.RegionList,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'regionList',
            key: 'regionCode',
            value: 'regionName'
          },
          params: {
            tenantCode: this.global.user.role.tenantCode
          }
        }),
        new QuestionDropdown({
          key: 'officeCode',
          label: 'Office',
          placeholder: 'Select office',
          serviceUrl: URLConstant.OfficeList,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'officeList',
            key: 'officeCode',
            value: 'officeName'
          },
          params: {
            tenantCode: this.global.user.role.tenantCode
          }
        }),
        new QuestionDropdown({
          key: 'stampingStatus',
          label: 'Proses Stamping',
          placeholder: 'Select All',
          options: [
            {key: '', value: 'All'},
            {key: 'Not Started', value: 'Not Started'},
            {key: 'Failed', value: 'Failed'},
            {key: 'In Progress', value: 'In Progress'},
            {key: 'Success', value: 'Success'}
          ],
        }),
        new QuestionDropdown({
          key: 'isActive',
          label: 'Status',
          controlType: FormConstant.TYPE_DROPDOWN,
          placeholder: 'Select Status',
          options: [
            {key: '', value: 'All'},
            {key: '1', value: 'Active'},
            {key: '0', value: 'Inactive'}
          ],
          value: ''
        }),
      ],
      params: [
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        },
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.user.role.tenantCode
        }
      ]
    };

    const MonitoringTable: Table<Monitoring> = {
      name: 'listDocument',
      list: [],
      columns: [
          {
              type: ColumnType.Text,
              cardType: ColumnType.Title,
              prop: 'refNumber',
              label: this.refNumberLabel,
              width: 100
          },
          {
            type: ColumnType.Text,
            cardType: ColumnType.Title,
            prop: 'docTypeName',
            label: 'Doc Type',
            width: 100
          },
          {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'docTemplateName',
            label: 'Doc Name',
            width: 100
          },
          {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'customerName',
            label: CommonConstant.LABEL_CUSTOMER_NAME,
            width: 100
          },
          {
            type: ColumnType.Date,
            cardType: ColumnType.Detail,
            format: CommonConstant.FORMAT_DATE_WITHTIME,
            prop: 'requestDate',
            label: CommonConstant.LABEL_REQUEST_DATE,
            width: 80
          },
          {
            type: ColumnType.Date,
            cardType: ColumnType.Detail,
            format: CommonConstant.FORMAT_DATE_WITHTIME,
            prop: 'completeDate',
            label: CommonConstant.LABEL_COMPLETE_DATE,
            width: 80
          },
          {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'totalSigned',
            label: CommonConstant.LABEL_TOTAL_SIGNER_SIGNED,
            width: 80
          },
          {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'totalStamped',
            label: CommonConstant.LABEL_TOTAL_MATERAI,
            width: 80
          }, 
          {
            type: ColumnType.Text,
            prop: 'signStatus',
            label: 'Status TTD',
            width: 90,
            align: Align.Center,
            class: CommonConstant.TEXT_DANGER,
            condition: true,
            conditionedClass: CommonConstant.TEXT_SUCCESS,
            conditionExpected: 'Complete',
            isStatusCardMobile: true
          },
          {
            type: ColumnType.IsActive,
            cardType: ColumnType.Detail,
            prop: 'isActive',
            label: 'Status',
            width: 70
          },
          {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'documentArchiveStatus',
            label: CommonConstant.LABEL_STATUS_ARCHIVE,
            width: 90
          },
          {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-eye',
                type: Act.View,
                descr: 'View',
                cardAction: true,
                mobileMode: false, 
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: CommonConstant.ICON_FT_DOWNLOAD,
                type: Act.Download,
                descr: 'Download',
                cardAction: false,
                mobileMode: true,
                iconSrc: CommonConstant.ICON_SRC_DOWNLOAD
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-user',
                type: Act.ViewSigner,
                descr: 'View Signer',
                cardAction: false,
                mobileMode: true,
                iconSrc: CommonConstant.ICON_SRC_USER
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                type: Act.Resend,
                icon: 'ft-mail',
                condition: true,
                conditionedClass: 'd-none',
                conditionVariable: 'signStatus',
                conditionExpected: 'Complete',
                descr: 'Resend Notification',
                cardAction: false,
                mobileMode: true,
                iconSrc: CommonConstant.ICON_SRC_MAIL
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-play-circle',
                descr: 'Start Stamping',
                type: Act.StartStamping,
                condition: true,
                conditionVariable: 'canStartStamp',
                conditionExpected: '0',
                conditionedClass: 'd-none',
                cardAction: false,
                mobileMode: true,
                iconSrc: CommonConstant.ICON_SRC_PLAY
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                type: Act.RetryStamping,
                icon: 'ft-rotate-cw',
                condition: true,
                conditionedClass: 'd-none',
                conditionVariable: 'canRetryStamp',
                conditionExpected: '0' ,
                descr: 'Retry Stamping',
                cardAction: false,
                mobileMode: true,
                iconSrc: CommonConstant.ICON_SRC_RETRY
              },
              {
                class: CommonConstant.TEXT_DANGER,
                icon: 'ft-x-circle',
                type: Act.Cancel,
                condition: true,
                conditionedClass: 'd-none',
                conditionVariable: 'isActive',
                conditionExpected: '0',
                descr: 'Cancel Document',
                cardAction: false,
                mobileMode: true,
                iconSrc: CommonConstant.ICON_SRC_CANCEL
              }
            ]
        },
          {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'officeName',
            label: 'Office',
            width: 90
          },
          {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'regionName',
            label: 'Region',
            width: 90
          },
          {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'statusProsesMaterai',
            label: 'Proses Stamping',
            width: 90
          }
          
          
      ]
    };

    this.view = {
      title: 'Monitoring Document',
      isCard: true,
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: MonitoringTable
        }
      ]
    }
  }

  async getRefNumLabel() {
    const url = URLConstant.GetTenantSetting;

    const request = new TenantSettingRequest();
    request.param = 'REF_NO_LABEL';
    request.tenantCode = this.global.user.role.tenantCode;

    await this.http.post(url, request).toPromise().then(
      (response) => {
        if (response['status']['code'] === 0) {
          let label = response['refNumberLabel'];
          label = label.includes(' :') ? label.substring(0, label.length-2) : label;
          this.refNumberLabel = label;
        } else {
          this.refNumberLabel = 'No Kontrak';
        }
      }
    );
  }

}
