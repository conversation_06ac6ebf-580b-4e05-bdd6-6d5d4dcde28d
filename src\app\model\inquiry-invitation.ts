export class InquiryInvitation {
  provinsi: string;
  kota: string;
  kecamatan: string;
  adminEmail: string;
  loginId: string;
  email: string;
  idPhoto: string;
  selfPhoto: string;
  npwpPhoto: string;
  ttd: string;
  redirect: boolean;
  status: string;
  autosignStatus: string;
  idEmailHosting: number;
  getkUser: string;
  token: string;
  isActive: string;
  isRegistered: string;
  invBy: string;
  recieverDetail: string;
  invCrt: string;
  kelurahan: string;
  kodePos: string;
  nama: string;
  alamat: string;
  jenisKelamin: string;
  tlp: string;
  tmpLahir: string;
  tglLahir: string;
  idKtp: string;
  npwp: string;
  vendorCode: string;
  vendorName: string;
}
