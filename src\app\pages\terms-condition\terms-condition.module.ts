import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { TranslateModule } from "@ngx-translate/core";
import { TermsConditionRoutingModule } from "./terms-condition-routing.module";
import { TermsConditionComponent } from "./terms-condition.component";

@NgModule({
    declarations: [TermsConditionComponent],
    imports: [
      CommonModule,
      NgbModule,
      ReactiveFormsModule,
      TermsConditionRoutingModule,
      TranslateModule
    ]
  })
  export class TermsConditionModule { }