import { ListInquiryAuditTrailRelatedDocument } from "app/model/api/AuditTrailRelatedDocument";
import { Signer } from "app/model/signer";
import { ListMessageDeliveryReportComponent } from "app/pages/message-delivery-report/list-message-delivery-report/list-message-delivery-report.component";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { CommonConstant } from "app/shared/constant/common.constant";

export const ListInquirySendNotificationMessageDelivery: Table<ListMessageDeliveryReportComponent> = {
    name: 'listDeliveryReport',
    list: [],
    columns: [
        {
            type: ColumnType.Number,
            prop: 'no',
            label: 'No.',
            width: 30
        },
        {
          type: ColumnType.Date,
          format: CommonConstant.FORMAT_DATE_WITHTIME_SECOND,
          prop: 'reportDate',
          label: 'Report Time',
          width: 150
        },
        {
            type: ColumnType.Text,
            prop: "messageMedia",
            label: "Message Media",
            width: 100,
          },
        {
          type: ColumnType.Text,
          prop: "vendor",
          label: "Vendor",
          width: 100,
        },
        {
          type: ColumnType.Text,
          prop: "deliveryStatus",
          label: "Delivery Status",
          width: 100,
        }
    ]
}