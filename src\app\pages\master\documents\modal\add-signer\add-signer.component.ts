import {ChangeDetectorRef, Component, Input, OnInit} from '@angular/core';
import {AbstractControl, FormBuilder, FormGroup, Validators} from '@angular/forms';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import {UserService} from '../../../../../services/api/user.service';
import {CheckRegisterStatusRequest} from '../../../../../model/api/check.register.status.request';
import {TenantService} from '../../../../../services/api/tenant.service';
import {BaseRequest} from '../../../../../model/api/base.request';
import {Signer} from '../../../../../shared/components/document-anotate/model/signer';
import {AutofillUserRequest} from '../../../../../model/api/autofill.user.request';

@Component({
  selector: 'app-add-signer',
  templateUrl: './add-signer.component.html',
  styleUrls: ['./add-signer.component.scss']
})
export class AddSignerComponent implements OnInit {

  @Input()
  signers: Signer[];

  @Input()
  vendorCode: string;

  signerForm: FormGroup;
  message: string;
  waiting: boolean;
  emailService: boolean;

  constructor(public activeModal: NgbActiveModal, private fb: FormBuilder, private cdr: ChangeDetectorRef,
              private userService: UserService, private tenantService: TenantService) {
    this.waiting = false;
  }

  ngOnInit(): void {
    this.initForm();
    this.getEmailServiceStatus();
  }

  onSubmit() {
    if (!this.checkSigner()) {
      return;
    }

    const request: CheckRegisterStatusRequest = new CheckRegisterStatusRequest();
    request.email = this.signerForm.get('email').value;
    request.phoneNum = this.signerForm.get('phone').value;
    request.vendorCode = this.vendorCode;

    this.userService.checkRegisterStatus(request).subscribe(response => {
      if (response.status.code !== 0) {
        this.message = response.status.message;
        return;
      }

      this.activeModal.dismiss({data: this.signerForm.getRawValue()});
    });

  }

  initForm() {
    this.signerForm = this.fb.group({
      name: ['', Validators.required],
      phone: ['', Validators.required],
      email: ['', Validators.email]
    });
  }

  getEmailServiceStatus() {
    const request = new BaseRequest();
    this.tenantService.getEmailServiceStatus(request).subscribe(response => {
      console.log('Tenant email service', response);

      if (response.status.code !== 0) {
        return;
      }

      this.emailService = response.emailService === '1';
      if (!this.emailService) {
        this.signerForm.get('email').setValidators([Validators.required, Validators.email]);
      } else {
        this.signerForm.get('phone').setValidators([Validators.required]);
        this.signerForm.get('email').setValidators([Validators.email]);
      }

      this.signerForm.updateValueAndValidity();
      this.cdr.detectChanges();
    })
  }

  hasRequired(key: string) {
    const validator = this.signerForm.get(key).validator({} as AbstractControl);
    return !!(validator && validator.required);
  }

  checkSigner(): boolean {
    let signer: Signer;
    const rawData = this.signerForm.getRawValue();
    signer  = this.signers.find(x => x.phone === rawData?.phone);

    if (signer) {
      this.message = `Nomor Handphone ${rawData?.phone} sudah di gunakan oleh penanda tangan ${signer.name}, silahkan gunakan nomor lain!`;
      return false;
    }

    signer = this.signers.find(x => x.email === rawData?.email);
    if (signer) {
      this.message = `Email ${rawData?.email} sudah di gunakan oleh penanda tangan ${signer.name}, silahkan gunakan email lain!`;
      return false;
    }

    return true;
  }

  lookupUser(key: string) {
    const request = new AutofillUserRequest();
    request.checkRegisterDetail = this.signerForm.get(key).value;
    request.vendorCode = this.vendorCode;

    this.userService.autofilUser(request).subscribe(response => {
      console.log('Autofill User', response);
      if (response.status.code !== 0) {
        this.message = response.status.message;
        return;
      }

      this.signerForm.patchValue({
        name: response.fullName,
        phone: response.phoneNumUser,
        email: response.emailUser
      });
      this.signerForm.updateValueAndValidity();
    })
  }
}
