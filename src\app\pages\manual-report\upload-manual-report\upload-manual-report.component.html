<div class="row" style="margin-top: 15px">
    <div class="col-6">
        <div class="content-header" style="margin-top: 0 !important;" translate>Upload Manual Report</div>
    </div>
</div>

<div class="row match-height">
  <div class="col-{{colsize}}" >
    <div class="card">
      <div class="card-content">
        <div class="card-body">
          <form [formGroup]="msxForm" (ngSubmit)="onSubmit()" id="msxForm">
            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestionkey('tenantCode')" [direction]="formObj.direction"></app-select>
              </div>
            </div>

            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestionkey('reportType')" [direction]="formObj.direction"></app-select>
              </div>
            </div>

            <div class="row">
                <div class="col-{{formObj.colSize}}">
                  <app-date [form]="msxForm" [question]="getQuestionkey('reportDate')" [direction]="formObj.direction"></app-date>
                </div>
              </div>
  
            <!-- Upload document -->
            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-file [form]="msxForm" [direction]="formObj.direction" [question]="getQuestionkey('documentExample')" (change)="onInput($event)" (rawFile)="onBase64Data($event)"></app-file>
              </div>
            </div>

            <div class="row">
              <div class="col-12 text-center">
                <button class="btn btn-light mr-2" type="button" (click)="doBack()">{{'Cancel' | translate}}</button>
                <button class="btn btn-info" type="submit" [disabled]="msxForm.invalid">{{'Next' | translate}}</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>