import { Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { RoleManagement } from 'app/model/role-management';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDate, QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { AddEditRoleManagementComponent } from '../add-edit-role-management/add-edit-role-management.component';
import { Router } from '@angular/router';
import { MsxPagingComponent } from 'app/shared/components/msx-view/msx-paging.component';

@Component({
  selector: 'app-list-role-management',
  templateUrl: './list-role-management.component.html',
  styleUrls: ['./list-role-management.component.scss']
})
export class ListRoleManagementComponent implements OnInit {

  serviceGetListRoleManagement = URLConstant.getListRoleManagement
  view: MsxView;

  searchFormObj: FormModel<any> = new FormModel<any>();
  form: FormGroup;

  buttonList: Button[];

  @ViewChild(MsxPagingComponent) msxPagingComponent!: MsxPagingComponent;

  constructor(private global: GlobalService, private modalService: NgbModal, private router: Router) { }

  ngOnInit(): void {
    this.initView();
  }

  initView() {
    this.searchFormObj = {
      name: 'SearchForm',
      colSize: 6,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        {
          key: 'roleName',
          label: 'Role Name',
          placeholder: 'Type role name here',
          controlType: FormConstant.TYPE_TEXT
        },
        new QuestionDropdown({
          key: 'tenantCode',
          label: 'Tenant',
          placeholder: 'Select Tenant',
          serviceUrl: URLConstant.GetTenantList,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'tenantList',
            key: 'tenantCode',
            value: 'tenantName'
          },
          params: {
            status: '1'
          }
        }),
        new QuestionDropdown({
          key: 'status',
          label: 'Status',
          placeholder: 'Select Status',
          options: [
            { key: '', value: 'All' },
            { key: '1', value: 'Active' },
            { key: '0', value: 'Inactive' }
          ],
          value: ''
        })
      ],
      params: [
        {
          key: 'audit',
          controlType: FormConstant.TYPE_HIDDEN,
          value: {
            callerId: this.global.user.loginId
          }
        },
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        }
      ]
    }

    const RoleTable: Table<RoleManagement> = {
      name: 'roles',
      list: [],
      columns: [
        {
          type: ColumnType.Text,
          prop: 'roleName',
          label: 'Role Name',
          width: 50
        },
        {
          type: ColumnType.Text,
          prop: 'tenantName',
          label: 'Tenant',
          width: 120
        },
        {
          type: ColumnType.IsActive,
          prop: 'managable',
          label: 'Managable',
          width: 80
        },
        {
          type: ColumnType.IsActive,
          prop: 'status',
          label: 'Status',
          width: 80
        },
        {
          type: ColumnType.Action,
          label: 'Action',
          width: 100,
          action: [
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-edit',
              descr: 'Edit Role',
              type: Act.Edit
            },
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-settings',
              type: Act.Setting,
              descr: 'Manage Menu of Role'
            }
          ]
        }
      ]
    }

    this.view = {
      title: 'Role Management',
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: RoleTable
        }
      ]
    }

    this.buttonList = [
      {name: 'New', class: 'btn btn-primary', hide: false, icon: 'ft-plus'}
    ]
  }

  onFormListener(form: FormGroup) {
    this.form = form;
  }

  onBtnClickListener(event) {
      switch (event['name']) {
        case this.buttonList[0].name:
          const modal = this.modalService.open(AddEditRoleManagementComponent, { backdrop: 'static', keyboard: false })
          modal.componentInstance.mode = 'Add' 
          break;
        default:
          break;
      }
    }
  
    
    onItemClick(event) {
      const data = event['data'];
  
      switch (event['act']['type']) {
        case Act.Edit:
          const modal = this.modalService.open(AddEditRoleManagementComponent, { backdrop: 'static', keyboard: false })
          modal.componentInstance.mode = 'Edit'
          modal.componentInstance.roleCode = data.roleCode;
          modal.componentInstance.roleName = data.roleName;
          modal.componentInstance.status = data.status;
          modal.componentInstance.tenantCode = data.tenantCode;
          modal.componentInstance.result.subscribe(() => {
            this.msxPagingComponent.refreshSearch(); // Refresh data  
          });
        break;
        case Act.Setting:
          this.router.navigate([PathConstant.MANAGE_MENU_OF_ROLE], {state: data});
        break;
      }
    }

}
