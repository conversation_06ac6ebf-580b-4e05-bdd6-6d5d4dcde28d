import { Component,Input, OnInit } from '@angular/core';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import { GenerateInvitationLinkForExpiredCertRequest } from 'app/model/api/generateInvitationLinkForExpiredCertRequest';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { HttpClient } from '@angular/common/http';
import { GlobalService } from 'app/shared/data/global.service';

@Component({
  selector: 'app-certificate-expired-regenerate-inv-link',
  templateUrl: './certificate-expired-regenerate-inv-link.component.html',
  styleUrls: ['./certificate-expired-regenerate-inv-link.component.scss']
})
export class CertificateExpiredRegenerateInvLinkComponent implements OnInit {

  @Input() loginId: string;
  @Input() documentId: string;
  @Input() msg: string;
  @Input() isEmbed: boolean;
  link: any;
  url: string;

  constructor(public activeModal: NgbActiveModal, private http: HttpClient, private readonly global: GlobalService) { }

  ngOnInit(): void {
    // Sonarqube notes: currently intentionally empty, will only be filled when necessary 
  }

  dismiss() {
    this.activeModal.dismiss('0');
  }

  async generateRenewInvLink() {
    const request = new GenerateInvitationLinkForExpiredCertRequest();
    if (this.isEmbed) {
      this.url = URLConstant.generateInvitationLinkForExpiredCertEmbed;
      request.msg = this.msg;
      request.tenantCode = this.global.user.role.tenantCode;
      request.audit = {callerId: this.msg};
      console.log("embed");
    } else if (!this.isEmbed) {
      this.url = URLConstant.generateInvitationLinkForExpiredCertRequest;
      request.loginId = this.loginId;
      request.audit = {callerId: this.loginId};
      console.log("no embed");
    }
    request.documentId = this.documentId;
    await this.http.post(this.url, request).toPromise().then(
      (response) => {
        if (response["status"]["code"] === 0) {
          console.log(response["link"]);
          this.link = response["link"];
          if (this.link) {
          window.location.href = this.link;
          } else {
            console.log('gagal')
          }

        }
      }
    )

  }

}
