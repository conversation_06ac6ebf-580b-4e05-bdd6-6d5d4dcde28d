<div class="row match-height">
  <div class="col-12">
    <!--Statistics cards Starts-->
    <div class="row">
      <div class="col-xl-6 col-lg-6 col-md-6 col-12">
        <div class="card">
          <form [formGroup]="vendorForm">
            <select class="vendor-select font-large-1" (change)="changeVendor($event)" formControlName="vendorSelect" placeholder="Select Vendor">
              <option *ngFor="let vendor of vendors" [value]="vendor.code">
                {{vendor.name}}
              </option>
            </select>
          </form>
        </div>
      </div>
      <div *ngIf="waiting" class="col-xl-6 col-lg-6 col-md-6 col-12">
        <div class="card card-balance">
          <div class="card-content">
            <div class="card-body py-0">
              <div class="media">
                <div class="media-body text-left">
                  <h3 class="font-large-1 mb-0 text-balance">Loading data...</h3>
                </div>
                <div class="media-right text-right">
                  <h3 class="font-large-1 mb-0 text-balance">-</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div *ngFor="let balance of balances" class="col-xl-6 col-lg-6 col-md-6 col-12">
        <div class="card card-balance">
          <div class="card-content">
            <div class="card-body py-0">
              <div class="media">
                <div class="media-body text-left">
                  <h3 class="font-large-1 mb-0 text-balance" translate>{{balance.description}}</h3>
                </div>
                <div class="media-right text-right">
                  <h3 class="font-large-1 mb-0 text-balance">{{balance.currentBalance}}</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--Statistics cards Ends-->
  </div>
</div>

<app-msx-paging [container]="view" [serviceUrl]="serviceBalanceMutation" (export)="getBalanceMutationFile($event)" (form)="onFormListener($event)"></app-msx-paging>
<ngx-spinner style="visibility: hidden;"></ngx-spinner>
