<form [formGroup]="templateForm" (ngSubmit)="onSubmit()">
  <div class="modal-header">
    <h4 class="modal-title">Add Document</h4>
    <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="form-group row align-items-center">
      <div class="col-lg-4 col-4">
        <label for="code" class="col-form-label">Code</label>
      </div>
      <div class="col-lg-8 col-8">
        <input formControlName="documentTemplateCode" type="text" id="code" class="form-control" name="code" placeholder="Document Code" appAutofocus>
        <div *ngIf="templateForm.dirty && templateForm.get('documentTemplateCode').hasError('maxLength')"
             class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> Document Code tidak boleh lebih dari 10 karakter</div>
      </div>
    </div>

    <div class="form-group row align-items-center">
      <div class="col-lg-4 col-4">
        <label for="name" class="col-form-label">Name</label>
      </div>
      <div class="col-lg-8 col-8">
        <input formControlName="documentTemplateName" type="text" id="name" class="form-control" name="name" placeholder="Document Name">
      </div>
    </div>

    <div class="form-group row align-items-center">
      <div class="col-lg-4 col-4">
        <label for="desc" class="col-form-label">Description</label>
      </div>
      <div class="col-lg-8 col-8">
        <textarea formControlName="documentTemplateDescription" class="form-control" name="desc" id="desc" placeholder="Description"></textarea>
      </div>
    </div>

    <div class="form-group row align-items-center">
      <div class="col-lg-4 col-4">
        <label for="document" class="col-form-label">Document</label>
      </div>
      <div class="col-lg-8 col-8">
        <input formControlName="documentExample" type="file" id="document" class="form-control" name="document"
               placeholder="Document Template" (change)="onInput($event)">
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button *ngIf="!waiting" type="button" class="btn btn-secondary" (click)="activeModal.dismiss('Cross click')">Cancel</button>
    <button *ngIf="!waiting" type="submit" class="btn btn-primary" [disabled]="templateForm.invalid">Save</button>
    <div *ngIf="waiting" class="d-flex justify-content-center">
      <div class="spinner-border text-secondary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
  </div>
</form>
