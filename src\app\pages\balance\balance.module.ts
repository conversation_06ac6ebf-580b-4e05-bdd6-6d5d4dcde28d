import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { BalanceRoutingModule } from './balance-routing.module';
import { BalanceComponent } from './balance/balance.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {NgSelectModule} from '@ng-select/ng-select';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap';
import {PipeModule} from '../../shared/pipes/pipe.module';
import {NgxDatatableModule} from '@swimlane/ngx-datatable';
import {SharedModule} from '../../shared/shared.module';
import {PdfJsViewerModule} from 'ngx-pdfjs-viewer';
import {MsFormModule} from '../../shared/components/ms-form/ms-form.module';
import { NgxSpinnerModule } from 'ngx-spinner';
import { AddBalanceComponent } from './add-balance/add-balance.component';

@NgModule({
  declarations: [BalanceComponent, AddBalanceComponent],
  imports: [
    CommonModule,
    BalanceRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgbModule,
    PipeModule,
    NgxDatatableModule,
    SharedModule,
    PdfJsViewerModule,
    MsFormModule,
    NgxSpinnerModule
  ],
  entryComponents: [
    BalanceComponent
  ]
})
export class BalanceModule { }
