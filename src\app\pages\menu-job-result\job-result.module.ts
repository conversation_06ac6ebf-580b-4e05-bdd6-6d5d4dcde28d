import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import {SharedModule} from '../../shared/shared.module';
import {MsFormModule} from '../../shared/components/ms-form/ms-form.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { PipeModule } from 'app/shared/pipes/pipe.module';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { PdfJsViewerModule } from 'ngx-pdfjs-viewer';
import { NgxSpinnerModule } from 'ngx-spinner';
import { JobResultRoutingModule } from './job-result-routing.module';
import { JobResultComponent } from './job-result/job-result.component';
import { ViewRequestParamComponent } from './view-request-param/view-request-param.component';

@NgModule({
  declarations: [JobResultComponent, ViewRequestParamComponent,],
  imports: [
    JobResultRoutingModule,
    CommonModule,
    SharedModule,
    MsFormModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgbModule,
    PipeModule,
    NgxDatatableModule,
    SharedModule,
    PdfJsViewerModule,
    MsFormModule,
    NgxSpinnerModule
  ]
})
export class JobResultModule { }