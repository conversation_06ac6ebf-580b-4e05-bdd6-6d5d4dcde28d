<div class="row" style="margin-top: 15px">
    <div class="col-6">
      <div class="content-header" style="margin-top: 0 !important;">Menus of Role {{roleName}}</div>
    </div>
</div>

<div class="row match-height">
    <div class="col-6">
        <div class="card">
            <div class="card-content">
                <div class="card-body">
                    <table class="table table-sm table-border center">
                        <tr *ngFor="let menu of menus">
                            <th class="header">{{menu.prompt}}</th>
                            <th>
                                <input class="form-check-input" type="checkbox" value="" id="{{menu.code}}" [checked]="isChecked(menu.code)">
                            </th>
                        </tr>
                    </table>

                    <div class="row">
                        <div class="col-12 text-center">
                            <button class="btn btn-light mr-2" (click)="goBack()" translate>Cancel</button>
                            <button class="btn btn-info" (click)="save()" translate>Save</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<ngx-spinner style="visibility: hidden;"></ngx-spinner>