<div *ngIf="!isMobile" class="row" style="margin-top: 15px">
  <div class="col-6">
    <div class="content-header" style="margin-top: 0 !important;" translate>Bulk Sign Document</div>
  </div>
  <div class="col-6 text-right">
    <a class="btn btn-secondary mr-2" (click)="onClickBack()" translate>Cancel</a>
    <button class="btn btn-primary" (click)="signAll()" [disabled]="!canSign" translate>Sign All</button>
  </div>
</div>

<div *ngIf="isMobile">
  <div class="row" style="margin-top: 15px">
    <div class="col-12 text-left">
      <div class="content-header" style="margin-top: 0 !important;" translate>Bulk Sign Document</div>
    </div>
  </div>
  <div class="row">
    <div class="col-12 text-right">
      <a class="btn btn-secondary mr-2" (click)="onClickBack()" translate>Cancel</a>
      <button class="btn btn-primary" (click)="signAll()" [disabled]="!canSign" translate>Sign All</button>
    </div>
  </div>
</div>

<div class="row match-height mt-3">
  <div class="col-12 tab-style tab-text">
    <ul class="nav nav-pills" ngbNav #nav="ngbNav" [(activeId)]="active">
      <ng-container *ngFor="let sign of signProcess" >
        <ng-container *ngFor="let index of documents" >
          <li class="nav-item" *ngIf="index.documentId == sign.documentId && sign.signingProcess == '0'" [ngbNavItem]="documents.indexOf(index) + 1">
            <a [ngClass]="{read: index.read}" ngbNavLink (click)="viewPdf(index.documentId)">{{ index.docTemplateName }} <i *ngIf="index.read" class="ft-check" style="color: #0fe14e;"></i></a>
            <ng-template ngbNavContent></ng-template>
          </li>
        </ng-container>
      </ng-container>
    </ul>
    <div>
      <pdf-viewer
      class="pdfViewer"
      id="pdfViewer"
      [src]="pdfBuffer"
      [rotation]="0"
      [original-size]="false"
      [show-all]="true"
      [fit-to-page]="false"
      [zoom]="1"
      [zoom-scale]="'page-width'"
      [stick-to-page]="false"
      [render-text]="true"
      [external-link-target]="'blank'"
      [autoresize]="true"
      [show-borders]="true"
      (after-load-complete)="onLoadComplete($event)"
      [(page)]="pdfPageNumber"
      (pageChange)="pageChange($event)"
    ></pdf-viewer>
    </div>
    <div [ngbNavOutlet]="nav" class="mt-1"></div>
  </div>
</div>
<!--<ngx-spinner style="visibility: hidden"></ngx-spinner>-->
