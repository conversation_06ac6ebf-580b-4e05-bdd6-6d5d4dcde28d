<form [formGroup]="signerForm" (ngSubmit)="onSubmit()">
  <div class="modal-header">
    <h4 class="modal-title"><PERSON>bah Penanda Tangan</h4>
    <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <ngb-alert type="danger" [dismissible]="false" *ngIf="message">{{message}}</ngb-alert>

    <div class="form-group row align-items-center" *ngIf="emailService">
      <div class="col-lg-4 col-4">
        <label for="phone" class="col-form-label" translate>Phone <span *ngIf="hasRequired('phone')" class="text-danger">*</span></label>
      </div>
      <div class="col-lg-8 col-8">
        <div class="input-group">
          <input type="tel" formControlName="phone" class="form-control" id="phone" placeholder="Fill signer phone here" />
          <div class="input-group-append">
            <span class="input-group-text">
              <i style="cursor:pointer;" class="ft-search" (click)="lookupUser('phone')"></i>
            </span>
          </div>
        </div>
        <div *ngIf="signerForm.get('phone').touched && signerForm.get('phone').hasError('required')"
             class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> Telp penanda tangan harus diisi!</div>
      </div>
    </div>

    <div class="form-group row align-items-center">
      <div class="col-lg-4 col-4">
        <label for="email" class="col-form-label" translate>Email <span *ngIf="hasRequired('email')" class="text-danger">*</span></label>
      </div>
      <div class="col-lg-8 col-8">
        <div class="input-group">
          <input type="email" formControlName="email" class="form-control" id="email" placeholder="Fill signer email here" />
          <div class="input-group-append">
            <span class="input-group-text">
              <i style="cursor:pointer;" class="ft-search" (click)="lookupUser('email')"></i>
            </span>
          </div>
        </div>

        <div *ngIf="signerForm.get('email').touched && signerForm.get('email').hasError('required')"
             class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> Email penanda tangan harus diisi!</div>
        <div *ngIf="signerForm.get('email').touched && signerForm.get('email').hasError('email')"
             class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> Format email tidak sesuai!</div>
      </div>
    </div>

    <div class="form-group row align-items-center">
      <div class="col-lg-4 col-4">
        <label for="name" class="col-form-label" translate>Name <span class="text-danger">*</span></label>
      </div>
      <div class="col-lg-8 col-8">
        <input formControlName="name" type="text" id="name" class="form-control" placeholder="Fill signer name here" appAutofocus />
        <div *ngIf="signerForm.get('name').touched && signerForm.get('name').hasError('required')"
             class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> Nama penanda tangan harus diisi!</div>
      </div>
    </div>

  </div>
  <div class="modal-footer">
    <button *ngIf="!waiting" type="button" class="btn btn-light" (click)="activeModal.dismiss('Cross click')">Cancel</button>
    <button *ngIf="!waiting" type="submit" class="btn btn-primary" [disabled]="!signerForm.valid">Save</button>
    <div *ngIf="waiting" class="d-flex justify-content-center">
      <div class="spinner-border text-secondary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
  </div>
</form>
