import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {InquiryComponent} from './inquiry.component';
import {InquiryDetailComponent} from './inquiry-detail/inquiry-detail.component';
import {ViewDocumentComponent} from '../master/view-document/view-document.component';
import {SignDocumentComponent} from './sign-document/sign-document.component';
import {BulkSignComponent} from './bulk-sign/bulk-sign.component';
import {BulkSignDigisignComponent} from './bulk-sign-digisign/bulk-sign-digisign.component';
import { ViewDocumentInquiryComponent } from './view-document-inquiry/view-document-inquiry.component';
import { PathConstant } from 'app/shared/constant/PathConstant';
import {InvitationRegisterComponent} from './invitation-register/invitation-register.component';
import { BulkSignEsignComponent } from './bulk-sign-esign/bulk-sign-esign.component';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { InquiryImportBmComponent } from './import-bm/inquiry-import-bm/inquiry-import-bm.component';
import { ImportBmExcelComponent } from './import-bm/import-bm-excel/import-bm-excel.component';

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: '',
        component: InquiryComponent,
        data: {
          title: 'Inquiry'
        }
      },
      {
        path: PathConstant.VIEW,
        component: ViewDocumentInquiryComponent,
        data: {
          title: 'View Document'
        }
      },
      {
        path: 'detail',
        children: [
          {
            path: '',
            component: InquiryDetailComponent,
            data: {
              title: CommonConstant.TITLE_INQUIRY_DOCUMENT_DETAIL
            },
          },
          {
            path: 'document',
            component: ViewDocumentComponent,
            data: {
              title: 'View Document'
            },
          },
          {
            path: 'sign',
            component: SignDocumentComponent,
            data: {
              title: 'Sign Document'
            },
          },
          {
            path: 'bulkSign',
            children: [
              {
                path: '',
                component: BulkSignComponent,
                data: {
                  title: 'Bulk Sign Document'
                }
              },
              {
                path: 'digisign',
                component: BulkSignDigisignComponent,
                data: {
                  title: 'Bulk Sign Digisign Document'
                }
              },
              {
                path: 'esign',
                component: BulkSignEsignComponent,
                data: {
                  title: 'Bulk Sign Esign Document'
                }
              }
            ],
          }

        ]
      },
      {
        path: PathConstant.INVITATION_REGISTER, // /inquiry/invitation-register
        component: InvitationRegisterComponent,
        data: {
          title: 'Inquiry Invitation Register'
        }
      }
    ]
  },
  {
    path: '',
    children: [
      {
        path: 'insert-excel-bm-autosign',
        component: ImportBmExcelComponent,
        data: {
          title: 'Import Excel Autosign BM'
        }
      }
    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class InquiryRoutingModule { }
