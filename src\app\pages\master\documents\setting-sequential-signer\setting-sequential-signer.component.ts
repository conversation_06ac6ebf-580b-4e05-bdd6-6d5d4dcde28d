import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {CdkDragDrop, moveItemInArray} from '@angular/cdk/drag-drop';
import { arrayLength } from 'ngx-custom-validators/src/app/array-length/validator';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Location } from '@angular/common';
import { AddDocumentTemplateRequest } from 'app/shared/dto/document-template/add-document-template.request';
import { GlobalService } from 'app/shared/data/global.service';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { Signer } from 'app/shared/components/document-anotate/model/signer';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { HttpClient } from '@angular/common/http';
import { Success } from 'app/shared/data/sweet-alerts';


@Component({
  selector: 'app-setting-sequential-signer',
  templateUrl: './setting-sequential-signer.component.html',
  styleUrls: ['./setting-sequential-signer.component.scss']
})
export class SettingSequentialSignerComponent implements OnInit {
  public state: any;
  public result: any;
  seqsigns: string[] = [];
  public signer: Signer[];
  public seqmaterai: number


  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.seqsigns, event.previousIndex, event.currentIndex);
  }

  constructor(private router: Router, private location: Location,private global: GlobalService,private http: HttpClient,) {
    this.state = this.router.getCurrentNavigation().extras;
    if (this.state && this.state.state.ttd && Array.isArray(this.state.state.ttd)) {
      const arrayLength = this.state.state.ttd.length;
      for (let i = 0; i < arrayLength; i++) {
        const dynamicData = this.state.state.ttd[i];
        if (dynamicData && dynamicData.label && dynamicData.label !== "Meterai" && !this.seqsigns.includes(dynamicData.label)) {
          this.seqsigns.push(dynamicData.label);
        }
      }
    }
  }

  ngOnInit(): void {
    console.log('data state', this.state)

    console.log('total', this.state.state.ttd)
  }

  doBack(){
    this.location.back();
  }

  onSubmit(){
  const documentBase64 = this.state.state.data['rawTemplate'];
    const request: AddDocumentTemplateRequest = new AddDocumentTemplateRequest();
    request.documentExample = documentBase64.replace(CommonConstant.DATA_APPLICATION_PDF_BASE64, '');
    request.documentTemplateCode = this.state.state.data['documentTemplateCode'];
    request.documentTemplateName = this.state.state.data['documentTemplateName'];
    request.documentTemplateDescription = this.state.state.data['documentTemplateDescription'];
    request.isActive = this.state.state.data['isActive'];
    request.isSequence = this.state.state.data['isSequence'];
    request.numberOfPage = this.state.state.page;
    request.paymentSignTypeCode = this.state.state.data['paymentSignTypeCode'];
    request.useSignQr = this.state.state.data['useSignQr'];
    request.prioritySequence = this.state.state.data['prioritySequence'] ? Number(this.state.state.data['prioritySequence']) : 0;
    
    request.tenantCode = this.global.user.roles[0].tenantCode;
    request.vendorCode = this.state.state.data['vendorCode'];

    if (this.state.state.mode === CommonConstant.MODE_SIGNER) {
      request.isSignLocOnly = '1';
    } else {
      request.isSignLocOnly = '0';
    }

    request.signer = [];
    const usedLabels = new Map<string, boolean>();
    

    this.seqsigns.forEach((seqsign, index) => {
      if(seqsign !== 'Meterai') {      
      const matchingSigners = this.state.state.ttd.filter((data: any) => data.label === seqsign);
      if (matchingSigners.length > 0) {
        matchingSigners.forEach((signerData) => {
          const signer: Signer = new Signer(signerData);
          const seqNo = (index + 1).toString(); // Mengupdate seqNo dengan urutan indeks dari 1
          signer.seqNo = seqNo;
          this.seqmaterai = (index + 2)
          request.signer.push(this.validateSigner(signer));
          usedLabels.set(signer.signerTypeCode, true);
        });
      }
    }
    });
    const meteraiSigners = this.state.state.ttd.filter((data: any) => data.label === 'Meterai');
    if (meteraiSigners.length > 0) {
      meteraiSigners.forEach((signerData) => {
        const signer: Signer = new Signer(signerData);
        signer.seqNo = (this.seqmaterai++).toString();
        request.signer.push(this.validateSigner(signer));
      });
    }

    console.log('Request Add/Update Document', request);
    const serviceUrl = this.state.state.mode === CommonConstant.MODE_ADD ? URLConstant.AddTemplate : URLConstant.UpdateTemplate;
    this.http.post(serviceUrl, request).subscribe(response => {
      if (response['status']['code'] !== 0) {
        console.log('Error', response);
        return;
      }
      return Success('Document Template berhasil di simpan').then(() => {
        this.router.navigate([PathConstant.LIST_DOCUMENT_TEMPLATE], {state: {msg: 'Document Template berhasil di simpan.'}})
      });      
    });
  }

  /**
   * Patch Lokasi ttd minus
   * @param mSginer
   */
  validateSigner(mSginer: Signer) {
    if (!this.signer) {
      return mSginer;
    }

    console.log('validate signer');
    if (mSginer.signTypeCode === 'SDT') {
      this.validateSdt(mSginer);
    } else {
      const check = this.signer.find(x => x.signerTypeCode === mSginer.signerTypeCode &&
        x.signPage === mSginer.signPage);

      if (!check) {
        return mSginer;
      }

      if (mSginer.signLocation.llx < 0 && mSginer.signLocation.urx < 0) {
        return check;
      } else {
        return mSginer;
      }
    }
  }

  validateSdt(mSginer: Signer) {
    const sdt = this.signer.find(x => x.signTypeCode === mSginer.signTypeCode &&
      x.signPage === mSginer.signPage);

    if (!sdt) {
      return mSginer;
    }

    if (mSginer.signLocation.llx < 0 && mSginer.signLocation.urx < 0) {
      return sdt;
    } else {
      return mSginer;
    }
  }
}
