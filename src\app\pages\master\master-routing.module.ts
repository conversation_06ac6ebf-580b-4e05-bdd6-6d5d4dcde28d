import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import {DocumentsComponent} from './documents/documents.component';
import {SettingSignerComponent} from './documents/setting-signer/setting-signer.component';
import {AddDocumentTemplateComponent} from './documents/add-document-template/add-document-template.component';
import {ViewDocumentComponent} from './view-document/view-document.component';
import { StampDutyComponent } from './stamp-duty/stamp-duty.component';
import { TopupStampDutyComponent } from './stamp-duty/topup-stamp-duty/topup-stamp-duty.component';
import { ReversalTopupComponent } from './stamp-duty/reversal-topup/reversal-topup.component';
import {ManualUploadComponent} from './documents/manual-upload/manual-upload.component';
import { SettingSequentialSignerComponent } from './documents/setting-sequential-signer/setting-sequential-signer.component';
import { ManualStampComponent } from './documents/manual-stamp/manual-stamp.component';
import { SettingManualSequentialComponent } from './documents/setting-manual-sequential/setting-manual-sequential.component';

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: 'documents',
        component: DocumentsComponent,
        data: {
          title: 'Documents'
        }
      },
      {
        path: 'documents/new',
        component: AddDocumentTemplateComponent,
        data: {
          title: 'Add Document Template',
          mode: 'Add'
        }
      },
      {
        path: 'documents/edit',
        component: AddDocumentTemplateComponent,
        data: {
          title: 'Edit Document Template',
          mode: 'Edit'
        }
      },
      {
        path: 'documents/setting',
        component: SettingSignerComponent,
        data: {
          title: 'Setting Signer'
        }
      },
      {
        path: 'documents/manual-upload',
        component: ManualUploadComponent,
        data: {
          title: 'Manual Upload Signer'
        }
      },
      {
        path: 'documents/view',
        component: ViewDocumentComponent,
        data: {
          title: 'View Document Template'
        },
      },
      {
        path: 'stampduty',
        component: StampDutyComponent,
        data: {
          title: 'Stamp Duty'
        }
      },
      {
        path: 'stampduty/topup',
        component: TopupStampDutyComponent,
        data: {
          title: 'Topup Stamp Duty'
        }
      },
      {
        path: 'stampduty/reversalTopup',
        component: ReversalTopupComponent,
        data: {
          title: 'Reversal Topup Stamp Duty'
        }
      },
      {
        path: 'documents/setting/sequential',
        component: SettingSequentialSignerComponent,
        data: {
          title: 'Setting Sequential Signer'
        }
      },
      {
        path: 'documents/manual-stamp',
        component: ManualStampComponent,
        data: {
          title: 'Manual Stamping e-meterai'
        }
      },
      {
        path: 'documents/manual-stamp/setting',
        component: SettingSignerComponent,
        data: {
          title: 'Setting e-Meterai'
        }
      },
      {
        path: 'documents/manual-upload/setting-sequential',
        component: SettingManualSequentialComponent,
        data: {
          title: 'Setting Manual upload Sequential Signer'
        }
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MasterRoutingModule { }
