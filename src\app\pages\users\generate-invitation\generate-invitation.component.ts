import {<PERSON>mpo<PERSON>, <PERSON>ement<PERSON>ef, OnInit, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup, ValidationErrors, Validators} from '@angular/forms';
import {InquiryInvitation} from '../../../model/inquiry-invitation';
import {invitationBy} from '../../../model/invitationBy';
import {Location} from '@angular/common';
import {Router} from '@angular/router';
import {VendorListRequest} from '../../../model/api/vendor-list.request';
import {URLConstant} from '../../../shared/constant/URLConstant';
import {VendorListResponse} from '../../../shared/dto/data/vendor-list.response';
import {CommonConstant} from '../../../shared/constant/common.constant';
import {PathConstant} from '../../../shared/constant/PathConstant';
import {DataService} from '../../../services/api/data.service';
import {UserService} from '../../../services/api/user.service';
import {GenerateInvitationRequest} from '../../../model/api/generate.invitation.request';
import {GenerateInvitation} from '../../../model/generate-invitation';
import {GenerateInvitationResponse} from '../../../model/api/generate.invitation.response';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {MsxAlertComponent} from '../../../shared/components/msx-alert/msx-alert.component';
import {Confirm} from '../../../shared/data/sweet-alerts';
import {ViewInvitationLinkComponent} from '../../inquiry/invitation-register/modal/view-invitation-link/view-invitation-link.component';
import { GlobalService } from 'app/shared/data/global.service';
import { BusinessLine } from 'app/model/business-line';
import { Region } from 'app/model/region';
import { Office } from 'app/model/office';
import { IpService } from 'app/services/api/ip.service';
import { GetLovRequest } from 'app/model/api/get.lov.request';

@Component({
  selector: 'app-generate-invitation',
  templateUrl: './generate-invitation.component.html',
  styleUrls: ['./generate-invitation.component.scss']
})
export class GenerateInvitationComponent implements OnInit {

  @ViewChild('formElement') formElement: ElementRef;

  registerForm: FormGroup;
  fields: any;
  validationMessages: any;
  template: any;
  state: InquiryInvitation;
  selectedItem: any = '';
  InviteBy: invitationBy[];
  psre = [];
  selectedPsre: any;
  result: GenerateInvitationResponse;

  // Attributes for dropdown
  businessLineList: BusinessLine[] = [];
  regionList: Region[] = [];
  officeList: Office[] = [];
  selectedBusinessLineCode: string;
  selectedRegionCode: string;
  selectedOfficeCode: string;
  roleList: { code: string, description: string }[] = [];

  messages  = [];
  keys      = [];

  ipAddress;

  constructor(private formBuilder: FormBuilder, private location: Location, private router: Router,
              private dataService: DataService, private userService: UserService, private modalService: NgbModal,
              private ipService: IpService, private global: GlobalService) { }

  async ngOnInit() {
    await this.getVendorList();
    this.initView();
    await this.getIdAddress();
    await this.getRoleList();
  }

  async getIdAddress(){
    await this.ipService.getIpAddress().toPromise().then(ipResponse =>{
      this.ipAddress = ipResponse.ip;
    })
  }

  initView() {
    this.setProperties();
    this.registerForm = this.formBuilder.group({
      name: ['', Validators.compose([Validators.required, Validators.maxLength(50)])],
      nik: ['', Validators.compose([Validators.required, Validators.minLength(16), Validators.maxLength(16), Validators.pattern('\\d*')])],
      pob: ['', Validators.compose([Validators.maxLength(50)])],
      dob: ['', Validators.compose([Validators.maxLength(10)])],
      gender: [''],
      phone: ['', Validators.compose([Validators.required, Validators.maxLength(15)])],
      address: [''],
      subDistrict: ['', Validators.compose([Validators.maxLength(50)])],
      district: ['', Validators.compose([Validators.maxLength(50)])],
      city: ['', Validators.compose([Validators.maxLength(50)])],
      province: ['', Validators.compose([Validators.maxLength(50)])],
      zip: ['', Validators.compose([Validators.maxLength(5), Validators.pattern('\\d*')])],
      email: ['', Validators.compose([
        Validators.pattern('[\\w\\-\\._]+@[\\w\\-\\._]+\\.\\w{2,10}'),
        Validators.maxLength(64)]
      )],
      region: [''],
      office: [''],
      businessLine: [''],
      refNumber: [''],
      vendorCode: [''],
      roleCode: [null, Validators.compose([Validators.required])]
    });

    this.getBusinessLineList();
    this.getRegionList();
  }

  async getVendorList() {
    const url =URLConstant.GetVendorList;
    const request = new VendorListRequest();
    request.vendorTypeCode = CommonConstant.VENDOR_TYPE_PSRE;
    let resp: VendorListResponse = new VendorListResponse();


    await this.dataService.getVendorList(request, url).toPromise().then(
      (response) => {
        resp = response;
        console.log('Vendor resp', resp);
        if (response.status.code === 0 && response.documentId) {
          this.router.navigate([PathConstant.SIGNATURE], {queryParams: {
              id: response.documentId
            }});
        }

        if (response.status.code === 0) {
          response.vendorList.forEach(vendor => {
            this.psre = [...this.psre, { id: vendor.code, name: vendor.name }];
          });

          this.selectedPsre = this.psre[0];
          console.log('selectedProvider', this.selectedPsre);
        }
      }
    );

    return resp;
  }

  private setProperties() {
    // Message Template
    this.template = {
      required: '{x} harus diisi',
      maxlength: 'Maksimal jumlah karakter {x} adalah {v}',
      minlength: 'Minimal jumlah karakter {x} adalah {v}',
      pattern: 'Silahkan isi {x} dengan format yang benar.',
    };

    this.fields = {
      name: {
        label: 'Nama Lengkap',
        prop: {
          maxlength: 50
        }
      },
      nik: {
        label: 'NIK',
        prop: {
          maxlength: 16,
          minlength: 16,
        }
      },
      pob: {
        label: 'Tempat Lahir',
        prop: {
          maxlength: 50
        }
      },
      dob: {
        label: 'Tanggal Lahir'
      },
      gender: {
        label: 'Jenis Kelamin'
      },
      phone: {
        label: 'No. Telp',
        prop: {
          maxlength: 15
        }
      },
      address: {
        label: 'Alamat',
        prop: {
          maxlength: 150
        }
      },
      subDistrict: {
        label: 'Kelurahan',
        prop: {
          maxlength: 50
        }
      },
      district: {
        label: 'Kecamatan',
        prop: {
          maxlength: 50
        }
      },
      city: {
        label: 'Kota',
        prop: {
          maxlength: 50
        }
      },
      province: {
        label: 'Provinsi',
        prop: {
          maxlength: 50
        }
      },
      zip: {
        label: 'Kode Pos',
        prop: {
          maxlength: 5
        }
      },
      fotoSelfie: {
        label: 'Foto Diri'
      },
      fotoKtp: {
        label: 'Foto KTP'
      },
      email: {
        label: 'Email',
        prop: {
          maxlength: 64
        }
      },
    };

    // Validation Custom Error Messages
    this.validationMessages = {
      name: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.name.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.name.label)
            .replace('{v}', this.fields.name.prop.maxlength)
        },
      ],
      nik: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.nik.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.nik.label)
            .replace('{v}', this.fields.nik.prop.maxlength)
        },
        {
          type: 'minlength', message: this.template.minlength.replace('{x}', this.fields.nik.label)
            .replace('{v}', this.fields.nik.prop.minlength)
        },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.nik.label) },
      ],
      pob: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.pob.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.pob.label)
            .replace('{v}', this.fields.pob.prop.maxlength)
        },
      ],
      dob: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.dob.label) }
      ],
      gender: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.gender.label) }
      ],
      phone: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.phone.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.phone.label)
            .replace('{v}', this.fields.phone.prop.maxlength)
        },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.phone.label) },
      ],
      address: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.address.label) }
      ],
      subDistrict: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.subDistrict.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.subDistrict.label)
            .replace('{v}', this.fields.subDistrict.prop.maxlength)
        },
      ],
      district: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.district.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.district.label)
            .replace('{v}', this.fields.district.prop.maxlength)
        },
      ],
      city: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.city.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.city.label)
            .replace('{v}', this.fields.city.prop.maxlength)
        },
      ],
      province: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.province.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.province.label)
            .replace('{v}', this.fields.province.prop.maxlength)
        },
      ],
      zip: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.zip.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.zip.label)
            .replace('{v}', this.fields.zip.prop.maxlength)
        },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.zip.label) },
      ],
      psre: [
        { type: 'required', message: this.template.required.replace('{x}', 'PSRe') }
      ],
      fotoSelfie: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.fotoSelfie.label) }
      ],
      fotoKtp: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.fotoKtp.label) }
      ],
      email: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.email.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.email.label)
            .replace('{v}', this.fields.email.prop.maxlength)
        },
        { type: 'pattern', message: 'Format email invalid!'}
      ],
      roleCode: [
        { type: 'required', message: this.template.required.replace('{x}', 'Role') }
      ]
    };
  }

  get rf() {
    return this.registerForm.controls;
  }

  async save() {
    console.log('Form Data', this.registerForm.getRawValue());
    if (this.registerForm.invalid) {
      this.getFormValidationErrors();
      const modal = this.modalService.open(MsxAlertComponent, { backdrop: 'static', keyboard: false, size: 'sm' });
      modal.componentInstance.title = 'Validation Error';
      modal.componentInstance.listMessages = this.messages;
      modal.componentInstance.hideBtn = false;
      return;
    }

    const confirm = await Confirm('Apakah data sudah sesuai dan buat link undangan?');
    if (confirm?.isConfirmed === false) {
      return;
    }

    const rawData  = this.registerForm.getRawValue();
    const request: GenerateInvitationRequest = new GenerateInvitationRequest();
    request.users = [];
    request.businessLineCode = this.selectedBusinessLineCode;
    request.regionCode = this.selectedRegionCode;
    request.officeCode = this.selectedOfficeCode;
    request.referenceNo = rawData['refNumber'];
    request.ipAddress = this.ipAddress;
    request.psreCode = rawData['vendorCode'].id;
    request.roleType = rawData['roleCode'];

    const userData = new GenerateInvitation();
    userData.email = rawData['email'];
    userData.tlp   = rawData['phone'];
    userData.jenisKelamin = rawData['gender'];
    userData.tmpLahir = rawData['pob'];
    userData.tglLahir = rawData['dob'];
    userData.idKtp = rawData['nik'];
    userData.provinsi = rawData['province'];
    userData.kota = rawData['city'];
    userData.kecamatan = rawData['district'];
    userData.kelurahan = rawData['subDistrict'];
    userData.kodePos = rawData['zip'];
    userData.alamat = rawData['address'];
    userData.selfPhoto = '';
    userData.idPhoto = '';
    userData.nama = rawData['name'];

    console.log('Generate Invitation Data', userData);
    request.users.push(userData);

    this.userService.generateInvitation(request).subscribe(res => {
      if (res.status.code === 0) {
        this.result = res;
        const modal = this.modalService.open(ViewInvitationLinkComponent, { size: 'md', backdrop: 'static', keyboard: false });
        modal.componentInstance.link = this.result.links[0];
        this.registerForm.reset();
      }
    })
  }

  getFormValidationErrors() {
    console.log('%c ==>> Validation Errors: ', 'color: red; font-weight: bold; font-size:25px;');
    this.messages = [];
    this.keys = [];
    return Object.keys(this.registerForm.controls).forEach(async key => {
      const controlErrors: ValidationErrors = this.registerForm.get(key).errors;
      if (controlErrors != null) {
        Object.keys(controlErrors).forEach(keyError => {
          const msg = this.validationMessages[key].find(x => x.type === keyError);
          this.registerForm.get(key).markAsDirty();
          this.messages.push(msg.message);
          this.keys.push(key);
          console.log('msg', this.messages);
          console.log('Key control: ' + key + ', keyError: ' + keyError + ', err value: ', controlErrors[keyError]);
        });

        this.setFocus(this.keys[0]);
      }
    });
  }

  setFocus(name) {
    const ele = this.formElement.nativeElement[name];
    if (ele) {
      ele.focus();
    }
  }

  onCancel() {
    Confirm('Apa anda yakin ingin membatalkan input link undangan?', null, null, null, 'Lanjutkan', 'Ya')
      .then(value => {
        if (value?.isConfirmed) {
          this.location.back();
        }
      })
  }

  getBusinessLineList() {
    this.dataService.getBusinessLineList(this.global.user.role.tenantCode).subscribe(response => {
      this.businessLineList = response.businessLineList;
    });
  }

  getRegionList() {
    this.dataService.getRegionList(this.global.user.role.tenantCode).subscribe(response => {
      this.regionList = response.regionList;
    });
  }

  getOfficeList() {
    this.dataService.getOfficeList(this.selectedRegionCode, this.global.user.role.tenantCode).subscribe(response => {
      this.officeList = response.officeList;
    });
  }

  getRoleList() {
    const lovRequest = new GetLovRequest();
    lovRequest.lovGroup = 'USER_TYPE';
    lovRequest.audit.callerId = this.global.user?.loginId;
    this.dataService.getLov(lovRequest).subscribe((response) => {
      this.roleList = response.lovList?.map(item => ({
        code: item.code,
        description: item.description
      })) || [];
      if (this.roleList.length > 0) {
        this.registerForm.patchValue({ roleCode: this.roleList[0].code });
      }
    });
  }

  onChange(property: string) {
    // Setiap value region berubah, reset dropdown office
    if (property === 'region') {
      this.selectedOfficeCode = null;
      this.officeList = [];

      // Pengecekan ini dilakukan supaya tidak get all office jika value region berubah ke null
      if (this.selectedRegionCode) {
        this.getOfficeList();
      }
    }
  }

}
