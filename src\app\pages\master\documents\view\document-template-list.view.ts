import {<PERSON><PERSON><PERSON><PERSON><PERSON>} from '../../../../shared/components/msx-view/models/MsxView';
import {WidgetType} from '../../../../shared/components/msx-view/models/WidgetType';
import {FormConstant} from '../../../../shared/components/ms-form/constants/form.constant';
import {ColumnType} from '../../../../shared/components/msx-datatable/enums/column-type';
import {Table} from '../../../../shared/components/msx-datatable/models/table';
import {DocumentTemplate} from '../../../../model/template';
import {FormModel} from '../../../../shared/components/ms-form/models';
import {Act} from '../../../../shared/components/msx-datatable/enums/act';
import { CommonConstant } from 'app/shared/constant/common.constant';

const DocumentSearchFilter: FormModel<string> = {
  name: 'documentTemplateSearchForm',
  direction: FormConstant.DIRECTION_HORIZONTAL,
  autoload: true,
  colSize: 6,
  components: [
    {
      key: 'documentTemplateCode',
      label: 'Document Template Code',
      placeholder: 'Type document template code here',
      controlType: FormConstant.TYPE_TEXT,
      value: ''
    },
    {
      key: 'documentTemplateName',
      label: 'Document Template Name',
      placeholder: 'Type document template name here',
      controlType: FormConstant.TYPE_TEXT,
      value: ''
    },
    {
      key: 'isActive',
      label: 'Status',
      controlType: FormConstant.TYPE_DROPDOWN,
      placeholder: 'Select Status',
      options: [
        {key: '', value: 'All'},
        {key: '1', value: 'Active'},
        {key: '0', value: 'Inactive'}
      ],
      value: ''
    }
  ],
  params: [
    {
      key: 'page',
      controlType: FormConstant.TYPE_HIDDEN,
      value: 1
    }
  ]
};

const DocumentTemplateTable: Table<DocumentTemplate> = {
  name: 'listDocumentTemplate',
  list: [],
  columns: [
    {
      type: ColumnType.Text,
      prop: 'documentTemplateCode',
      label: 'Code',
      width: 160
    },
    {
      type: ColumnType.Text,
      prop: 'documentTemplateName',
      label: 'Name',
      width: 130
    },
    {
      type: ColumnType.Text,
      prop: 'documentTemplateDescription',
      label: 'Description',
      width: 200
    },
    {
      type: ColumnType.Text,
      prop: 'numberOfSign',
      label: 'Sign',
      width: 50
    },
    {
      type: ColumnType.Text,
      prop: 'numberOfInitial',
      label: 'Initial',
      width: 50
    },
    {
      type: ColumnType.Text,
      prop: 'numberOfStampDuty',
      label: 'Stamp Duty',
      width: 90
    },
    {
      type: ColumnType.Text,
      prop: 'paymentSignTypeDescription',
      label: 'Payment Sign Type',
      width: 140
    },
    {
      type: ColumnType.IsActive,
      prop: 'isActive',
      label: 'Status',
      width: 70
    },
    {
      type: ColumnType.Action,
      label: 'Action',
      width: 100,
      action: [
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: 'ft-edit',
          type: Act.Edit,
          descr: 'Edit'
        },
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: 'ft-settings',
          type: Act.Setting,
          descr: 'Setting'
        },
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: 'ft-eye',
          type: Act.View,
          descr: 'View'
        }
      ]
    }
  ]
};

export const DocumentTemplateListView: MsxView = {
  title: 'Document Template',
  components: [
    {
      type: WidgetType.SearchFilter,
      component: DocumentSearchFilter
    },
    {
      type: WidgetType.Datatable,
      component: DocumentTemplateTable
    }
  ]
};


