<section class="faq-wrapper">
  <!-- faq search starts -->
  <div class="faq-search">
    <div class="row">
      <div class="col-12">
        <div class="card faq-bg bg-transparent box-shadow-0">
          <div class="card-content">
            <div class="card-body p-md-5">
              <h1 class="faq-title text-center mb-3">Hello, how can we help?</h1>
              <form class="d-flex justify-content-center" action="javascript:;">
                <div class="faq-search-input form-group position-relative w-50">
                  <input type="text" class="form-control round form-control-lg shadow p-3" id="searchbar"
                    placeholder="Ask a question...">
                  <button class="btn btn-primary round position-absolute" type="button">
                    <span class="d-none d-sm-block">Search</span>
                    <i class="ft-search d-block d-sm-none"></i>
                  </button>
                </div>
              </form>
                <p class="card-text text-center font-medium-1 text-muted mt-3">or choose a category to quickly find the help you need</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- faq search ends -->
  <!-- faq starts -->
  <div class="faq">
    <div class="row">
      <div class="col-12">
        <div class="card bg-transparent shadow-none">
          <div class="card-content">
            <div class="card-body py-2">
              <div class="swiper-centered-slides swiper-container p-3" [swiper]="swiperCenterOpt1Config"
                (indexChange)="onIndexChange($event)">
                <div class="swiper-wrapper">
                  <div class="swiper-slide rounded swiper-shadow" id="getting-started-text">
                    <i class="ft-flag font-large-1"></i>
                    <div class="swiper-text pt-md-2 pt-sm-1">Getting Started</div>
                  </div>
                  <div class="swiper-slide rounded swiper-shadow" id="pricing-plans-text">
                    <i class="ft-dollar-sign font-large-1"></i>
                    <div class="swiper-text pt-md-2 pt-sm-1">Pricing & Plans</div>
                  </div>
                  <div class="swiper-slide rounded swiper-shadow" id="sales-question-text">
                    <i class="ft-shopping-bag font-large-1"></i>
                    <div class="swiper-text pt-md-2 pt-sm-1">Sales Question</div>
                  </div>
                  <div class="swiper-slide rounded swiper-shadow" id="usage-guide-text">
                    <i class="ft-book-open font-large-1"></i>
                    <div class="swiper-text pt-md-2 pt-sm-1">Usage Guides</div>
                  </div>
                  <div class="swiper-slide rounded swiper-shadow" id="general-guide-text">
                    <i class="ft-info font-large-1"></i>
                    <div class="swiper-text pt-md-2 pt-sm-1">General Guide</div>
                  </div>
                </div>
                <!-- Add Arrows -->
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
              </div>
              <div class="main-wrapper-content">
                <div class="wrapper-content active">
                  <div class="text-center p-md-4 p-sm-1 py-1 p-0">
                    <h1 class="faq-title">{{faqMain.title}}</h1>
                    <p>{{faqMain.content}}</p>
                  </div>
                  <div class="card">
                    <div class="accordion" id="faqAccordion3">
                      <div class="card-content">
                        <div class="card-body">
                          <ngb-accordion [closeOthers]="true">
                            <ngb-panel [id]="faq.faqId" *ngFor="let faq of faqMain.faqs">
                              <ng-template ngbPanelTitle>
                                <a data-toggle="collapse" aria-expanded="false" aria-controls="accordion1"
                                  class="card-title">{{faq.title}}</a>
                              </ng-template>
                              <ng-template ngbPanelContent>
                                <p>{{faq.content}}</p>
                              </ng-template>
                            </ngb-panel>
                          </ngb-accordion>
                          </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- faq ends -->
  <!-- faq contact starts -->
  <div class="faq-contact">
    <div class="row mb-2">
      <div class="col-12 text-center">
          <h3 class="faq-subtitle my-3">You still have a question?</h3>
          <p class="p-2 text-muted font-medium-1 mb-4">If you cannot find a question in our FAQ, you can always contact us. We will answer to you shortly!</p>
        </div>
    </div>
    <div class="row d-flex justify-content-center mb-5">
      <div class="col-sm-12 col-md-4 text-center border rounded p-3 mr-md-3 m-2">
        <div class="mb-2 d-inline-block">
          <i class="ft-phone-call primary font-large-1 cursor-pointer"></i>
        </div>
        <h5>+ (810) 2548 2568</h5>
        <p class="text-muted font-medium-1 m-0">We are always happy to help!</p>
      </div>
      <div class="col-sm-12 col-md-4 text-center border color-gray-faq rounded p-3 m-2">
        <div class="mb-2 d-inline-block">
          <i class="ft-mail primary font-large-1 cursor-pointer"></i>
        </div>
        <h5><a href="mailto:<EMAIL>"><EMAIL></a></h5>
        <p class="text-muted font-medium-1 m-0">Best way to get answer faster!</p>
      </div>
    </div>
  </div>
  <!-- faq contact ends -->
</section>
