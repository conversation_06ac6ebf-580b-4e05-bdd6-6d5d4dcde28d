import {BaseRequest} from './base.request';

export class LoginRequest extends BaseRequest {
  grant_type: string;
  username: string;
  password: string;
  client_id: string;
  client_secret: string;
  fcmToken?: string;

  constructor(grant_type:string, username: string, password: string, client_id: string, client_secret: string) {
    super();
    this.grant_type = grant_type;
    this.username = username;
    this.password = password;
    this.client_id = client_id;
    this.client_secret = client_secret;
  }
}
