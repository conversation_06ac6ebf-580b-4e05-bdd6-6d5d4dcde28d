export class InquiryUserdata {
  idMsUser: any;
  email: string;
  nik: string;
  name: string;
  placeOfBirth: string;
  dateOfBirth: string;
  gender: string;
  phone: string;
  address: string;
  provinsi: string;
  kota: string;
  kecamatan: string;
  kelurahan: string;
  kodePos: string;
  registrationStatus: string;
  refNumber: string;
  hideEditButton: boolean;
  isRegistered: string;
  emailService: string;
  statusEdit: string;
  vendorCode: string;

  constructor() {
    this.idMsUser = 0;
    this.email = '';
    this.nik = '';
    this.name = '';
    this.placeOfBirth = '';
    this.dateOfBirth = '';
    this.gender = '';
    this.phone = '';
    this.address = '';
    this.provinsi = '';
    this.kota = '';
    this.kecamatan = '';
    this.kelurahan = '';
    this.kodePos = '';
    this.registrationStatus = '';
    this.refNumber = '';
    this.hideEditButton = true;
    this.emailService = '0';
    this.isRegistered = '0';
  }
}
