<div class="row" style="margin-top: 15px">
    <div class="col-6">
      <div class="content-header" style="margin-top: 0 !important;">{{mode | translate}} Tenant</div>
    </div>
  </div>

  <div class="row match-height">
    <div class="col-6">
        <div class="card">
            <div class="card-content">
                <div class="card-body">
                    <form [formGroup]="addEditTenantForm">
                        <p class="content-header" translate>Tenant Name</p>
                        <div class="form-group">
                            <input type="text" formControlName="tenantName" id="tenantCode"
                                class="form-control mb-1 col-5">
                            <div *ngIf="addEditTenantForm.get('tenantName').invalid"
                                class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                {{validationMessages.tenantName.message}}</div>
                        </div>
                        <p class="content-header" translate>Tenant Code</p>
                        <div class="form-group" aria-disabled="tcReadOnly">
                            <input type="text" formControlName="tenantCode" id="tenantCode"
                                class="form-control mb-1 col-5" [disabled]="tcReadOnly" [readonly]="tcReadOnly">
                            <div *ngIf="addEditTenantForm.get('tenantCode').invalid"
                                class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                {{validationMessages.tenantCode.message}}</div>
                        </div>
                        <p class="content-header" translate>Ref Number Label</p>
                        <div class="form-group">
                            <input type="text" formControlName="refNumberLabel" id="refNumberLabel"
                                class="form-control mb-1 col-5">
                            <div *ngIf="addEditTenantForm.get('refNumberLabel').invalid"
                                class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                {{validationMessages.refNumberLabel.message}}</div>
                        </div>
                        <p class="content-header" translate>API Key</p>
                        <div class="form-group">
                            <div class="input-group mb-1">
                                <input type="text" formControlName="apiKey" id="apiKey"
                                    class="form-control mb-1 col-5">
                                <button class="input-group-addon btn btn-danger" (click)="generateApiKey()" style="padding-top: 0px; padding-bottom: 0px; 
                                    border-top-left-radius: 0px; border-bottom-left-radius: 0px;" translate>Generate</button>
                            </div>
                            <div *ngIf="addEditTenantForm.get('apiKey').invalid"
                                class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                {{validationMessages.apiKey.message}}</div>
                        </div>
                        <p class="content-header" translate>Balance Threshold</p>
                        <div *ngFor="let balance of balances" class="form-group">
                            <label *ngIf="balanceShow[balance.code]" for="sdt" class="form-title" translate>{{balance.description}}</label>
                            <div *ngIf="balanceShow[balance.code]" class="input-group mb-1">
                                <input [value]="balanceValue[balance.code]" type="number" min="0" id="{{balance.code}}" class="form-control mb-1 col-2">
                                <button class="input-group-addon btn btn-danger" (click)="deleteBalanceThreshold(balance.code)" style="padding-top: 0px; padding-bottom: 0px; 
                                border-top-left-radius: 0px; border-bottom-left-radius: 0px;" translate>Delete</button>
                            </div>
                            <button *ngIf="!balanceShow[balance.code]" class="btn btn-info" style="width: 170px;" (click)="addBalanceThreshold(balance.code)" translate>{{'Add' | translate}} {{balance.description | translate}}</button>
                        </div>

                        <p *ngIf="mode !== 'Add'" class="content-header" translate>Tenant Setting</p>
                        <div *ngFor="let setting of tenantSetting" class="form-group">
                            <label *ngIf="setting.exist" for="sdt" class="form-title" translate>{{setting.description}}</label>
                            <div *ngIf="setting.exist"  class="input-group mb-1" style="padding: 10px 0;">
                                <div *ngIf="setting.type === 'boolean'">
                                    <label class="switch">
                                        <input id="setting - {{setting.code}}" type="checkbox" [checked]="setting.value === '1'" (change)="toggleSwitch(setting)">
                                        <span class="slider round"></span>
                                    </label>
                                
                                    <button class="input-group-addon btn btn-danger delete-btn" (click)="deleteTenantSetting(setting.code)" translate>Delete</button>
                                </div>
                                <div *ngIf="setting.type === 'string'" class="input-group mb-1">
                                    <input [(ngModel)]="setting.value" type="text" id="setting - {{setting.code}}" class="form-control mb-1 col-4" [ngModelOptions]="{standalone: true}">
                                    <div *ngIf="setting.code ==='aesEncryptKey'">
                                        <button class="input-group-addon btn btn-danger" (click)="generateAESKey()" style="padding-top: 0px; padding-bottom: 0px; height: 25px;
                                    border-top-left-radius: 0px; border-bottom-left-radius: 0px;" translate>Generate</button>
                                    </div>
                                    <button class="input-group-addon btn btn-danger"  style="padding-top: 0px; padding-bottom: 0px; height: 25px; 
                                    border-top-left-radius: 0px; border-bottom-left-radius: 0px;" (click)="deleteTenantSetting(setting.code)" translate>Delete</button>
                                </div>

                                <div *ngIf="setting.type === 'number'" class="input-group mb-1">
                                    <input [(ngModel)]="setting.value" type="number" min="0" id="setting - {{setting.code}}" class="form-control mb-1 col-2" [ngModelOptions]="{standalone: true}">
                                    <button class="input-group-addon btn btn-danger" style="padding-top: 0px; padding-bottom: 0px; 
                                    border-top-left-radius: 0px; border-bottom-left-radius: 0px;" (click)="deleteTenantSetting(setting.code)" translate>Delete</button>
                                </div>

                                <div *ngIf="setting.type === 'dropdown'" class="input-group mb-1">
                                    <select  id="setting - {{setting.value}}"  class="form-control mb-1 col-5" [(ngModel)]="setting.value" [ngModelOptions]="{standalone: true}"> 
                                        <option *ngFor="let option of dropdownOptions[setting.code]" [value]="option.code">
                                            {{ option.code }}
                                        </option>
                                    </select>
                                    
                                    <button class="input-group-addon btn btn-danger" (click)="deleteTenantSetting(setting.code)" style="padding-top: 0px; padding-bottom: 0px; border-top-left-radius: 0px; border-bottom-left-radius: 0px;" translate>Delete</button>
                                </div>     
                            </div>
                            <button *ngIf="!setting.exist" class="btn btn-info" (click)="addTenantSetting(setting.code)" style="width: 170px;" translate>{{'Add' | translate}} {{setting.description | translate}}</button>
                        </div>
                        
                        <p class="content-header" translate>Balance Reminder Email Receiver</p>
                        <div *ngFor="let email of emailList; let i = index">
                            <div class="input-group mb-1">
                                <input [(ngModel)]="emailList[i].address" [ngModelOptions]="{standalone: true}"
                                    class="form-control col-5" placeholder="Tulis email">
                                <button class="input-group-addon btn btn-danger" (click)="deleteEmail(i)" style="padding-top: 0px; padding-bottom: 0px; 
                                    border-top-left-radius: 0px; border-bottom-left-radius: 0px;" translate>Delete</button>
                            </div>
                        </div>

                        <button class="btn btn-info" (click)="addEmail()" translate>Add</button>
                        <div *ngIf="mode === 'Add'">
                            <p class="content-header" translate>Email User Admin</p>
                            <div class="form-group">
                                <input type="text" formControlName="emailUserAdmin" id="emailUserAdmin"
                                    class="form-control mb-1 col-5">
                                <div *ngIf="addEditTenantForm.get('emailUserAdmin').invalid"
                                    class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                    {{validationMessages.emailUserAdmin.message}}</div>
                            </div>
                            <p class="content-header" translate>Kode Akses User Admin</p>
                            <div class="form-group">
                                <input type="text" formControlName="passwordUserAdmin" id="passwordUserAdmin"
                                    class="form-control mb-1 col-5">
                                <div *ngIf="addEditTenantForm.get('passwordUserAdmin').invalid"
                                    class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                    {{validationMessages.passwordUserAdmin.message}}</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 text-center" style="margin-top: 10px;">
                                <button class="btn btn-light mr-2" (click)="goBack()" translate>Cancel</button>
                                <button class="btn btn-info" (click)="addEditTenant()"
                                    [disabled]="addEditTenantForm.invalid" translate>Save</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>