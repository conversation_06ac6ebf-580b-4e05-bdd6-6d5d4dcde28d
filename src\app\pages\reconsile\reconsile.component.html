<div class="row" style="margin-top: 15px">
  <div class="col-6">
    <div class="content-header" style="margin-top: 0 !important;" translate>Reconcile Tool</div>
  </div>
</div>

<div class="row match-height">
  <div class="col-6">
    <div class="card">
      <div class="card-content">
        <div class="card-body">
          <form [formGroup]="msxForm" (ngSubmit)="onSubmit()" id="msxForm" #formElement>
            <ng-container *ngIf="formObj?.params && formObj.params?.length > 0">
              <input *ngFor="let param of formObj.params" type="hidden" [formControlName]="param.key"
                     [value]="param.value" [id]="param.key" />
            </ng-container>

            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('vendorCode')" [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>

              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('tenantCode')" [options]="getOptions('tenantCode')" [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <app-select [form]="msxForm" [question]="getQuestion('balanceType')" [options]="getOptions('balanceType')" [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>
            </div>

            <div class="row">
              <div class="col-6">
                <app-date [form]="msxForm" [question]="getQuestion('startDate')" direction="vertical"></app-date>
              </div>

              <div class="col-6">
                <app-date [form]="msxForm" [question]="getQuestion('endDate')" direction="vertical"></app-date>
              </div>
            </div>

            <div class="row">
              <div class="col-12 text-right">
                <button class="btn btn-info" type="submit" [disabled]="msxForm.invalid">Submit</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

