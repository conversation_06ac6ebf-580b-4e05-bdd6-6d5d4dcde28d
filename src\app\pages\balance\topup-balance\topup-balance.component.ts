import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { DateFormatConstant } from 'app/shared/components/ms-form/constants/date-format.constant';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { TopupBalanceRequest } from 'app/shared/dto/balance/topup-balance.request';
import * as moment from 'moment';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-topup-balance',
  templateUrl: './topup-balance.component.html',
  styleUrls: ['./topup-balance.component.scss']
})
export class TopupBalanceComponent implements OnInit {
  topupForm: FormModel<any>;
  xForm: FormGroup;

  constructor(private router: Router, private global: GlobalService, private http: HttpClient, private toastService: ToastrService) { }

  ngOnInit(): void {
    this.initView();
  }

  initView() {
    this.topupForm = {
      name: 'topupBalance',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        {
          key: 'tenantName',
          label: 'Nama Tenant',
          controlType: FormConstant.TYPE_TEXT,
          readonly: true,
          value: this.global.user.roles[0].tenantName
        },
        new QuestionDropdown({
          key: 'vendorCode',
          label: 'Vendor',
          placeholder: 'Select Vendor',
          serviceUrl: URLConstant.GetVendorList,
          args: {
            list: 'vendorList',
            key: 'code',
            value: 'name'
          },
          required: true
        }),
        new QuestionDropdown({
          key: 'balanceType',
          label: 'Tipe Balance',
          placeholder: 'Select Balance Type',
          serviceUrl: URLConstant.GetLov,
          args : {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params : {
            lovGroup : 'BALANCE_TYPE',
            constraint1: 'CONFIG_BALANCE'
          },
          required: true
        }),
        {
          key: 'qty',
          label: 'Penambahan Balance',
          placeholder: '0',
          controlType: FormConstant.TYPE_CURRENCY,
          required: true
        },
        {
          key: 'refNo',
          label: 'Nomor Invoice',
          placeholder: 'Type Invoice Number',
          controlType: FormConstant.TYPE_TEXT,
          maxLength: 100,
          required: true
        },
        {
          key: 'notes',
          label: 'Notes',
          placeholder: 'Type Notes',
          controlType: FormConstant.TYPE_TEXT,
          maxLength: 200,
          required: true
        },
        {
          key: 'trxDate',
          label: 'Tanggal Pembelian',
          placeholder: CommonConstant.FORMAT_DATE,
          controlType: FormConstant.TYPE_DATE,
          required: true
        }
      ],
      params: [
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_INPUT,
          type: FormConstant.TYPE_TEXT,
          value: this.global.user.role.tenantCode
        }
      ]
    }
  }

  onForm(form: FormGroup) {
    this.xForm = form;
  }

  onInput(result) {
    if (result.question.key === 'qty') {
      if (Number(result.data) <= 0) {
        this.toastService.error('Penambahan balance tidak boleh kurang dari 0', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
      }
    } else if (result.question.key === 'refNo') {
      if (String(result.data).length > 100) {
        this.toastService.error('Panjang invoice number tidak boleh > 100', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
      }
    } else if ((result.question.key === 'notes')) {
      if (String(result.data).length > 200) {
        this.toastService.error('Panjang notes tidak boleh > 200', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
      }
    }
  }

  onNext(data) {

    if (String(data['refNo']).length <= 0) {
      this.toastService.error('Invoice Number tidak boleh kosong', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }
    if (String(data['notes']).length <= 0) {
      this.toastService.error('notes tidak boleh kosong', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
    }

    const request: TopupBalanceRequest = new TopupBalanceRequest();
    request.tenantCode = data.tenantCode;
    request.vendorCode = data.vendorCode;
    request.balanceTypeCode = data.balanceType;
    request.notes = data.notes;
    request.qty = data.qty;
    request.refNo = data.refNo;
    request.trxDate = moment(data.trxDate).format(DateFormatConstant.FORMAT_EN_DEFAULT);
    request.audit.callerId = this.global.user.loginId;
    this.http.post(URLConstant.TopupBalance, request).subscribe(response => {
      if (response['status']['code'] === 0) {
        this.toastService.success('Topup success', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        window.location.reload();
      } else {
        this.toastService.error(response['status']['message'], null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        })
      }
    });
  }
}
