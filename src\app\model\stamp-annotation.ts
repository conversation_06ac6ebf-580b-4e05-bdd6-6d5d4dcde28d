import {Signer} from '../shared/components/document-anotate/model/signer';

export class StampAnnotation {
  stampPage: string;
  transform: string;
  notes: string;
  stampLocation: {
    llx: string;
    lly: string;
    urx: string;
    ury: string;
  };
  positionPrivy: string;

  public static fromSigner(signer: Signer) {
    const annotation = new StampAnnotation();
    annotation.stampPage = signer.signPage.toString();
    annotation.transform = signer.transform;
    annotation.notes = signer.notes || '';
    annotation.stampLocation = {
      llx: signer.signLocation.llx.toString(),
      lly: signer.signLocation.lly.toString(),
      urx: signer.signLocation.urx.toString(),
      ury: signer.signLocation.ury.toString()
    };
    annotation.positionPrivy = signer.positionPrivy;

    return annotation;
  }
}
