
import { ReportLinkInvitation } from "app/model/report-link-invitation";
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { QuestionDate, QuestionDropdown, QuestionTextbox } from "app/shared/components/ms-form/questions";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { URLConstant } from "app/shared/constant/URLConstant";
import { CommonConstant } from "app/shared/constant/common.constant";

const ReportLinkInvitationSearchFilter: FormModel<string> = {
    name: 'ReportLinkInvitationSearchForm',
    direction: FormConstant.DIRECTION_HORIZONTAL,
    colSize: 6,
    exportExcel: true,
    components: [
        new QuestionTextbox({
          key: 'nama',
          label: 'name',
          placeholder: 'Type name here',
          value: ''
        }),
        new QuestionTextbox({
            key: 'penerimaUndangan',
            label: 'Invitation Recipient',
            placeholder: 'Type Invitation Recipient here',
            value: ''
          }),
        new QuestionDate({
            key: 'tanggalPengirimanDari',
            label: 'Delivery Date From',
            placeholder: CommonConstant.FORMAT_DATE,
            isDateStart: true
          }),
        new QuestionDate({
            key: 'tanggalPengirimanSampai',
            label: 'Delivery Date To',
            placeholder: CommonConstant.FORMAT_DATE,
            isDateEnd: true
          }),
        new QuestionDropdown({
          key: 'statusUndangan',
          label: 'Invitation Status',
          placeholder: 'Select Invitation Status',
          options: [
            {key: '', value: 'All'},
            {key: '1', value: 'AKTIF'},
            {key: '0', value: 'NON AKTIF'}
          ],
        }),
        new QuestionDropdown({
            key: 'pengirimanMelalui',
            label: 'Shipping Via',
            placeholder: 'Select Shipping Via',
            options: [
              {key: '', value: 'All'},
              {key: 'SMS', value: 'SMS'},
              {key: 'Email', value: 'Email'}
            ]
        }),
        new QuestionDropdown({
            key: 'statusRegistrasi',
            label: 'Registration Status',
            placeholder: 'Select Registration Status',
            options: [
                {key: '', value: 'All'},
                {key: '1', value: 'DONE'},
                {key: '0', value: 'NOT DONE'}
              ],
        })
    ],
    params: [
        {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
        }
    ]
};

const ReportLinkInvitationTable: Table<ReportLinkInvitation> = {
    name: 'listInvitation',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'nama',
            label: 'name',
            width: 130
        },
        {
            type: ColumnType.Text,
            prop: 'pengirimanMelalui',
            label: 'Shipping Via',
            width: 80
        },
        {
            type: ColumnType.Text,
            prop: 'penerimaUndangan',
            label: 'Invitation Recipient',
            width: 150
        },
        {
          type: ColumnType.Date,
          format: 'DD-MMM-YYYY HH:MM:SS',
          prop: 'tanggalPengiriman',
          label: 'Delivery Date',
          width: 70
        },
        {
            type: ColumnType.Date,
            format: 'DD-MMM-YYYY HH:MM:SS',
            prop: 'tanggalRegistrasi',
            label: 'Registration Date',
            width: 70
          },
        {
            type: ColumnType.Text,
            prop: 'statusRegistrasi',
            label: 'Registration Status',
            width: 80
        },
        {
            type: ColumnType.Text,
            prop: 'statusUndangan',
            label: 'Invitation Status',
            width: 80
        }
    ]
};

export const ReportLinkInvitationListView: MsxView = {
    title: 'listInvitation',
    components: [
        {
            type: WidgetType.SearchFilter,
            component: ReportLinkInvitationSearchFilter
        },
        {
            type: WidgetType.Datatable,
            component: ReportLinkInvitationTable
        }
    ]
};
