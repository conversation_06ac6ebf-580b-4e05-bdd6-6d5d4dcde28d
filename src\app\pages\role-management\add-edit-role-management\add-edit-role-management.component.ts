import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { FormB<PERSON>er, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FormModel } from 'app/shared/components/ms-form/models';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { DeviceDetectorService } from 'ngx-device-detector';
import { AddRoleManagementRequest } from 'app/model/api/add-role-management.request';
import { RoleService } from 'app/services/api/role.service';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { ToastrService } from 'ngx-toastr';
import { EditRoleManagementRequest } from 'app/model/api/edit-role-management.request';

@Component({
  selector: 'app-add-edit-role-management',
  templateUrl: './add-edit-role-management.component.html',
  styleUrls: ['./add-edit-role-management.component.scss']
})
export class AddEditRoleManagementComponent implements OnInit {
  @Input() roleName: string;
  @Input() tenantCode: string;
  @Input() roleCode: string;
  @Input() status: string;
  @Input() mode: string;
  @Output() result = new EventEmitter<void>();

  formObj: FormModel<any>;
  msxForm: FormGroup;
  col = 12;

  swal = swalFunction;

  constructor(public activeModal: NgbActiveModal, private deviceService: DeviceDetectorService,
    private roleService: RoleService, private toastrService: ToastrService, private cdr: ChangeDetectorRef
  ) 
  { 
    if (deviceService.isMobile()) {
      this.col = 12;
    }
  }

  ngOnInit(): void {
    this.initView(); 
  }

  initView() {
    this.formObj = {
      mode: this.mode,
      name: 'addEditRoleManagement',
      colSize: 12,
      isModal: true,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionDropdown({
          key: 'tenantCode',
          label: 'Tenant',
          placeholder: 'Select Tenant',
          serviceUrl: URLConstant.GetTenantList,
          value: this.tenantCode,
          readonly: this.mode === 'Edit',
          args: {
            list: 'tenantList',
            key: 'tenantCode',
            value: 'tenantName'
          },
          params: {
            status: '1'
          },
          required: true,
          isInModal: true,
        }),
        {
          key: 'roleName',
          label: 'Role Name',
          placeholder: 'Type role name here',
          controlType: FormConstant.TYPE_TEXT,
          value: this.roleName,
          required: true
        }
      ],
      params: []
    };

    if (this.mode === 'Edit') {
      this.formObj.components.push(
        new QuestionDropdown({
          key: 'isActive',
          label: 'Status',
          placeholder: 'Select status',  
          value: '',
          options: [ 
              {key: '1', value: 'Active'},
              {key: '0', value: 'Inactive'},
          ], 
          required: true, 
        }), 
      );
    }
  }

  onSubmit(data: any) {
    if (data['isTrusted']) {
      return;
    }

    if (this.mode === 'Add') {
      const request: AddRoleManagementRequest = new AddRoleManagementRequest();
      request.roleName = data.roleName;
      request.tenantCode = data.tenantCode;
      this.roleService.add(request).subscribe(
        response => {
          if (response.status.code === 0) {
            this.activeModal.dismiss({action: 'refresh'});
            this.toastrService.info(response.status.message, null, {
              positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
            }); 
          }
        }
      );
    } else {
      const request: EditRoleManagementRequest = new EditRoleManagementRequest();
      request.roleCode = this.roleCode;
      request.roleName = data.roleName;
      request.isActive = data.isActive;
      request.tenantCode = data.tenantCode;
      this.roleService.edit(request).subscribe(
        response => {
          if (response.status.code === 0) {
            this.activeModal.dismiss({action: 'refresh'});
            this.toastrService.info(response.status.message, null, {
              positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
            });
            this.result.emit();
          }
        }
      );
    }
  }

  onForm(form: FormGroup) {
    this.msxForm = form;

    if (this.mode === 'Edit') {
      this.msxForm.patchValue({
        isActive: this.status,
        roleName: this.roleName,
        tenantCode: this.tenantCode
      }); 
      this.msxForm.get('tenantCode').disable();

      this.cdr.detectChanges();
    }
  }

  dismiss() {
    this.activeModal.dismiss("cancel");
  }

}
