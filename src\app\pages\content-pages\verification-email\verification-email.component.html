<form [formGroup]="templateForm" (ngSubmit)="onSubmit()">
    <div class="modal-body">
        <button type="button" class="close" aria-label="Close" (click)="dismiss()">
            <span aria-hidden="true">&times;</span>
        </button>

        <div *ngIf="!isOTPSelect">
            <div class="mt-5">
                <div class="row justify-content-center align-items-center">
                    <p class="font-large-1 text-center text-bold-700 px-5" translate>Enter verification code</p>
                </div>
            </div>
    
            <div class="row justify-content-center align-items-center">
                <p class="text-center mb-0" translate>We have sent a verification code to</p>
            </div>
            
            <div class="row justify-content-center align-items-center">
                <p class="text-center">{{ recepient }}</p>
            </div>
    
            <input formControlName="otp" type="tel" class="letterotp mt-3" maxlength="6" id="otp" value="">
          
            <div class="col justify-content-center align-items-center mt-4">
                <div *ngIf="timeLeft > 0" class="row justify-content-center align-items-center">{{'Wait' | translate}} {{ timeLeft }} {{'seconds' | translate}}</div>
                <div *ngIf="timeLeft === -1" id="ketotp" class="d-flex justify-content-center align-items-center" translate>Didn't get the code?</div>
            </div>
            
            
            <div *ngIf="timeLeft === -1" class="col justify-content-center align-items-center mt-3">
                <div *ngIf="isResendOtpSelect" class="row my-8 px-4 justify-content-center align-items-center">
                    <div class="col">
                        <div *ngif="notifType != 'EMAIL'" class="row justify-content-center align-items-center">
                            <p class="text-center mb-0" translate>Media pengiriman OTP :</p>
                        </div>
                        
                        <div class="row">
                            <div class="col-12 mt-2" *ngIf="isSMS">
                                <span class="border rounded border-primary d-flex align-items-center" (click)="onClickSMS()">
                                    <div class="form-check form-check-inline p-3">
                                        <input class="form-check-input" type="radio" id="SMS" name="verification" [checked]="default == 'SMS'" style="pointer-events: none;">
                                        <label class="form-check-label label-radio-button" for="SMS" translate>SMS</label>
                                    </div>
                                </span>
                            </div>
                        
                            <div class="col-12 mt-2" *ngIf="isWA">
                                <span class="border rounded border-primary d-flex align-items-center" (click)="onClickWA()">
                                    <div class="form-check form-check-inline p-3">
                                        <input class="form-check-input" type="radio" id="WA" name="verification" [checked]="default == 'WA'" style="pointer-events: none;">
                                        <label class="form-check-label label-radio-button" for="WA" translate>Whatsapp</label>
                                    </div>
                                </span>
                            </div>

                            <div class="col-12 mt-2" *ngIf="isEmail">
                                <span class="border rounded border-primary d-block" (click)="onClickEMAIL()">
                                  <div class="form-check form-check-inline align-middle p-3">
                                    <input class="form-check-input" type="radio" id="EMAIL" name="verification" [checked]="default == 'EMAIL'" style="pointer-events: none;">
                                    <label class="form-check-label label-radio-button" for="EMAIL" translate>Email</label>
                                  </div>
                                </span>
                              </div>
                        </div>
                    </div>
                </div>
            
                <div class="row justify-content-center align-items-center mt-4" style="color: #FF7F37">
                    <a (click)="sendOtp()" translate>Send code again</a>
                </div>
                
            </div>
            
            

            <button type="submit" class="btn btn-info btn-block mt-4" [disabled]="templateForm.invalid" translate>Verify</button>
        </div>
            
        <div *ngIf="isOTPSelect">
            <div class="mt-5 px-4">
                <div class="row justify-content-center align-items-center">
                    <p class="font-large-1 text-center text-bold-700 px-5" translate>Lupa Kode Akses</p>
                </div>
            </div>
    
            <div class="row my-4 justify-content-center align-items-center">
                <p class="text-center mb-0" translate>Silahkan Pilih Media Pengiriman OTP untuk reset kode akses Anda</p>
            </div>
    
            <div class="row">
                <div class="col-12 mt-2" *ngIf="isSMS">
                    <span class="border rounded border-primary d-block" (click)="onClickSMS()">
                      <div class="form-check form-check-inline align-middle p-3">
                        <input class="form-check-input" type="radio" id="SMS" name="verification" [checked]="default == 'SMS'" style="pointer-events: none;">
                        <label class="form-check-label label-radio-button" for="SMS" translate>SMS</label>
                      </div>
                    </span>
                </div>
    
                <div class="col-12 mt-2" *ngIf="isWA">
                    <span class="border rounded border-primary d-block" (click)="onClickWA()">
                      <div class="form-check form-check-inline align-middle p-3">
                        <input class="form-check-input" type="radio" id="WA" name="verification" [checked]="default == 'WA'" style="pointer-events: none;">
                        <label class="form-check-label label-radio-button" for="WA" translate>Whatsapp</label>
                      </div>
                    </span>
                  </div>
                  <div class="col-12 mt-2" *ngIf="isEmail">
                    <span class="border rounded border-primary d-block" (click)="onClickEMAIL()">
                      <div class="form-check form-check-inline align-middle p-3">
                        <input class="form-check-input" type="radio" id="EMAIL" name="verification" [checked]="default == 'EMAIL'" style="pointer-events: none;">
                        <label class="form-check-label label-radio-button" for="EMAIL" translate>Email</label>
                      </div>
                    </span>
                  </div>
            </div>

            <div class="row justify-content-center align-items-center">
                <div class="m-4">
                    <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss('Cross click')">Cancel</button>
                </div>
                <div class="m-4">
                    <button type="button" class="btn btn-primary" (click)="changeModalForgotPassword()">Process</button>
                </div>
            </div>
        </div>
    </div>
</form>
  