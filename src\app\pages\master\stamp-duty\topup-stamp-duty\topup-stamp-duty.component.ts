import { HttpClient } from '@angular/common/http';
import { Component, OnInit} from '@angular/core';
import { Router } from '@angular/router';
import { TopupStampDutyRequest } from 'app/model/api/topup.sdt.request';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';
import {FormBuilder, FormGroup} from '@angular/forms';
import { DeviceDetectorService } from 'ngx-device-detector';

@Component({
  selector: 'app-topup-stamp-duty',
  templateUrl: './topup-stamp-duty.component.html',
  styleUrls: ['./topup-stamp-duty.component.scss']
})
export class TopupStampDutyComponent implements OnInit {
  mForm: FormModel<any>;
  xForm: FormGroup;
  vendorOptions: {key: string, value: string}[] = [];
  fee = 0;
  qty = 0;
  col = 6;

  constructor(private router: Router, private global: GlobalService, private http: HttpClient,
    private toastService: ToastrService, private deviceService: DeviceDetectorService) {
    if (deviceService.isMobile()) {
      this.col = 12;
    }
  }

  ngOnInit(): void {
    this.initView();
  }

  goBack() {
    this.router.navigate([PathConstant.LIST_STAMP_DUTY]);
  }

  initView() {
    this.mForm = {
      mode: 'Topup',
      name: 'topupStampDuty',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionDropdown({
          key: 'vendorCode',
          label: 'Vendor',
          placeholder: 'Select Vendor',
          serviceUrl: URLConstant.GetVendorList,
          params: {
            vendorTypeCode: CommonConstant.VENDOR_TYPE_E_MATERAI
          },
          args: {
            list: 'vendorList',
            key: 'code',
            value: 'name'
          }
        }),
        {
          key: 'stampDutyInvNo',
          label: 'Stamp Duty Invoice Number',
          placeholder: 'Type stamp duty invoice number here',
          controlType: FormConstant.TYPE_TEXT,
          required: true
        },
        {
          key: 'stampDutyDate',
          label: 'Stamp Duty Invoice Date',
          placeholder: CommonConstant.FORMAT_DATE,
          controlType: FormConstant.TYPE_DATE,
          required: true
        },
        {
          key: 'stampDutyFee',
          label: 'Stamp Duty Fee',
          placeholder: 'Type stamp duty fee here',
          controlType: FormConstant.TYPE_CURRENCY,
          required: true
        },
        {
          key: 'stampDutyQty',
          label: 'Stamp Duty Qty',
          placeholder: 'Type stamp duty qty here',
          controlType: FormConstant.TYPE_CURRENCY,
          required: true,
        },
        {
          key: 'notes',
          label: 'Notes',
          placeholder: 'Type notes here',
          controlType: FormConstant.TYPE_TEXT,
          required: true
        },
        {
          key: 'invoiceAmount',
          label: 'Invoice Amount',
          placeholder: '0',
          controlType: FormConstant.TYPE_CURRENCY,
          readonly: true
        }
      ],
      params: [
        {
          key: 'loginId',
          controlType: FormConstant.TYPE_INPUT,
          type: FormConstant.TYPE_HIDDEN,
          value: this.global.user.loginId
        },
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_INPUT,
          type: FormConstant.TYPE_HIDDEN,
          value: this.global.user.role.tenantCode
        },
      ]
    }
  }

  onInput(result) {
    let value;
    console.log('onInput', result.data);
    if (result['question']['key'] === 'stampDutyFee') {
      this.fee = parseInt(String(result.data).split('.').join(''));
      value = this.fee * this.qty;
      this.xForm.patchValue({invoiceAmount: value});
    }

    if (result['question']['key'] === 'stampDutyQty') {
      this.qty = parseInt(String(result.data).split('.').join(''));
      value = this.fee * this.qty;
      this.xForm.patchValue({invoiceAmount: value});
    }
  }

  onForm(form: FormGroup) {
    this.xForm = form;
  }

  onNext(data) {
    // tslint:disable-next-line:radix
    if (parseInt(data['stampDutyFee']) <= 0) {
      this.toastService.error('Stamp duty fee tidak boleh kurang dari 0', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }
    // tslint:disable-next-line:radix
    if (parseInt(data['stampDutyQty']) <= 0) {
      this.toastService.error('Stamp duty qty tidak boleh kurang dari 0', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }
    if (String(data['stampDutyInvNo']).length > 100) {
      this.toastService.error('Panjang invoice number tidak boleh > 100', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }
    if (String(data['notes']).length > 200) {
      this.toastService.error('Panjang notes tidak boleh > 100', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }

    // Untuk sementara ubah manual, sebelumnya kena error
    const date = data['stampDutyDate']['year'] + '-' + data['stampDutyDate']['month'] + '-' + data['stampDutyDate']['day'];
    data['stampDutyDate'] = date;

    const req: TopupStampDutyRequest = {...data};
    console.log("Topup request", req);
    this.http.post(URLConstant.CreateStampDuty, req).subscribe(response => {
      if (response['status']['code'] === 0) {
        this.toastService.success('Topup success', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        this.router.navigate([PathConstant.LIST_STAMP_DUTY]);
      }
    });
  }

}
