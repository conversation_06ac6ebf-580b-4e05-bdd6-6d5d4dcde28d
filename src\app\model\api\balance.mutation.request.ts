import { AuditContext } from "../audit.context";
import { BaseRequest } from "./base.request";

export class BalanceMutationRequest extends BaseRequest {
    tenantCode: string;
    vendorCode: string;
    balanceType: string;
    transactionType: string;
    transactionDateStart: string;
    transactionDateEnd: string;
    documentType: string;
    documentName: string;
    officeCode: string;
    referenceNo: string;
    page: number;
    status: string;
    constructor() {
        super();
        this.audit = new AuditContext();
    }
}