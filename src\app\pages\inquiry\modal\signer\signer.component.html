<div class="modal-header">
  <h4 class="modal-title font-weight-bold" translate>Document Signer</h4>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <ng-container *ngIf="!isMobile">
    <app-msx-datatable [tableObj]="signerTable" [datasource]="datasource"></app-msx-datatable>
  </ng-container>

  <ng-container *ngIf="isMobile">
    <div *ngFor="let signer of listSigner" class="cards">
      <div class="body">

        <div style="border-bottom: 1px solid #E6E7E8 !important;"></div>
        <h5 class="capitalize font-weight-bold pt-2">{{signer.signerName.toLowerCase()}}</h5>
        <h5 class="capitalize font-weight-bold">{{signer.signerType.toLowerCase()}}</h5>
        <div style="border-bottom: 1px solid #E6E7E8 !important;"></div>

        <div class="detail py-2">
          <div class="row">
             <div class="col-6">
                <label class="font-small-5" translate>Email</label>
             </div>
             <div class="col-6 text-right">
                <label class="capitalize font-small-5">{{signer.signerEmail.toLowerCase()}}</label>
             </div>
          </div>
          <div class="row">
             <div class="col-6">
                <label class="font-small-5" translate>Status</label>
             </div>
             <div class="col-6 text-right">
                <label class="capitalize font-small-5" [ngClass]="getSignStatus(signer)">{{signer.signStatus.toLowerCase()}}</label>
             </div>
          </div>
          <div class="row">
             <div class="col-6">
                <label class="font-small-5" translate>Signed Date</label>
             </div>
             <div class="col-6 text-right">
                <label class="font-small-5">{{ signer.signDate ? signer.signDate : '-' }}</label>
             </div>
          </div>
       </div>

         <div style="border-bottom: 1px solid #E6E7E8 !important;"></div> 

      </div>
    </div>

    <div class="row pt-2">
      <div class="col-6">
        <label class="font-small-5" translate>Total</label>
      </div>
      <div class="col-6 text-right">
        <h5 class="card-title font-weight-bold">{{listSigner.length}}</h5>
      </div>
     </div> 
  </ng-container> 
</div>
   