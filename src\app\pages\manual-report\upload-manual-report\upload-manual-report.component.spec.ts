import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UploadManualReportComponent } from './upload-manual-report.component';

describe('UploadManualReportComponent', () => {
  let component: UploadManualReportComponent;
  let fixture: ComponentFixture<UploadManualReportComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ UploadManualReportComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UploadManualReportComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
