import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ColumnMode } from '@swimlane/ngx-datatable';
import { FormConstant } from '../../../shared/components/ms-form/constants/form.constant';
import { FormModel } from '../../../shared/components/ms-form/models';
import { BalanceRequest } from 'app/model/api/balance.request';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { HttpClient } from '@angular/common/http';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { ToastrService } from 'ngx-toastr';
import { VendorListRequest } from 'app/model/api/vendor-list.request';
import { AuditContext } from 'app/model/audit.context';
import { BalanceMutationRequest } from 'app/model/api/balance.mutation.request';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { BalanceMutation } from 'app/model/balance-mutation';
import { saveAs } from 'file-saver';
import { QuestionDate, QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import {NgxSpinnerService} from 'ngx-spinner';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-balance',
  templateUrl: './balance.component.html',
  styleUrls: ['./balance.component.scss'],
  encapsulation: ViewEncapsulation.None
})

export class BalanceComponent implements OnInit {
  public ColumnMode = ColumnMode;
  public tempData = [];
  serviceBalanceMutation = URLConstant.GetListBalanceMutation
  view: MsxView;

  swal = swalFunction;

  balances = [];
  waiting = false;

  vendors: any[];
  balanceMutationList: BalanceMutation[];

  vendorForm = this.formBuilder.group({
    vendorSelect: ['']
  });

  searchFormObj: FormModel<any> = new FormModel<any>();
  form: FormGroup;

  constructor(private formBuilder: FormBuilder, private global: GlobalService, private http: HttpClient,
              private toastrService: ToastrService, private ngZone: NgZone, private cdk: ChangeDetectorRef,
              private spinner: NgxSpinnerService) {

  };

  async ngOnInit() {
    this.getVendor();
  };

  private dateToString(dateObject: any) {
    return (dateObject['year']) ? dateObject['year'] + '-' + dateObject['month'] + '-' + dateObject['day'] : dateObject;
  }

  initView() {
    this.searchFormObj = {
      name: 'SearchForm',
      colSize: 6,
      exportExcel: true,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        new QuestionDropdown({
          key: 'balanceType',
          label: 'Balance Type',
          placeholder: 'Select Balance Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          required: true,
          validations: [
            {type: 'required', message: 'Payment Sign Type harus di isi.'}
          ],
          params: {
            lovGroup: 'BALANCE_TYPE'
          }
        }),
        {
          key: 'referenceNo',
          label: 'Reference No',
          placeholder: 'Type reference number here',
          controlType: FormConstant.TYPE_TEXT
        },
        new QuestionDropdown({
          key: 'transactionType',
          label: 'Transaction Type',
          placeholder: 'Select Transaction Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'TRX_TYPE'
          }
        }),
        {
          key: 'documentName',
          label: 'Document Name',
          placeholder: 'Type document name here',
          controlType: FormConstant.TYPE_TEXT
        },
        new QuestionDate({
          key: 'transactionDateStart',
          label: 'Transaction Date From',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        {
          key: 'transactionDateEnd',
          label: 'Transaction Date To',
          placeholder: CommonConstant.FORMAT_DATE,
          controlType: FormConstant.TYPE_DATE
        },
        new QuestionDropdown({
          key: 'documentType',
          label: 'Document Type',
          placeholder: 'Select Document Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'DOC_TYPE'
          }
        }),
        new QuestionDropdown({
          key: 'officeCode',
          label: 'Office',
          placeholder: 'Select Office',
          serviceUrl: URLConstant.OfficeList,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'officeList',
            key: 'officeCode',
            value: 'officeName'
          },
          params: {
            tenantCode: this.global.user.role.tenantCode
          }
        }),
        new QuestionDropdown({
          key: 'status',
          label: 'Status Balance',
          placeholder: 'Select Balance Status',
          options: [
            {key: '', value: 'All'},
            {key: 'Success', value: 'Success'},
            {key: 'Failed', value: 'Failed'}
          ]
        })
      ],
      params: [
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.user.role.tenantCode
        },
        {
          key: 'vendorCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.vendor
        },
        {
          key: 'audit',
          controlType: FormConstant.TYPE_HIDDEN,
          value: {
            callerId: this.global.user.loginId
          }
        },
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        }
      ]
    }

    const BalanceMutationTable: Table<BalanceMutation> = {
      name: 'listMutation',
      list: [],
      columns: [
        {
          type: ColumnType.Text,
          prop: 'transactionNo',
          label: 'Trx No',
          width: 50
        },
        {
          type: ColumnType.Text,
          prop: 'transactionDate',
          label: 'Trx Date',
          width: 120
        },
        {
          type: ColumnType.Text,
          prop: 'officeName',
          label: 'Office',
          width: 80
        },
        {
          type: ColumnType.Text,
          prop: 'transactionType',
          label: 'Trx Type',
          width: 100
        },
        {
          type: ColumnType.Text,
          prop: 'customerName',
          label: 'Trx By',
          width: 80
        },
        {
          type: ColumnType.Text,
          prop: 'refNumber',
          label: 'Ref No',
          width: 150
        },
        {
          type: ColumnType.Text,
          prop: 'documentType',
          label: 'Doc Type',
          width: 110
        },
        {
          type: ColumnType.Text,
          prop: 'documentName',
          label: 'Doc Name',
          width: 120
        },
        {
          type: ColumnType.Text,
          prop: 'notes',
          label: 'Notes',
          width: 150
        },
        {
          type: ColumnType.Currency,
          prop: 'qty',
          label: 'Qty',
          width: 70
        },
        {
          type: ColumnType.Currency,
          prop: 'balance',
          label: 'Balance',
          width: 70
        }
      ]
    };

    this.view = {
      title: 'Balance Mutation',
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: BalanceMutationTable
        }
      ]
    }
  }

  changeVendor(e) {
    this.global.vendor = e.target.value;
    this.getBalance(this.global.vendor);
    this.form.patchValue({
      vendorCode: this.global.vendor
    });
    this.initView();
  }

  onFormListener(form: FormGroup) {
    this.form = form;
  }

  getVendor() {
    const vendorListReq: VendorListRequest = new VendorListRequest();
    vendorListReq.audit = new AuditContext();
    vendorListReq.audit.callerId = this.global.user.loginId;
    vendorListReq.tenantCode = this.global.user.role.tenantCode;
    vendorListReq.vendorTypeCode = '';
    this.http.post(URLConstant.GetVendorList, vendorListReq).subscribe((response) => {
      if (response['status']['code'] !== 0) {
        this.toastrService.error(response['status']['message']);
      } else {
        this.vendors = response['vendorList'];
        if (!this.global.vendor) {
          this.global.vendor = this.vendors[0].code;
        }
        this.initView();
        this.getBalance(this.global.vendor);
        this.vendorForm.patchValue({
          vendorSelect: this.global.vendor
        });
      }
    });
  }

  async getBalance(vendorCode: string) {
    const balanceReq: BalanceRequest = new BalanceRequest();
    balanceReq.audit.callerId = this.global.user.loginId;
    balanceReq.tenantCode = this.global.user.role.tenantCode;
    balanceReq.vendorCode = vendorCode;
    this.waiting = true;
    this.http.post(URLConstant.GetBalanceByTenantCodeAndVendorCode, balanceReq).subscribe(
      (response) => {
        if (response["status"]["code"] != 0) {
          this.toastrService.error(response["status"]["message"]);
        } else {
          this.balances = response["listBalance"];
          this.formatNumber(this.balances);
        }
        this.waiting = false;
        this.spinner.hide();
      }
    );

    this.waiting = false;
  }

  formatNumber(listBalance) {
    for (var i = 0; i < listBalance.length; i++) {
      listBalance[i].currentBalance = listBalance[i].currentBalance.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
  }

  async getBalanceMutationFile(params) {
    const bmReq: BalanceMutationRequest = new BalanceMutationRequest();
    bmReq.tenantCode = this.global.user.role.tenantCode;
    bmReq.vendorCode = this.global.vendor;
    bmReq.officeCode = params.officeCode;
    bmReq.balanceType = params.balanceType;
    bmReq.documentType = params.documentType;
    bmReq.documentName = params.documentName;
    bmReq.referenceNo = params.referenceNo;
    bmReq.transactionType = params.transactionType;
    bmReq.status = params.status;
    
    if (params.transactionDateStart != null && params.transactionDateStart != "") {
      bmReq.transactionDateStart = this.dateToString(params.transactionDateStart);
    }
      
    if (params.transactionDateEnd != null && params.transactionDateEnd != "") {
      bmReq.transactionDateEnd = this.dateToString(params.transactionDateEnd);
    }
      
    bmReq.audit = new AuditContext();
    bmReq.audit.callerId = this.global.user.loginId;
    await this.http.post(URLConstant.GetListBalanceMutationFile, bmReq).toPromise().then(
      (response) => {
        if (response["status"]["code"] != 0) {
          // agar tidak muncul 2 kali message errornya
          //this.toastrService.error(response["status"]["message"]);
        } else {
          const blob = this.b64toBlob(response["excelBase64"]);
          saveAs(blob, response["filename"]);
        }
      }
    );
  }

  b64toBlob(b64Data, contentType = '', sliceSize = 512) {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }

  searchFormRequest(params: any) {
    console.log('Search Form Request', params);
    this.balanceMutationList = params.listMutation;
  }
}
