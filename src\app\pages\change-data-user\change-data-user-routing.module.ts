import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ChangeDataComponent } from './change-data/change-data.component';
import {ChangeDataInputComponent} from './change-data-input/change-data-input.component';
import {PathConstant} from '../../shared/constant/PathConstant';

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: 'change-data',
        component: ChangeDataComponent,
        data: {
          title: 'Change Data'
        }
      },
      {
        path: PathConstant.CHANGE_DATA_INPUT,
        component: ChangeDataInputComponent,
        data: {
          title: 'Change Data'
        }
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ChangeDataUserRoutingModule {}
