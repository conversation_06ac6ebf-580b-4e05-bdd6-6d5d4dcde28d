.alert-light-danger {
  background-color: #FEEFD0 !important;
  color: #F55252  !important;
  border-color: #FEEFD0;
}

.left-content {
  background-color:#F9F9F9;

  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 300px;
    height: 300px;
    margin-top: calc((100/1024)*100%);
  }

  .title {
    margin-top: calc((74/1024)*100%);
    font-weight: 600;
    font-size: 24px;
    line-height: 150%;
    text-align: center;
    letter-spacing: 0.01em;
    color: #0D1831;
  }

  .desc {
    margin-left: calc((91/1440)*100%);
    margin-right: calc((91/1440)*100%);

    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    line-height: 170%;
    text-align: center;
    color: #6E7483;
  } 
  
}

.right-content {
  background-color:#FFFFFF;

  .content-container {
    display: flex;
    flex-direction: column;
    margin-left: calc((105/1440)*100%);
    margin-right: calc((100/1440)*100%);

    .input {
  
      width: auto;
      height: 35px;
      background: #FFFFFF;
      border: 1px solid #E0E0E0;
      border-radius: 8px;
  
      display: flex;
      flex-direction: row;
      align-items: center;
  
      p {
        margin-left: 0px;
        margin-bottom: 0px;
        font-size: 16px;
      }
  
      i {
        margin-left: 20px;
        margin-right: 16px;
        width: 14px;
        height: 14px;
      }
  
      input {
        border: 0px;
      }
  
    }

    .login-button {
      background: linear-gradient(330.93deg, #547AD1 12.57%, #6BA0FE 87.39%);
      border-radius: 8px;
      border: 0px;
      width: 100%;
      height: 40px;
      margin-top: 10px;
      margin-bottom: 10px;
  
      color: #FFFFFF;
      font-style: normal;
      font-size: 20px;
      line-height: 28px;
    }
  
    .not-registered-msg {
      align-self: center;
      font-style: normal;
      font-weight: normal;
      font-size: 14px;
      text-align: center;
      color: #6E7483;
  
      .create-account-msg {
        color: #4168c2;
      }
    }
  
    img {
      //margin-top: calc((75/1024)*100%);
      margin-bottom: calc((100/1024)*100%);

      align-self: flex-end;
    }
  
    h1 {  
      font-style: normal;
      font-weight: bold;
      font-size: 32px;
      line-height: 150%;
      letter-spacing: 0.02em;
      color: #0D1831;
    }
  }
}