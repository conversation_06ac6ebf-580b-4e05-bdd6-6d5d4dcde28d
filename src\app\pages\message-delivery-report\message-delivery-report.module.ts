import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MessageDeliveryReportRoutingModule } from './message-delivery-report-routing.module';
import { ListMessageDeliveryReportComponent } from './list-message-delivery-report/list-message-delivery-report.component';
import { MsFormModule } from 'app/shared/components/ms-form/ms-form.module';
import { SharedModule } from 'app/shared/shared.module';


@NgModule({
  declarations: [ListMessageDeliveryReportComponent],
  imports: [
    CommonModule,
    MessageDeliveryReportRoutingModule,
    MsFormModule,
    SharedModule
  ]
})
export class MessageDeliveryReportModule { }
