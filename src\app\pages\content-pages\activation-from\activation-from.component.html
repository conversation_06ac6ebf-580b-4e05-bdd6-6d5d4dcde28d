<!--Registration Page Starts-->
<nav class="navbar navbar-light bg-white">
    <div class="text-center justify-content-center">
      <a class="logo-text" href="">
        <img src="./assets/img/logo-eSign.png" alt="eSign logo" class="logo-img" style="width: 148px; height: 45px" />
      </a>
    </div>
  </nav>
  
  <section id="regestration">
    <div class="row">
      <div class="col-12 d-flex align-items-center justify-content-center">
        <div class="card overflow-hidden" style="width: 480px;">
          <div class="card-content">
            <div class="card-body auth-img">
              <div class="row m-0">
  
                <div class="col-lg-12 col-md-12 px-4 py-3">
                  <h4 class="card-title mb-2 register-title" translate>Activation Form</h4>


                  <form [formGroup]="activationForm">
  
                    <div class="form-group">
                      <label for="email" class="form-title">Email <span class="mandatory text-danger">*</span></label>
                      <input type="email" formControlName="email" id="email" class="form-control mb-2" placeholder="Email" 
                            
                      required disabled>
                    </div>
  
                    <div class="form-group">
                      <label for="name" class="form-title" translate>Full Name <span class="mandatory text-danger">*</span></label>
                      <input type="text" formControlName="name" id="name" class="form-control mb-2" placeholder="Nama Lengkap" 
                             
                      required disabled>
                    </div>

                    <!-- password -->
                    <div *ngIf="!isNoPassword" class="form-group">
                        <label for="name" class="form-title" translate>Password<span class="mandatory text-danger">*</span></label>
                        <div class="input">
                          <i class="ft-lock"></i>
                          <input [type]="isShowPassword ? 'text' : 'password'" formControlName="password" class="form-control" [placeholder]="'Password' | translate"
                                 required>
                          <i style="cursor: pointer;" [ngClass]="{'ft-eye-off': !isShowPassword, 'ft-eye': isShowPassword}" (click)="toggleIsShowPassword()"></i>
                        </div>
                        <div *ngFor="let validation of validationMessages.password">
                          <div *ngIf="activationForm.get('password').dirty && activationForm.get('password').hasError(validation.type)"
                               class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                        </div>
                      </div>

                      <!-- retype password -->
                      <div *ngIf="!isNoPassword" class="form-group">
                        <label for="name" class="form-title" translate>Retype Password<span class="mandatory text-danger">*</span></label>
                        <div class="input">
                          <i class="ft-lock"></i>
                          <input [type]="isShowConfirmPassword ? 'text' : 'password'" formControlName="confirmPassword" class="form-control" [placeholder]="'Retype Password' | translate"
                                 required>
                          <i style="cursor: pointer;" [ngClass]="{'ft-eye-off': !isShowConfirmPassword, 'ft-eye': isShowConfirmPassword}" (click)="toggleIsShowConfirmPassword()"></i>
                        </div>
                        <div *ngFor="let validation of validationMessages.confirmPassword">
                          <div *ngIf="activationForm.get('confirmPassword').dirty && activationForm.get('confirmPassword').hasError(validation.type)"
                               class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                        </div>
                        <div *ngIf="activationForm.errors?.differentPassword"
                        class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{confimPasswordMessage}}</div>
                      </div>

                      <!-- otp media option -->
                    <div class="form-group">
                      <label for="gender" class="form-title" translate>OTP Delivery Medium<span class="mandatory text-danger">*</span></label>
                      <div class="row">
                        <div class="col-6" *ngIf="isSms">
                          <span class="border rounded border-primary d-block">
                            <div class="form-check form-check-inline align-middle p-3">
                              <input class="form-check-input" type="radio" id="sms" formControlName="sendMedia" value="SMS">
                              <label class="form-check-label label-radio-button" for="sms" translate>SMS</label>
                            </div>
                          </span>
                        </div>
                        <div class="col-6" *ngIf="isWa">
                          <span class="border rounded border-primary d-block">
                            <div class="form-check form-check-inline align-middle p-3">
                              <input class="form-check-input" type="radio" id="wa" formControlName="sendMedia" value="WA">
                              <label class="form-check-label label-radio-button" for="wa" translate>WhatsApp</label>
                            </div>
                          </span>
                        </div>
                      </div>
  
                      <!-- <ng-select formControlName="gender" [items]="genders" bindValue="id" id="gender"
                        bindLabel="name" placeholder="Pilih Jenis Kelamin" [(ngModel)]="selectedGender">
                      </ng-select> -->
                      <div *ngFor="let validation of validationMessages.gender">
                        <div *ngIf="registerForm.get('sendMedia').dirty && registerForm.get('sendMedia').hasError(validation.type)"
                             class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                      </div>
                    </div>

                    <div class="row">
                        <div class="col-12 text-center">
                          <button *ngIf="!isNoPassword" class="btn btn-light" style="margin-right: 8px" (click)="doReset()" translate>Reset</button>
                          <button class="btn btn-info" type="submit" [disabled]="activationForm.invalid"  (click)="onProcess()" translate>Process</button>
                        </div>
                    </div>
                  </form>

                </div>
  
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  