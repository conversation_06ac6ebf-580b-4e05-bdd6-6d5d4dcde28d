import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseRequest } from 'app/model/api/base.request';
import { GetLovRequest } from 'app/model/api/get.lov.request';
import { ListBalanceTenantRequest } from 'app/model/api/listbalancetenant.request';
import { UpdateBalanceTenant } from 'app/model/api/update.balanceTenant.request';
import { BalanceTenant } from 'app/model/balanceTenant';
import { ListBalanceVendoroftenantRequest } from 'app/model/balancevendoroftenant.list.request';
import { DataService } from 'app/services/api/data.service';
import { FormModel } from 'app/shared/components/ms-form/models';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { VendorListRequest } from 'app/shared/dto/data/vendor-list.request';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-service-balance',
  templateUrl: './service-balance.component.html',
  styleUrls: ['./service-balance.component.scss']
})
export class ServiceBalanceComponent implements OnInit {

  BalanceTenantForm: FormGroup;
  mForm: FormModel<any>;

  vendors = [];
  balances = [];
  balanceTenants = [];
  state: any;
  routeData: any;

  constructor(private activatedRoute: ActivatedRoute, private router: Router, private dataService: DataService, private location: Location,
    private toastrService: ToastrService, private http: HttpClient, private spinner: NgxSpinnerService, private global: GlobalService) 
  { 
    this.routeData = this.activatedRoute.snapshot.data;

    this.activatedRoute.queryParams.subscribe(() => {
      this.state = this.router.getCurrentNavigation().extras.state;
      console.log('state', this.state);
    })

    this.getVendors();
    this.getBalances();
    this.getBalanceTenants();
    this.spinner.hide();
  }

  ngOnInit(): void {
  }

  getVendors() {
    this.spinner.show();
    const request = new BaseRequest;
    request.audit = this.global.audit;
    this.dataService.getAllVendorList(request, URLConstant.GetAllVendorList).subscribe(
      (response) => {
        if (response.status.code === 0) {
          this.vendors = response.vendorList;
        } else {
          this.toastrService.error(response.status.message);
        }
      }
    );
  }

  getBalances() {
    const request = new GetLovRequest();
    request.lovGroup = 'BALANCE_TYPE';
    this.http.post(URLConstant.GetLov, request).subscribe(
      (response) => {
        if (response['status']['code'] === 0) {
          this.balances = response['lovList'];
        } else {
          this.toastrService.error(response['status']['message']);
        }
      }
    )
  }

  getBalanceTenants() {
    const request = new ListBalanceTenantRequest();
    request.tenantCode = this.state.tenantCode;
    localStorage.setItem('tenantCode', this.state.tenantCode);
    request.audit = this.global.audit;
    this.http.post(URLConstant.GetListBalanceTenant, request).subscribe(
      (response) => {
        if (response['status']['code'] === 0) {
          this.balanceTenants = response['listBalanceTenant'];
        } else {
          this.toastrService.error(response['status']['message']);
        }
      }
    )
  }

  goBack() {
    this.location.back();
  }

  saveBalanceTenant() {
    var listBalanceTenant = [] as BalanceTenant[];
    this.balances.forEach(
      (balance) => {
        this.vendors.forEach(
          (vendor) => {
            const id = balance['code'] + '@' + vendor['code'];
            const active = (<HTMLInputElement> document.getElementById(id)).checked;
            if (active) {
              var bean = new BalanceTenant();
              bean.balanceTypeCode = balance['code'];
              bean.balanceTypeName = balance['description'];
              bean.isActive = '1';
              bean.vendorCode = vendor['code'];
              bean.vendorName = vendor['name'];
              listBalanceTenant.push(bean);
            }
          }
        )
      }
    )

    const request = new UpdateBalanceTenant();
    request.listBalanceTenant = listBalanceTenant;
    request.tenantCode = this.state.tenantCode;
    localStorage.setItem('tenantCode', this.state.tenantCode);
    request.audit = this.global.audit;
    this.http.post(URLConstant.UpdateListBalanceTenant, request).subscribe(
      (response) => {
        if (response['status']['code'] != 0) {
          this.toastrService.error(response['status']['message']);
        } else {
          this.toastrService.success('Update Balance Tenant berhasil.')
        }
      }
    )

    this.location.back();
  }
  
}
