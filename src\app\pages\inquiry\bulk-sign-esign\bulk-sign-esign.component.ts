import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { SignBalanceAvailabilityRequest } from 'app/model/api/sign.balance.availability.request';
import { AuditContext } from 'app/model/audit.context';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { SignerSigningVerificationComponent } from '../modal/signer-signing-verification/signer-signing-verification.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Document } from 'app/model/document';
import { GetSignerDetailRequest } from 'app/model/api/get-signer-detail.request';
import { UserProfile } from 'app/model/user-profile';
import { DocumentService } from 'app/services/api/document.service';
import { CountlyService } from 'app/services/api/countly.service';
import { waitForAsync } from '@angular/core/testing';
import { NgxSpinnerService } from 'ngx-spinner';
import { Observable } from 'rxjs';
import { GetSignerDetailResponse } from 'app/model/api/get-signer-detail.response';
@Component({
  selector: 'app-bulk-sign-esign',
  templateUrl: './bulk-sign-esign.component.html',
  styleUrls: ['./bulk-sign-esign.component.scss']
})
export class BulkSignEsignComponent implements OnInit {
  documents: Document[];
  email: any;
  phoneNo: any;
  msg: string;
  tenantCode: string;
  maxLivenessFaceCompareAttempt: any;
  isNoPassword: any;

  constructor(private router: Router, private activeRoute: ActivatedRoute, private global: GlobalService,private toastrService: ToastrService,
    private http: HttpClient,private modalService: NgbModal, private documentService: DocumentService, private countlyService: CountlyService,
    private spinner: NgxSpinnerService
    ) { 

    if (this.router.getCurrentNavigation().extras.state) {
      const state = this.router.getCurrentNavigation().extras.state;
      this.documents = state.data;
      console.log('test',this.documents);

      if(state.msg){
        this.msg = state.msg;
      }
      if(state.tenantCode){
        this.tenantCode = state.tenantCode;
      }
    }

  }

  ngOnInit(): void {
    this.countlyService.initiate();
  }

  delay(ms: number) {
    return new Promise( resolve => setTimeout(resolve, ms) );
  }

  getSignBalance() {
    this.getDetailSigner().subscribe((response) => {
      if (response['status']['code'] !== 0 ) {
        this.toastrService.error(response['status']['message']);
      } else {
        this.email = response['email'];
        this.phoneNo = response['phoneNo'];
        this.maxLivenessFaceCompareAttempt = response['maxLivenessFaceCompareAttempt'];
        this.isNoPassword = response['isNoPassword']

        const paymentList: SignBalanceAvailabilityRequest = new SignBalanceAvailabilityRequest();
        paymentList.listDocumentId = [];
        paymentList.audit = new AuditContext();
        paymentList.audit.callerId = this.email;
        let url;
        if(this.msg && this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
          paymentList.msg = this.msg;
          paymentList.vendorCode = this.documents[0].vendorCode;
          paymentList.tenantCode = this.tenantCode;
          url = URLConstant.GetSignBalanceAvailabilityEmbedV2;
        }
        else{
          paymentList.audit.callerId = this.global.user.loginId;
          paymentList.tenantCode = this.global.user.role.tenantCode;
          paymentList.vendorCode = this.documents[0].vendorCode;
          url = URLConstant.GetSignBalanceAvailability;
        }

        for (let i = 0; i < this.documents.length; i++) {
          paymentList.listDocumentId.push(this.documents[i].documentId);
        }

        this.http.post(url, paymentList).subscribe(
          (response) => {
            if (response['status']['code'] !== 0) {
            //  this.toastrService.error(response['status']['message']);
            } else {
                const modal = this.modalService.open(SignerSigningVerificationComponent, { backdrop: 'static', keyboard: false, size: 'l' });
                modal.componentInstance.email = this.email;
                modal.componentInstance.phoneNo = this.phoneNo;
                modal.componentInstance.vendorCode = this.documents[0].vendorCode;
                modal.componentInstance.documents = this.documents;
                modal.componentInstance.tenantCode = this.tenantCode;
                modal.componentInstance.msg = this.msg;
                modal.componentInstance.maxLivenessFaceCompareAttempt = this.maxLivenessFaceCompareAttempt;
                modal.componentInstance.isNoPassword = this.isNoPassword;
                this.countlyService.trackPageViewData('SignerSigningVerificationComponent');

              }
          }
        );
      }
    });
  }

getDetailSigner() {
    const detail: GetSignerDetailRequest = new GetSignerDetailRequest();
    detail.audit = new AuditContext();
    let url: string;
    
    if (this.msg && this.router.url.includes('/embed/') && this.router.url.includes('/V2/')) {

      detail.msg = this.msg;
      detail.vendorCode = this.documents[0].vendorCode;
      detail.tenantCode = this.tenantCode;
      url = URLConstant.getSignerDetailEmbedV2;

    } else {

      detail.audit.callerId = this.global.user.loginId;
      detail.vendorCode = this.documents[0].vendorCode;
      detail.email = this.global.user.loginId;
      url = URLConstant.getSignerDetail;
      
    }
    
    return this.documentService.getDetailSigner(url, detail);
  }
}
