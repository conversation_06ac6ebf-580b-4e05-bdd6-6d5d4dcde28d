import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SettingSequentialSignerComponent } from './setting-sequential-signer.component';

describe('SettingSequentialSignerComponent', () => {
  let component: SettingSequentialSignerComponent;
  let fixture: ComponentFixture<SettingSequentialSignerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SettingSequentialSignerComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SettingSequentialSignerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
