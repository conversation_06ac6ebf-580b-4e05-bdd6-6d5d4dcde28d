import { Location } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { ViewDocumentRequest } from 'app/model/api/view.document.request';
import { ViewDocumentResponse } from 'app/model/api/view.document.response';
import { CountlyService } from 'app/services/api/countly.service';
import { Act } from 'app/shared/components/msx-card-datatable/enums/act'; 
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { data } from 'app/shared/data/smart-data-table';
import { DeviceDetectorService } from 'ngx-device-detector';
import { SignerComponent } from '../modal/signer/signer.component';
import { HttpClient } from '@angular/common/http';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Vendor } from 'app/shared/constant/vendor';
import { SignDocumentRequest } from 'app/model/api/sign.document.request';
import { SignDocumentResponse } from 'app/model/api/sign.document.response';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { Document } from 'app/model/document';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-view-document-inquiry',
  templateUrl: './view-document-inquiry.component.html',
  styleUrls: ['./view-document-inquiry.component.scss']
})
export class ViewDocumentInquiryComponent implements OnInit {
  @ViewChild('pdfViewerOnDemand') pdfViewerOnDemand;

  downloadSrc:string = CommonConstant.ICON_SRC_DOWNLOAD;
  userSrc:string = CommonConstant.ICON_SRC_USER;
  signButtonLabel:string = CommonConstant.LABEL_SIGNING_DOCUMENT;

  private pathSrc: string;
  documents: Document[] = [];

  public state: any;
  public pdfBase64: string;
  public refNumber: string;
  public url;
  isMobile: boolean = false;
  isSign: boolean = false;
  roleCode: string;
  pathsrc: string = '';
  msg: string;
  tenantCode: string;
  isOffice: string;
  swal = swalFunction;

  //pdfviewer
  pdfBuffer: ArrayBuffer; 
  pdfPageNumber = 1;  

  roleBranchManager:string = CommonConstant.BRANCH_MANAGER;
  roleCust:string = CommonConstant.CUSTOMER;

  ActionCustMobile = [
    { 
      type: Act.Download,
      mobileMode: false,
      cardAction: false,
      iconSrc: CommonConstant.ICON_SRC_DOWNLOAD,
      descr: 'Download'
    },
    {  
      type: Act.ViewSigner,
      descr: 'Signer',
      mobileMode: false,
      cardAction: false,
      iconSrc: CommonConstant.ICON_SRC_USER
    }
  ]

  constructor(private location: Location, private router: Router,  private global: GlobalService,
    private cdr: ChangeDetectorRef, private spinner: NgxSpinnerService,
    private deviceService: DeviceDetectorService, private http: HttpClient, private modalService: NgbModal,
    private countlyService: CountlyService, private activeRoute: ActivatedRoute, private toastrService: ToastrService,) {
    this.state = this.router.getCurrentNavigation().extras.state;
    this.pdfBase64 = this.state.pdfBase64;
    this.refNumber = this.state.refNumber;
    this.isOffice = this.state.isOffice;
    this.tenantCode = this.state.tenantCode;
    this.msg = this.state.msg;
    this.roleCode = this.global.user.role.roleCode; 
    this.pathsrc = this.global.user.pathSrc ?? this.pathsrc;
    this.isSign = this.state.isSign ?? false; 

    const snapshot = this.activeRoute.snapshot.data;
    this.pathSrc = snapshot.path;

    if (this.refNumber == null){
      this.refNumber = this.state.nomorDokumen
    }

    if (deviceService.isMobile()) {
      this.isMobile = true;
    }
  }

  ngOnInit(): void {
    this.countlyService.initiate();
    const blob = this.b64toBlob(this.pdfBase64,'application/pdf');
    console.log(blob.type); // should print "application/pdf"
    const objectUrl = window.URL.createObjectURL(blob);
    this.url = `${objectUrl}#toolbar=0`;

    this.base64ToArrayBuffer(this.pdfBase64);
    this.cdr.detectChanges();
  }

  b64toBlob(b64Data, contentType='', sliceSize=512) {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];
  
    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);
  
      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
  
      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
  
    const blob = new Blob(byteArrays, {type: contentType});
    return blob;
  }

  doBack() {
    this.location.back();
  }

  onClickAction(act:any){
    switch (act.type) {
      case Act.ViewSigner:
        return this.openSignerListModal(this.state);

      case Act.Download:
        return this.gotoViewOrDownload(this.state, true); 
         
    }
  }

  openSignerListModal(data: any) {
      const modal = this.modalService.open(SignerComponent, { size: 'lg' });
      modal.componentInstance.documentId = data.documentId;
      modal.componentInstance.isOffice = this.isOffice;
      if (this.msg) {
        modal.componentInstance.msg = this.msg;
      }
      if (this.tenantCode) {
        modal.componentInstance.tenantCode = this.tenantCode;
      }
  }

  gotoViewOrDownload(data: any, download: boolean) {
      console.log('Data', data);
      const request: ViewDocumentRequest = new ViewDocumentRequest();
      request.documentId = data.documentId;
      let url;
      if (this.msg && this.router.url.includes('/embed/') && !this.router.url.includes('/V2/')){
        request.msg = this.msg;
        url = URLConstant.ViewDocumentEmbed;
      } else if(this.msg && this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
        request.msg = this.msg;
        request.tenantCode = this.tenantCode;
        url =URLConstant.ViewDocumentEmbedV2;
      } 
      else{
        url =URLConstant.ViewDocument;
      }
      
      this.http.post<ViewDocumentResponse>(url, request).subscribe(
        (response) => {
          if (response.status.code === 0) { 
              const downloadLink = document.createElement('a');
              const fileName = data.docTemplateName + '.pdf';
  
              downloadLink.href = `data:application/pdf;base64,${response.pdfBase64}`;
              downloadLink.download = fileName;
              downloadLink.click(); 
          }
        }
      );
    }

  onClickSign() {
    if (this.state.vendorCode === Vendor.VIDA) {
      return this.gotoSignVida(this.state);
    }
    
    if (this.state.vendorCode === Vendor.PRIVY) {
      return this.gotoSignPrivy(this.state);
    }
    
      // Uncomment for error handling if needed:
      // if (this.msg && this.router.url.includes('/V2/')) {
      //   return this.swal.Error('Tidak dapat melakukan tanda tangan dokumen Digisign!');
      // }
    
    return this.gotoSign(this.state);
  }

  gotoSign(data: any) {
    const extras: NavigationExtras = {
      state: {
        refNumber: data.refNumber
      },
      queryParams: {
        id: data.documentId
      }
    };
    if (this.pathSrc === CommonConstant.PATH_SRC_EMBED ) {
      this.router.navigate([PathConstant.EMBED_SIGNATURE], extras);
    }  else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2){
      this.router.navigate([PathConstant.EMBED_V2_SIGNATURE], extras);
      }
    else {
      this.router.navigate([PathConstant.SIGNATURE], extras);
    }
  }
      
  async gotoSignVida(data) {
      const request: SignDocumentRequest = new SignDocumentRequest();
        let signDocUrl;
        request.documentId = data.documentId;
  
        if (this.msg && !this.router.url.includes('/V2/')) {
          request.msg = this.msg;
          signDocUrl = URLConstant.SignDocumentEmbed;
        } 
        else if(this.msg && this.router.url.includes('/V2/')){
          request.msg = this.msg;
          request.tenantCode = this.tenantCode;
          signDocUrl = URLConstant.SignDocumentEmbedV2;
        }
        else {
          request.email = this.global.user.loginId;
          signDocUrl = URLConstant.SignDocument;
        }
  
         //check send document status
        // const checkDocStatus = await this.checkStatusDocument.validate(data.documentId, this.global.msg, this.router.url, data.refNumber);
        // if (checkDocStatus.documentSendStatus === '1') {
          await this.http.post<SignDocumentResponse>(signDocUrl, request).toPromise().then(
            async (response) => {
              if (response.status.code === 0 && response.vendorCode === Vendor.VIDA) {
                
                if (response.docs != null && response.docs.length !== 0) {
                  const extras: NavigationExtras = {
                    state: {
                      data: response.docs,
                      vendorCode:response.vendorCode,
                      msg: this.msg,
                      tenantCode: this.tenantCode,
                    }
                  };
                  this.navigateSignVida(extras);
                } else{
                  return this.goToBulkSign(data);
                }
              }  
  
            }, (err) => {
              this.toastrService.error(err.message, null, { positionClass: 'toast-top-right' });
            }
          );
        //}
  }

  navigateSignVida(extras) {
    if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      this.router.navigate([PathConstant.EMBED_BULK_SIGN], extras);
    } 
    else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
      this.router.navigate([PathConstant.EMBED_V2_BULK_SIGN], extras);
    } else {
      this.router.navigate([PathConstant.INQUIRY_BULK_SIGN], extras);
    }
  }
  
  async gotoSignPrivy(data) {
      const request: SignDocumentRequest = new SignDocumentRequest();
        let signDocUrl;
        request.documentId = data.documentId;
  
        if (this.msg && !this.router.url.includes('/V2/')) {
          request.msg = this.msg;
          signDocUrl = URLConstant.SignDocumentEmbed;
        } 
        else if(this.msg && this.router.url.includes('/V2/')){
          request.msg = this.msg;
          request.tenantCode = this.tenantCode;
          signDocUrl = URLConstant.SignDocumentEmbedV2;
        }
        else {
          request.email = this.global.user.loginId;
          signDocUrl = URLConstant.SignDocument;
        }
  
         //check send document status
        // const checkDocStatus = await this.checkStatusDocument.validate(data.documentId, this.global.msg, this.router.url, data.refNumber);
        // if (checkDocStatus.documentSendStatus === '1') {
          await this.http.post<SignDocumentResponse>(signDocUrl, request).toPromise().then(
            async (response) => {
              if (response.status.code === 0 && response.vendorCode === Vendor.PRIVY) {
                
                if (response.docs != null && response.docs.length !== 0) {
                  const extras: NavigationExtras = {
                    state: {
                      data: response.docs,
                      vendorCode:response.vendorCode,
                      msg: this.msg,
                      tenantCode: this.tenantCode,
                    }
                  };
                  this.navigateSignPrivy(extras);
                } else{
                  return this.goToBulkSign(data);
                }
              }  
  
            }, (err) => {
              this.toastrService.error(err.message, null, { positionClass: 'toast-top-right' });
            }
          );
        //}
  }

  navigateSignPrivy(extras: any) {
    if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      this.router.navigate([PathConstant.EMBED_BULK_SIGN], extras);
    }
    else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
      this.router.navigate([PathConstant.EMBED_V2_BULK_SIGN], extras);
    } else {
      this.router.navigate([PathConstant.INQUIRY_BULK_SIGN], extras);
    }
  }
  
  goToBulkSign(datas) {
      //penjagaan untuk jika pilih dokumen dengan beda vendor, digi bersama vida
      let totalDigi = 0;
      let totalVida = 0;
      let totalPrivy = 0;
      let totalTknAj = 0;
      if(this.documents.length === 0){
        this.documents.push(datas);
      }
  
      for(let j = 0; j < this.documents.length; j++) {
        if(this.documents[j].vendorCode === Vendor.DigiSign){
          totalDigi ++;
        } else if(this.documents[j].vendorCode === Vendor.VIDA){
          totalVida ++;
        } else if(this.documents[j].vendorCode === Vendor.PRIVY){
          totalPrivy ++;
        } else if(this.documents[j].vendorCode === Vendor.TekenAja){
          totalTknAj ++;
        }
  
        if (this.documents[j].isCurrentTopPriority === '0') {
          return this.swal.Error('Terdapat Dokumen dengan urutan prioritas yang lebih tinggi yang perlu di tanda tangan terlebih dahulu. Silahkan pilih dokumen ulang.');
        }
      }
    
       if(totalDigi !== this.documents.length && totalVida !== this.documents.length && totalPrivy !== this.documents.length && totalTknAj !== this.documents.length){
         return this.swal.Error('Tidak dapat melakukan bulksign dengan vendor lebih dari 1! Silahkan pilih dokumen lain.');
       } 
      //  else if(totalDigi === this.documents.length && this.msg && this.router.url.includes('/V2/')){
      //    return this.swal.Error('Tidak dapat melakukan tanda tangan dokumen Digisign !');
      //  }
  
      
      //this.documents = datas;
      const extras: NavigationExtras = {
        state: {
          msg: this.msg,
          tenantCode: this.tenantCode,
          data: this.documents,
          isFromView: true,
        }
      };
  
      // START: Workaround bulksign page kosong
      if (this.documents.length === 1 && this.documents[0].vendorCode !== Vendor.VIDA && this.documents[0].vendorCode !== Vendor.PRIVY) {
  
        const singleSignExtras: NavigationExtras = {
          state: {
            refNumber: this.documents[0].refNumber,
            skipBulksignCheck: '1'
          },
          queryParams: {
            id: this.documents[0].documentId
          }
        };
  
        if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
          this.router.navigate([PathConstant.EMBED_SIGNATURE], singleSignExtras);
        } else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2){
          this.router.navigate([PathConstant.EMBED_V2_BULK_SIGN], extras);
        }
        else {
          console.log('Single sign, skip bulksign check');
          this.router.navigate([PathConstant.SIGNATURE], singleSignExtras);
        }
        return;
      }
      // END: Workaround bulksign page kosong
  
      if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
        this.router.navigate([PathConstant.EMBED_BULK_SIGN], extras);
      } 
      else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
        this.router.navigate([PathConstant.EMBED_V2_BULK_SIGN], extras);
      }
      else {
        this.router.navigate([PathConstant.INQUIRY_BULK_SIGN], extras);
      }
  }  

  base64ToArrayBuffer(base64) {
    console.log('x', base64);
    const binary_string =  window.atob(base64);
    const len = binary_string.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++)        {
        bytes[i] = binary_string.charCodeAt(i);
    }

    this.pdfBuffer = bytes.buffer;
    console.log('Pdf Buffer empty', !this.pdfBuffer);
    this.cdr.detectChanges();
    this.spinner.hide();
  }
 
  
}
