main-body {
    display: flex;
  }

.checkbox {
    width: 30px;
    height: 30px;
    margin: 10px 20px 0 0;
    appearance: none; /* menonaktifkan gaya bawaan */
//   -webkit-appearance: none; /* menonaktifkan gaya bawaan pada browser Webkit */
//   -moz-appearance: none; /* menonaktifkan gaya bawaan pada browser Mozilla */
  border: 2px solid #ccc; /* menambahkan border */
  background-color: #fff; /* menambahkan background color */
  outline: none; /* menghilangkan outline saat focus */
  }

  .checkbox:checked {
    background-color: turquoise;
    border-color: turquoise;
  }

  .checkbox:checked::after {
    content: '\2713'; /* menambahkan karakter ceklis Unicode */
    color: #16c60c; /* warna karakter ceklis */
    font-size: 30px; /* ukuran font karakter ceklis */
    font-weight: 1000; /* membuat karakter ceklis menjadi lebih tebal */
    position: absolute; /* menempatkan karakter ceklis di atas kotak centang */
    top: 50%; /* mengatur posisi vertikal karakter ceklis */
    left: 50%; /* mengatur posisi horizontal karakter ceklis */
    transform: translate(-50%, -50%); /* menggeser posisi karakter ceklis agar berada di tengah-tengah kotak centang */
  }

  .checkbox:disabled + label::before {
    cursor: not-allowed;
    opacity: 0.5;
  }
  .checkbox:read-only + label::before {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .navbar {
    margin-bottom: 0 !important;
  }
  
  #navtop {
    display: flex;
    justify-content: flex-start;
    margin-left: 0px;
    font-size: 50px;
  }

  .navbar-brand {
    font-size: 24px;
  }

  app-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: #f5f5f5;
    text-align: center;
  }

  #prosesBulk {
    font-size: 1.5rem;
    width: 100%;
    margin-right: 100px; /* atur margin menjadi auto agar tombol menjadi di tengah */
  }
  .navbars {
    width:100%; 
    padding-top:0px; 
    padding-left: 0px;
    padding-right: 0px; 
    padding-bottom: 0px;
  }
  
  
  
  