import { Component, Input, OnInit } from '@angular/core';
import { InquiryAuditTrail } from 'app/model/InquiryAuditTrail';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { BehaviorSubject } from 'rxjs';
import { AuditTrailDetailTable } from './view/audit-trail-detail.view';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modal-audit-trail-detail',
  templateUrl: './modal-audit-trail-detail.component.html',
  styleUrls: ['./modal-audit-trail-detail.component.scss']
})

export class ModalAuditTrailDetailComponent implements OnInit {

  @Input() data: any;

  data2:any;

  auditTrailDetailTable = AuditTrailDetailTable;

  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);

  constructor(public activeModal: NgbActiveModal) { }

  ngOnInit(): void {
    console.log('On click', this.data);
    // this.datasource.next(this.data);

    this.data2 = this.convertToAuditTrailObject(this.data);

    console.log("converted data", this.data2);

    this.datasource.next(this.data2);
  }

  convertToAuditTrailObject(data: any) {
    return {
      status: {
        code: 0
      },
      listinquiryAuditTrailSignProcess: [
        {
          notificationType : data.notificationType,
          notificationVendor : data.notificationVendor,
          sendingPoint : data.sendingPoint,
          otpCode : data.otpCode,
          notes : data.notes
        }
      ]
    };
  }
  

  // datasource: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);

  



}
