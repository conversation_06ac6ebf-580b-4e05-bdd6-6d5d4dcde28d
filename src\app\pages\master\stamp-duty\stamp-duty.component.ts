import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { StampDutyService } from 'app/services/api/stamp-duty.service';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { DownloadStampDutyReportRequest } from 'app/shared/dto/stamp-duty/download-sdt-report.request';
import { MateraiDetailComponent } from './materai-detail/materai-detail.component';
import { StampDutyListView } from './view/stamp-duty-list.view';
import { saveAs } from 'file-saver';

@Component({
  selector: 'app-stamp-duty',
  templateUrl: './stamp-duty.component.html',
  styleUrls: ['./stamp-duty.component.scss']
})
export class StampDutyComponent implements OnInit {

  view: MsxView;
  buttonList: Button[];
  routeData: any;
  serviceUrl = URLConstant.ListStampDuty;

  constructor(private activatedRoute: ActivatedRoute, private router: Router, private modalService: NgbModal,
    private stampDutyService: StampDutyService) {
    this.routeData = this.activatedRoute.snapshot.data;
  }

  ngOnInit(): void {
    this.view = StampDutyListView;
  }

  onBtnClickListener(event) {
    switch (event['name']) {
      case this.buttonList[0].name:
        this.router.navigate([PathConstant.REVERSAL_TOPUP_STAMP_DUTY]);
        break;
      case this.buttonList[1].name:
        this.router.navigate([PathConstant.TOPUP_STAMP_DUTY]);
        break;
      default:
        break;
    }
  }

  onItemClickListener(result: any) {
    const modal = this.modalService.open(MateraiDetailComponent, { backdrop: 'static', keyboard: false, size: 'xl' });
    modal.componentInstance.idStampDuty = result['data']['idStampDuty'];
  }

  private dateToString(dateObject: any) {
    return (dateObject['year']) ? dateObject['year'] + '-' + dateObject['month'] + '-' + dateObject['day'] : dateObject;
  }

  downloadStampDutyReport(params) {
    console.log('Download SDT Param', params);
    const request = new DownloadStampDutyReportRequest();
    request.businessLineCode = params['businessLineCode'];
    request.invoiceNo = params['invoiceNo'];
    request.officeCode = params['officeCode'];
    request.regionCode = params['regionCode'];
    request.stampDutyNo = params['stampDutyNo'];
    request.stampDutyStatus = params['stampDutyStatus'];
    request.stampDutyUsedDtEnd = this.dateToString(params['stampDutyUsedDtEnd']);
    request.stampDutyUsedDtStart = this.dateToString(params['stampDutyUsedDtStart']);

    this.stampDutyService.downloadStampDutyReport(request).subscribe(
      response => {
        if (response.status.code === 0) {
          const blob = this.b64toBlob(response.base64ExcelReport);
          saveAs(blob, response.filename);
        }
      }
    )
  }

  b64toBlob(b64Data, contentType = '', sliceSize = 512) {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }

}
