import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PsreSettingRoutingModule } from './psre-setting-routing.module';
import { ListPsreSettingComponent } from './list-psre-setting/list-psre-setting.component';
import { MsFormModule } from 'app/shared/components/ms-form/ms-form.module';
import { SharedModule } from 'app/shared/shared.module';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { EditSettingPsreComponent } from './edit-setting-psre/edit-setting-psre.component';


@NgModule({
  declarations: [ListPsreSettingComponent, EditSettingPsreComponent],
  imports: [
    CommonModule,
    PsreSettingRoutingModule,
    MsFormModule,
    SharedModule,
    MsFormModule,
    ReactiveFormsModule,
    NgSelectModule,
  ]
})
export class PsreSettingModule { }
