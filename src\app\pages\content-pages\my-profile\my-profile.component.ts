import { Component, OnInit } from '@angular/core';
import { MyProfileService } from 'app/services/api/my-profile.service';
import { MyProfileRequest } from 'app/shared/dto/my-profile/my-profile-s.request';
import { GlobalService } from 'app/shared/data/global.service';
import * as internal from 'assert';


@Component({
  selector: 'app-my-profile',
  templateUrl: './my-profile.component.html',
  styleUrls: ['./my-profile.component.scss']
})
export class MyProfileComponent implements OnInit {
  template: any;
  name: string;
  beans: any;
  beanobj: any[];
  label: any[];
  constructor(private profile:MyProfileService, private global: GlobalService) {}

  ngOnInit(): void {
    this.setProfile();
    this.beanobj = [];
    this.label = [];
  }
  
  getTags(tags: string): string[] {
    return tags.split(',');
  }
  setProfile(){
    const request = new MyProfileRequest
    request.tenantCode = this.global.user.role.tenantCode;
    request.email = this.global.audit.callerId;

    this.profile.getMyProfile(request).subscribe(
      (response) => {
        console.log('response: ', response);
        if (response.status.code === 0) {  
          console.warn(response);
          this.name = response['nama'];

          for(let i = 0; i<response.bean.length; i++){
            //label nama 
            this.label.push(
              { emaillabel:"Email", 
                phonelabel:"Phone Number", 
                vendorlabel:"Vendor"  }
            )

            //hasil response
            this.beans= response.bean[i];
            this.beanobj.push(
              {  email: this.beans.email, 
                 phoneNumber: this.beans.phoneNumber, 
                 vendor:this.beans.vendor  }
            )
          }
          
        }
      }
    )
  }
}
