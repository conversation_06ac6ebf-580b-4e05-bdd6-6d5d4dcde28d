import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BaseResponse } from 'app/model/api/base.response';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { SentOtpSigningVerificationRequest } from 'app/shared/dto/otp/sent-otp-signing-verification.request';
import { VerifyOtpBySMSRequest } from 'app/shared/dto/otp/verify-otp-by-sms.request';
import { environment } from 'environments/environment';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { ProgressSigningComponent } from '../progress-signing/progress-signing.component';
import { Document } from 'app/model/document';
import { CountlyService } from 'app/services/api/countly.service';

@Component({
  selector: 'app-signer-signing-otp-verification',
  templateUrl: './signer-signing-otp-verification.component.html',
  styleUrls: ['./signer-signing-otp-verification.component.scss']
})
export class SignerSigningOtpVerificationComponent implements OnInit {

  @Input() phoneNo: string;
  @Input() vendorCode: string;
  @Input() tenantCode: string;
  @Input() verificationType: string;
  @Input() documents : Document[];
  @Input() loginId: string;
  @Input() msg: string;
  @Input() otpByEmail: string;
  @Input() sendMedia: string;
  @Input() isWa: boolean;
  @Input() isSms: boolean;
  @Input() isEmail: boolean;
  @Input() isPrivy: boolean;
  @Input() isEmbed: boolean;
  @Input() durationResendOTP: number;

  sendOtpPlatform: string;
  templateForm: any;
  sendMediaForm: FormGroup;

  timeLeft: number;
  interval: any;
  swal = swalFunction;

  constructor(public activeModal: NgbActiveModal, private toastrService: ToastrService, private http: HttpClient, 
    private router: Router, private formBuilder: FormBuilder, private location: Location, private global: GlobalService, 
    private modalService: NgbModal, private countlyService: CountlyService) { 
    
    }

  ngOnInit(): void {
    if (this.otpByEmail === "1") {
      this.sendOtpPlatform = this.loginId;
    } else {
      this.sendOtpPlatform = this.phoneNo;
    }
    console.log("otpPlatform : " + this.sendOtpPlatform)
    this.templateForm = this.formBuilder.group({
      otp: ['', Validators.required]
    });

    this.sendMediaForm = this.formBuilder.group({
      sendMedia: ['', Validators.required]
    });

    this.sendMediaForm.patchValue({
      sendMedia: this.sendMedia
    })

    if (localStorage.getItem('timeLeft') != null) {
      this.timeLeft = Number(localStorage.getItem('timeLeft'));
      this.startTimer();
    }
    
    this.timeLeft = this.durationResendOTP;
    console.log(this.timeLeft);
    clearInterval(this.interval);
    this.startTimer();
    console.log("OTP by email : " + this.otpByEmail);
  }

  dismiss() {
    this.activeModal.dismiss('0');
    clearInterval(this.interval);
    localStorage.setItem('timeLeft', this.timeLeft.toString());
  }

  openSuccessPopup() {
    this.toastrService.success('Verifikasi OTP berhasil', null, {
      positionClass: 'toast-top-right'
    });
  }

  openWrongCodePopup() {
    this.swal.Error('Kode OTP tidak sesuai!');
  }

  onSubmit() {
    const request = new VerifyOtpBySMSRequest();
    request.phoneNo = this.phoneNo;
    request.otpCode = this.templateForm.controls.otp.value;
    request.vendorCode = this.vendorCode;

    request.documentId = [];
      for (const doc of this.documents) {
        request.documentId.push(doc.documentId);
    }

    let url: string;

    if(this.msg && this.router.url.includes('/V2/')){
      request.msg = this.msg;
      request.tenantCode = this.tenantCode;
      url = URLConstant.verifyOtpSigningEmbedV2;
    }
    else {
      request.tenantCode = this.global.user.role.tenantCode;
      url = URLConstant.verifyOtpSigningVerification;
    }

    this.http.post<BaseResponse>(url, request).subscribe(
      (response) => {
        if (response.status.code!== 0) {
          this.swal.Error(response.status.message);
          console.log('Error', response.status.message);
          return;
        }
        this.openSuccessPopup();
        this.activeModal.close(request.otpCode);
        const modal = this.modalService.open(ProgressSigningComponent, { backdrop: 'static', keyboard: false, size: 'lg' });
        if(this.msg) {
          modal.componentInstance.msg = this.msg;
          modal.componentInstance.tenantCode = this.global.user.role.tenantCode;
        }
        modal.componentInstance.documents = this.documents;
        modal.componentInstance.loginId = this.loginId;
        this.countlyService.trackPageViewData('Success Sign');
      }
    );
  }

  sendOtp() {
    const request = new SentOtpSigningVerificationRequest();
    request.phoneNo = this.phoneNo;
    request.vendorCode = this.vendorCode;
    let url: string;

    if(this.msg) {
      request.msg = this.msg;
      request.tenantCode = this.tenantCode;
      url = URLConstant.sentOtpSigningVerificationEmbedV2;
      request.sendingPointOption = this.isPrivy ? 'SMS' : this.sendMediaForm.controls.sendMedia.value;
    }
    else {
      request.tenantCode = this.global.user.role.tenantCode;
      request.sendingPointOption = this.isPrivy ? 'SMS' : this.sendMediaForm.controls.sendMedia.value;
      url = URLConstant.sentOtpSigningVerification;
    }
    
    request.documentId = [];
    for (const doc of this.documents) {
      request.documentId.push(doc.documentId);
    }

    this.http.post<BaseResponse>(url, request).subscribe((response) => {
      if (response.status.code !== 0) {
        this.swal.Error(response.status.message);
        console.log('Error', response.status.message);
      } else {
        this.timeLeft = response['durationResendOTP'];;
        clearInterval(this.interval);
        this.startTimer();
      }
    });
  
}

startTimer() {
  this.interval = setInterval(() => {
    if (this.timeLeft > 1) {
      this.timeLeft--;
    } else {
      this.timeLeft = -1;
    }
  }, 1000);
}

}
