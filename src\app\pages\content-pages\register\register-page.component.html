<!--Registration Page Starts-->
<nav *ngIf="src === 'INVITATION'" class="navbar navbar-light bg-white">
  <div class="text-center justify-content-center">
    <a class="logo-text" href="">
      <img src="./assets/img/logo-eSign.png" alt="eSign logo" class="logo-img" style="width: 148px; height: 45px" />
    </a>
  </div>
</nav>

<section id="regestration">
  <div class="row">
    <div class="col-12 d-flex align-items-center justify-content-center">
      <div class="card overflow-hidden" style="width: 480px;">
        <div class="card-content">
          <div class="card-body auth-img">
            <div class="row m-0">
              <div class="col-lg-12 col-md-12 px-4 py-3" *ngIf="state === 'pre'">
                <h4 class="card-title mb-2">Pre Register</h4>
                <p>Apa<PERSON>h anda sudah pernah melakukan registrasi<br /> di PSRe berikut dengan menggunakan alamat email <u>{{loginId}}</u>?</p>

                <form>
                  <div class="form-group">
                    <label for="vendor">PSRE</label>
                    <ng-select [items]="provider" id="vendor" bindLabel="name" placeholder="Pilih PSRe" name="psre" [(ngModel)]="selectedProvider">
                    </ng-select>
                  </div>
                  <div class="form-group">
                    <label for="answer">Jawaban</label>
                    <ng-select [items]="answers" id="answer" bindLabel="name" placeholder="Pilih Jawaban" [(ngModel)]="selectedAnswer"
                    (change)="onAnswerChange()" name="answer">
                    </ng-select>
                  </div>
                  <div class="d-flex justify-content-between flex-sm-row flex-column">
                    <button *ngIf="!selectedAnswer" class="btn btn-info" disabled>Register</button>
                    <a *ngIf="selectedAnswer && selectedAnswer.id === 1" class="btn btn-info" (click)="onSubmit()">Register</a>
                    <a *ngIf="selectedAnswer && selectedAnswer.id === 2" class="btn btn-info" (click)="goToDetail()">Tanda Tangan Dokumen</a>
                  </div>

                </form>
              </div>

              <div class="col-lg-12 col-md-12 px-4 py-3" *ngIf="state === 'register'">
                <h4 class="card-title mb-2 register-title" translate>Register</h4>
                <p class="register-msg" translate>Fill the below form to create a new account</p>

                <form [formGroup]="registerForm" #formElement>
                  <div class="form-group">
                    <label for="psre" class="form-title">PSrE <span class="mandatory text-danger">*</span></label>
                    <ng-select formControlName="psre" [items]="provider" id="psre" bindLabel="name" [placeholder]="'Select PSrE' | translate"
                      [(ngModel)]="selectedProvider" class="form-input" (change)="onChange('psre', $event)"
                      [ngClass]="{ 'is-invalid': registerFormSubmitted && rf.psre.invalid, 'is-valid': registerFormSubmitted && !rf.psre.invalid }">
                    </ng-select>
                  </div>

                  <div class="form-group">
                    <label for="email" class="form-title">Email <span class="mandatory text-danger">*</span></label>
                    <input type="email" formControlName="email" id="email" class="form-control mb-2" placeholder="Email"
                           [ngClass]="{ 'is-invalid': registerForm.get('email').dirty && rf.email.invalid, 'is-valid': registerForm.get('email').dirty && !rf.email.invalid }"
                           required>
                    <div *ngFor="let validation of validationMessages.email">
                      <div *ngIf="registerForm.get('email').dirty && registerForm.get('email').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="nik" class="form-title">NIK <span class="mandatory text-danger">*</span></label>
                    <input type="text" formControlName="nik" id="nik" class="form-control mb-2" placeholder="NIK"
                           [ngClass]="{ 'is-invalid': registerForm.get('nik').dirty && rf.nik.invalid, 'is-valid': registerForm.get('nik').dirty && !rf.nik.invalid }"
                           required>
                    <div *ngFor="let validation of validationMessages.nik">
                      <div *ngIf="registerForm.get('nik').dirty && registerForm.get('nik').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="name" class="form-title" translate>Full Name <span class="mandatory text-danger">*</span></label>
                    <input type="text" formControlName="name" id="name" class="form-control mb-2" placeholder="Nama Lengkap"
                           (input)="onInput($event)" [ngClass]="{ 'is-invalid': rf.name.invalid && registerForm.get('name').dirty, 'is-valid': !rf.name.invalid && registerForm.get('name').dirty }"
                           required>
                    <div *ngFor="let validation of validationMessages.name">
                      <div *ngIf="registerForm.get('name').dirty && registerForm.get('name').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="pob" class="form-title" translate>Place of Birth <span class="mandatory text-danger">*</span></label>
                    <input type="text" formControlName="pob" id="pob" class="form-control mb-2" placeholder="Tempat Lahir"
                           [ngClass]="{ 'is-invalid': registerForm.get('pob').dirty && rf.pob.invalid, 'is-valid': registerForm.get('pob').dirty && !rf.pob.invalid }"
                           required>
                    <div *ngFor="let validation of validationMessages.pob">
                      <div *ngIf="registerForm.get('pob').dirty && registerForm.get('pob').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="dob" class="form-title" translate>Date of Birth <span class="mandatory text-danger">*</span></label>
                    <input type="date" formControlName="dob" id="dob" class="form-control mb-2" placeholder="Tanggal Lahir"
                      [ngClass]="{ 'is-invalid': registerForm.get('dob').dirty && registerForm.get('dob').invalid, 'is-valid': registerForm.get('dob').dirty && !rf.dob.invalid }">
                    <div *ngFor="let validation of validationMessages.dob">
                      <div *ngIf="registerForm.get('dob').dirty && registerForm.get('dob').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="gender" class="form-title" translate>Gender <span class="mandatory text-danger">*</span></label>
                    <div class="row">
                      <div class="col-6">
                        <span class="border rounded border-primary d-block">
                          <div class="form-check form-check-inline align-middle p-3">
                            <input class="form-check-input" type="radio" id="female" formControlName="gender" value="F">
                            <label class="form-check-label label-radio-button" for="female" translate>Female</label>
                          </div>
                        </span>
                      </div>
                      <div class="col-6">
                        <span class="border rounded border-primary d-block">
                          <div class="form-check form-check-inline align-middle p-3">
                            <input class="form-check-input" type="radio" id="male" formControlName="gender" value="M">
                            <label class="form-check-label label-radio-button" for="male" translate>Male</label>
                          </div>
                        </span>
                      </div>
                    </div>

                    <!-- <ng-select formControlName="gender" [items]="genders" bindValue="id" id="gender"
                      bindLabel="name" placeholder="Pilih Jenis Kelamin" [(ngModel)]="selectedGender">
                    </ng-select> -->
                    <div *ngFor="let validation of validationMessages.gender">
                      <div *ngIf="registerForm.get('gender').dirty && registerForm.get('gender').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="phone" class="form-title" translate>Phone Number <span class="mandatory text-danger">*</span></label>
                    <input type="tel" formControlName="phone" id="phone" class="form-control mb-2" placeholder="Contoh : 081234567890"
                          [ngClass]="{ 'is-invalid': registerForm.get('phone').dirty && rf.phone.invalid, 'is-valid': registerForm.get('phone').dirty && !rf.phone.invalid }"
                          required>
                    <div *ngFor="let validation of validationMessages.phone">
                      <div *ngIf="registerForm.get('phone').dirty && registerForm.get('phone').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="address" class="form-title" translate>Full Address <span class="mandatory text-danger">*</span></label>
                    <textarea rows="3" formControlName="address" id="address" class="form-control mb-2" placeholder="Tulis alamat lengkap"
                              [ngClass]="{ 'is-invalid': registerForm.get('address').dirty && rf.address.invalid, 'is-valid': registerForm.get('address').dirty && !rf.address.invalid }"
                              required>
                    </textarea>
                    <div *ngFor="let validation of validationMessages.address">
                      <div *ngIf="registerForm.get('address').dirty && registerForm.get('address').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group" *ngIf="!comboMode; else comboProvince">
                    <label class="form-title" translate>Province <span class="mandatory text-danger">*</span></label>
                    <input type="text" formControlName="province" class="form-control mb-2" placeholder="Provinsi"
                           [ngClass]="{ 'is-invalid': registerForm.get('province').dirty && rf.province.invalid, 'is-valid': registerForm.get('province').dirty && !rf.province.invalid }"
                           required>
                    <div *ngFor="let validation of validationMessages.province">
                      <div *ngIf="registerForm.get('province').dirty && registerForm.get('province').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <ng-template #comboProvince>
                    <div class="form-group">
                      <label class="form-title" translate>Province <span class="mandatory text-danger">*</span></label>
                      <ng-select formControlName="province" [items]="listProvince" bindLabel="namaProvinsi" [placeholder]="'Select Province' | translate"
                                 [(ngModel)]="selectedProvince" class="form-input" (change)="onChange('province', $event)"
                                 [ngClass]="{ 'is-invalid': registerFormSubmitted && rf.province.invalid, 'is-valid': registerFormSubmitted && !rf.province.invalid }">
                      </ng-select>
                      <div *ngFor="let validation of validationMessages.province">
                        <div *ngIf="registerForm.get('province').dirty && registerForm.get('province').hasError(validation.type)"
                             class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                      </div>
                    </div>
                  </ng-template>

                  <div class="form-group" *ngIf="!comboMode; else comboCity">
                    <label for="pob" class="form-title" translate>City <span class="mandatory text-danger">*</span></label>
                    <input type="text" formControlName="city" id="city" class="form-control mb-2" placeholder="Kota"
                           [ngClass]="{ 'is-invalid': registerForm.get('city').dirty && rf.city.invalid, 'is-valid': registerForm.get('city').dirty && !rf.city.invalid }"
                           required>
                    <div *ngFor="let validation of validationMessages.city">
                      <div *ngIf="registerForm.get('city').dirty && registerForm.get('city').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <ng-template #comboCity>
                    <div class="form-group">
                      <label class="form-title" translate>City <span class="mandatory text-danger">*</span></label>
                      <ng-select formControlName="city" [items]="listCity" bindLabel="districtName" [placeholder]="'Select City' | translate"
                                 [(ngModel)]="selectedCity" class="form-input" (change)="onChange('city', $event)"
                                 [ngClass]="{ 'is-invalid': registerFormSubmitted && rf.city.invalid, 'is-valid': registerFormSubmitted && !rf.city.invalid }">
                      </ng-select>
                      <div *ngFor="let validation of validationMessages.city">
                        <div *ngIf="registerForm.get('city').dirty && registerForm.get('city').hasError(validation.type)"
                             class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                      </div>
                    </div>
                  </ng-template>

                  <div class="form-group" *ngIf="!comboMode; else comboDistrict">
                    <label for="district" class="form-title" translate>District <span class="mandatory text-danger">*</span></label>
                    <input type="text" formControlName="district" id="district" class="form-control mb-2" placeholder="Kecamatan"
                           [ngClass]="{ 'is-invalid': registerForm.get('district').dirty && rf.district.invalid, 'is-valid': registerForm.get('district').dirty && !rf.district.invalid }"
                           required>
                    <div *ngFor="let validation of validationMessages.district">
                      <div *ngIf="registerForm.get('district').dirty && registerForm.get('district').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <ng-template #comboDistrict>
                    <div class="form-group">
                      <label class="form-title" translate>District <span class="mandatory text-danger">*</span></label>
                      <ng-select formControlName="district" [items]="listDistrict" bindLabel="subDistrictName" [placeholder]="'Select District' | translate"
                                 [(ngModel)]="selectedDistrict" class="form-input" (change)="onChange('district', $event)"
                                 [ngClass]="{ 'is-invalid': registerFormSubmitted && rf.district.invalid, 'is-valid': registerFormSubmitted && !rf.district.invalid }">
                      </ng-select>
                      <div *ngFor="let validation of validationMessages.district">
                        <div *ngIf="registerForm.get('district').dirty && registerForm.get('district').hasError(validation.type)"
                             class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                      </div>
                    </div>
                  </ng-template>

                  <div class="form-group">
                    <label for="subDistrict" class="form-title" translate>Ward <span class="mandatory text-danger">*</span></label>
                    <input type="text" formControlName="subDistrict" id="subDistrict" class="form-control mb-2" placeholder="Kelurahan"
                           [ngClass]="{ 'is-invalid': registerForm.get('subDistrict').dirty && rf.district.invalid, 'is-valid': registerForm.get('subDistrict').dirty && !rf.district.invalid }"
                           required>
                    <div *ngFor="let validation of validationMessages.subDistrict">
                      <div *ngIf="registerForm.get('subDistrict').dirty && registerForm.get('subDistrict').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="zip" class="form-title" translate>Zip Code <span class="mandatory text-danger">*</span></label>
                    <input type="text" formControlName="zip" id="zip" class="form-control mb-2" placeholder="Kode Pos"
                           [ngClass]="{ 'is-invalid': registerForm.get('zip').dirty && rf.zip.invalid, 'is-valid': registerForm.get('zip').dirty && !rf.zip.invalid }"
                           required>
                    <div *ngFor="let validation of validationMessages.zip">
                      <div *ngIf="registerForm.get('zip').dirty && registerForm.get('zip').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="fotoDiri" class="form-title" translate>Self Photo <span class="mandatory text-danger">*</span></label>
                    <div class="justify-content-center text-center" style="display: block; margin-top: 5px; margin-bottom: 5px;"
                      *ngIf="registerForm.get('fotoSelfie').invalid">
                      <img src="../../../../assets/img/photos/photo-area.png"
                        style="border-radius: 8px; width: 100%; height: 100%;" alt="Foto Diri" (click)="openWebcam('fotoSelfie')" />
                    </div>
                    <div class="justify-content-center text-center" style="display: block; margin-top: 5px; margin-bottom: 5px;"
                      *ngIf="registerForm.get('fotoSelfie').valid">
                      <img [src]="registerForm.get('fotoSelfie').value"
                        style="border-radius: 8px; width: 100%; height: 100%;" alt="Foto Diri" />
                      <p style="font-size: 10px">Ukuran File: {{getFileSize('fotoSelfie')}}</p>
                    </div>
                    <div class="pt-3" *ngIf="!disableTakePhoto">
                      <button class="btn btn-info form-control photo-button" (click)="openWebcam('fotoSelfie')" translate>Take Photo</button>
                    </div>

                    <input formControlName="fotoSelfie" type="hidden" id="fotoDiri">
                    <div *ngFor="let validation of validationMessages.fotoSelfie">
                      <div *ngIf="registerForm.get('fotoSelfie').dirty && registerForm.get('fotoSelfie').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="fotoKtp" class="form-title" translate>Photo ID <span class="mandatory text-danger">*</span></label>
                    <div class="justify-content-center text-center" style="display: block; margin-top: 5px; margin-bottom: 5px;"
                      *ngIf="registerForm.get('fotoKtp').invalid">
                      <img src="../../../../assets/img/photos/photo-area.png"
                        style="border-radius: 8px; width: 100%; height: 100%;" alt="Foto Diri" (click)="openWebcamKtp('fotoKtp')" />
                    </div>
                    <div class="justify-content-center text-center" style="display: block; margin-top: 5px; margin-bottom: 5px;"
                      *ngIf="registerForm.get('fotoKtp').valid">
                      <img [src]="registerForm.get('fotoKtp').value" class="img-fluid"
                        style="border-radius: 8px;" alt="Foto Ktp" />
                      <p style="font-size: 10px">Ukuran File: {{getFileSize('fotoKtp')}}</p>
                    </div>

                    <div class="row align-items-center" *ngIf="!disableBrowse">
                      <div class="col-5">
                        <label class="btn btn-secondary">
                          <input formControlName="fotoKtpRaw" (change)="onFileChange($event, 'fotoKtp')" (click)="clearFotoKtp()" type="file"
                          accept="image/jpg, image/jpeg" id="fotoKtp">
                          {{'Browse' | translate}}
                        </label>
                      </div>
                      <div class="col-7 text-left">
                        <div *ngIf="!fileName && registerForm.get('fotoKtp').value == null" translate>
                          No file selected
                        </div>
                        <div *ngIf="fileName">
                          {{ fileName }}
                        </div>
                      </div>
                    </div>
                    <div class="pt-3" *ngIf="!disableTakePhotoKTP">
                      <p class="font-large-1 text-center" *ngIf="!disableBrowse" translate>or</p>
                      <button class="btn btn-info form-control photo-button" (click)="openWebcamKtp('fotoKtp')" translate>Take Photo</button>
                    </div>

                    <!-- <input formControlName="fotoKtp" (change)="onFileChange($event, 'fotoKtp')" type="file"
                      accept="image/jpg, image/jpeg" class="form-control-file" id="fotoKtp"> -->

                    <div *ngFor="let validation of validationMessages.fotoKtp">
                      <div *ngIf="registerForm.get('fotoKtp').dirty && registerForm.get('fotoKtp').hasError(validation.type)"
                           class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="checkbox auth-checkbox  ">
                      <input type="checkbox" id="auth-login" formControlName="acceptTerms" class="form-control" (change)="onTnc($event)">
                      <label for="auth-login"><span class="tos-msg">&nbsp;&nbsp;Saya Menerima <a routerLink="/terms" target="_blank" style="text-decoration: underline;">Syarat dan Ketentuan</a></span></label>
                      <div *ngIf="registerFormSubmitted && (rf.acceptTerms.invalid || rf.acceptTerms.errors?.required)"
                           class="help-block mt-1 text-danger">
                            <i class="ft-alert-circle align-middle"></i>Accept Ts & Cs is required
                      </div>
                    </div>
                  </div>

                  <div *ngIf="psreCode === 'VIDA'">
                    <div class="form-group">
                      <div class="checkbox auth-checkbox  ">
                        <input type="checkbox" id="auth-login-vida-mitra" formControlName="acceptTermsMitra" class="form-control" (change)="onTnc($event)">
                        <label for="auth-login-vida-mitra"><span class="tos-msg">&nbsp;&nbsp;Saya Menerima <a routerLink="/terms-condition-vida" target="_blank" style="text-decoration: underline;">Syarat Dan Ketentuan dari Mitra</a></span></label>
                        <div *ngIf="registerFormSubmitted && (rf.acceptTermsMitra.invalid || rf.acceptTermsMitra.errors?.required)"
                             class="help-block mt-1 text-danger">
                              <i class="ft-alert-circle align-middle"></i>Accept Ts & Cs is required
                        </div>
                      </div>
                    </div>
                  </div>

                  <div *ngIf="psreCode === 'PRIVY'">
                    <div class="form-group">
                      <div class="checkbox auth-checkbox  ">
                        <input type="checkbox" id="auth-login-vida-mitra" formControlName="acceptTermsMitra" class="form-control" (change)="onTnc($event)">
                        <label for="auth-login-vida-mitra"><span class="tos-msg">&nbsp;&nbsp;Saya Menerima <a routerLink="/terms-condition-privy" target="_blank" style="text-decoration: underline;">Syarat Dan Ketentuan dari Mitra</a></span></label>
                        <div *ngIf="registerFormSubmitted && (rf.acceptTermsMitra.invalid || rf.acceptTermsMitra.errors?.required)"
                             class="help-block mt-1 text-danger">
                              <i class="ft-alert-circle align-middle"></i>Accept Ts & Cs is required
                        </div>
                      </div>
                    </div>
                  </div>

                  <div *ngIf="psreCode === 'TKNAJ' && src === 'INVITATION'">
                    <div class="form-group">
                      <div class="checkbox auth-checkbox  ">
                        <input type="checkbox" id="auth-login-tknaj-mitra" formControlName="acceptTermsMitra" class="form-control" (change)="onTnc($event)">
                        <label for="auth-login-tknaj-mitra"><span class="tos-msg">&nbsp;&nbsp;Saya Menerima <a routerLink="/terms-condition-tekenaja" target="_blank" style="text-decoration: underline;">Syarat Dan Ketentuan dari Mitra</a></span></label>
                        <div *ngIf="registerFormSubmitted && (rf.acceptTermsMitra.invalid || rf.acceptTermsMitra.errors?.required)"
                             class="help-block mt-1 text-danger">
                              <i class="ft-alert-circle align-middle"></i>Accept Ts & Cs is required
                        </div>
                      </div>
                    </div>
                  </div>

                  <div *ngIf="psreCode === 'VIDA'">
                    <div class="form-group">
                      <div class="checkbox auth-checkbox  ">
                        <input type="checkbox" id="auth-login-vida" formControlName="acceptTermsVida" class="form-control" (change)="onTnc($event)">
                        <label for="auth-login-vida"><div class="tos-msg" style="margin-left: 10px; font-size: 8pt; text-align: justify;">Saya dengan ini menyatakan telah membaca, memahami, dan menyetujui syarat dan ketentuan layanan Penyelenggara Sertifikasi Elektronik serta menjamin keakuratan data pribadi saya untuk diproses lebih lanjut oleh VIDA sebagai mitra dari eSignHub untuk keperluan penerbitan dan pengelolaan Sertifikat Elektronik.</div></label>
                        <div *ngIf="registerFormSubmitted && (rf.acceptTermsVida.invalid || rf.acceptTermsVida.errors?.required)"
                             class="help-block mt-1 text-danger">
                              <i class="ft-alert-circle align-middle"></i>Accept Ts & Cs is required
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="d-flex justify-content-between flex-sm-row flex-column">
                    <button class="btn btn-info" appPreventDoubleClick (throttledClick)="onRegister()" [throttleTime]="1000" style="width: 100%; height: 55px; font-size: 16px;" translate>Register</button>
                    <a class="btn btn-link" routerLink="/dashboard" *ngIf="state === 'register'" style="display: none">Sudah terdaftar di PSrE</a>
                  </div>
                </form>
              </div>

              <div class="col-lg-12 col-md-12 px-4 py-3" *ngIf="state === 'register-success'">
                <h4 class="card-title mb-2">Pendaftaran Berhasil!</h4>
                <div *ngIf="!registerMsg">
                  <p>Anda sudah berhasil registrasi, silahkan cek {{getContact()}} untuk melakukan proses aktivasi.</p>
                </div>
                <div *ngIf="registerMsg">
                  <p>{{registerMsg}}</p>
                </div>
              </div>

              <!-- Inactive invitation link -->
              <div class="col-lg-12 col-md-12 px-4 py-3" *ngIf="state === 'link-inactive'">
                <h4 class="card-title mb-2">Link Undangan sudah tidak aktif!</h4>
                <div>
                  <p>Silahkan menunggu/menggunakan link permintaan tanda tangan.</p>
                </div>
              </div>

              <!-- Expired invitation link -->
              <div class="col-lg-12 col-md-12 px-4 py-3" *ngIf="state === 'link-expired'">
                <h4 class="card-title mb-2">Link undangan sudah sudah kadaluwarsa!</h4>
                <p>Harap lakukan pembuatan ulang undangan registrasi untuk melanjutkan proses registrasi.</p>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!--Registration Page Ends-->
<ngx-spinner style="visibility: hidden"></ngx-spinner>

<ngx-photo-editor
  [modalTitle]="'Edit Foto'"
  [imageBase64]="imageChangedEvent"
  (imageCropped)="imageCropped($event)"
  [aspectRatio]="4/3"
  [imageFormat]="imageFormat"
  [imageQuality]="95"
  [autoCrop]="false"
  [viewMode]="1">
</ngx-photo-editor>
