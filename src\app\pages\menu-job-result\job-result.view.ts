
import { JobResult } from "app/model/job-result";
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { QuestionDate, QuestionDropdown, QuestionTextbox } from "app/shared/components/ms-form/questions";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { URLConstant } from "app/shared/constant/URLConstant";
import { CommonConstant } from "app/shared/constant/common.constant";

const JobResultSearchFilter: FormModel<string> = {
    name: 'JobResultSearchForm',
    direction: FormConstant.DIRECTION_HORIZONTAL,
    colSize: 6,
    exportExcel: false,
    components: [
        new QuestionDate({
            key: 'requestDateStart',
            label: 'Request Date Start',
            placeholder: CommonConstant.FORMAT_DATE,
            isDateStart: true
        }),
        new QuestionDate({
            key: 'requestDateEnd',
            label: 'Request Date End',
            placeholder: CommonConstant.FORMAT_DATE,
            isDateEnd: true
        }),
        new QuestionDropdown({
          key: 'jobId',
          label: 'Job Name',
          placeholder: 'Select Job Name',
          serviceUrl: URLConstant.GetListJobByJobProcessType,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'jobList',
            key: 'jobId',
            value: 'jobName'
          }
        }),
        new QuestionDropdown({
          key: 'processResult',
          label: 'Process Result',
          placeholder: 'Select Process Result',
          options: [
            {key: '', value: 'All'},
            {key: '0', value: 'New'},
            {key: '1', value: 'In Progress'},
            {key: '2', value: 'Completed'},
            {key: '3', value: 'Cancelled'},
            {key: '4', value: 'Failed'},
            {key: '5', value: 'Deleted'}
          ],
        }),
        new QuestionTextbox({
          key: 'loginId',
          label: 'Request By',
          placeholder: 'Type Request By',
          value: ''
        }),
    ],
    params: [
        {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
        }
    ]
};

const JobResultTable: Table<JobResult> = {
    name: 'listJobResult',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'requestBy',
            label: 'Request By',
            width: 130
        },
        {
            type: ColumnType.Text,
            prop: 'jobName',
            label: 'Job Name',
            width: 100
        },
        {
          type: ColumnType.Date,
          format: CommonConstant.FORMAT_DATE_WITHTIME_SECOND,
          prop: 'dtmCrt',
          label: 'Request Time',
          width: 70
        },
        {
            type: ColumnType.Date,
            format: CommonConstant.FORMAT_DATE_WITHTIME_SECOND,
            prop: 'processStartTime',
            label: 'Process Start',
            width: 70
        },
        {
          type: ColumnType.Date,
          format: CommonConstant.FORMAT_DATE_WITHTIME_SECOND,
          prop: 'processFinishTime',
          label: 'Process End',
          width: 70
        },
        {
            type: ColumnType.Text,
            prop: 'process',
            label: 'Duration',
            width: 80
        },
        {
            type: ColumnType.Text,
            prop: 'processStatus',
            label: 'Status',
            width: 80
        },
        {
          type: ColumnType.Action,
          label: 'Action',
          width: 100,
          action: [
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-eye',
              descr: 'View Request Param',
              type: Act.View,
              condition: true,
              conditionVariable: 'isSuccess',
              conditionExpected: '0',
              conditionedClass: 'd-none'
            },
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-x-circle',
              descr: 'Cancel Job Process',
              type: Act.Cancel,
              condition: true,
              conditionVariable: 'isNew',
              conditionExpected: '0',
              conditionedClass: 'd-none'
            }
          ]
      }
    ]
};

export const JobResultListView: MsxView = {
    title: 'Job Result',
    components: [
        {
            type: WidgetType.SearchFilter,
            component: JobResultSearchFilter
        },
        {
            type: WidgetType.Datatable,
            component: JobResultTable
        }
    ]
};
