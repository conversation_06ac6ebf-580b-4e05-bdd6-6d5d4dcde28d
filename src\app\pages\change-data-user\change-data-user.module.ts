import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ChangeDataUserRoutingModule } from './change-data-user-routing.module';
import { ChangeDataComponent } from './change-data/change-data.component';
import { ChangeDataInputComponent } from './change-data-input/change-data-input.component';
import {MsFormModule} from '../../shared/components/ms-form/ms-form.module';


@NgModule({
  declarations: [ChangeDataComponent, ChangeDataInputComponent],
  imports: [
    CommonModule,
    ChangeDataUserRoutingModule,
    MsFormModule
  ]
})
export class ChangeDataUserModule { }
