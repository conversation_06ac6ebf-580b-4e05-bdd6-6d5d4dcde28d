import {AfterViewInit, ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ActivatedRoute, NavigationExtras, Router} from '@angular/router';
import {ToastrService} from 'ngx-toastr';
import {GlobalService} from '../../../shared/data/global.service';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {NgxSpinnerService} from 'ngx-spinner';
import {PathConstant} from '../../../shared/constant/PathConstant';
import {SignDocumentRequest} from '../../../model/api/sign.document.request';
import {URLConstant} from '../../../shared/constant/URLConstant';
import {SignDocumentResponse} from '../../../model/api/sign.document.response';
import {CommonConstant} from '../../../shared/constant/common.constant';
import {GetTenantLivenessRequest} from '../../../shared/dto/liveness/get-tenant-liveness.request';
import {GetTenantLivenessResponse} from '../../../shared/dto/liveness/get-tenant-liveness.response';
import {LivenessGuidesComponent} from '../../../shared/components/liveness-guides/liveness-guides.component';
import {LivenessCheckComponent} from '../../../shared/components/liveness-check/liveness-check.component';
import { CheckStatusDocumentService } from 'app/services/check-status-document.service';
import { Location } from '@angular/common';
import { CheckDocumentBeforeSigningRequest } from 'app/model/api/checkDocumentBeforeSigning.request';
import * as swalFunction from '../../../shared/data/sweet-alerts';


@Component({
  selector: 'app-sig-document-embed',
  templateUrl: './sig-document-embed.component.html',
  styleUrls: ['./sig-document-embed.component.scss']
})
export class SigDocumentEmbedComponent implements OnInit, AfterViewInit {

  url: string;
  documentId: string;
  refNumber: string;
  isShow = false;
  signProcess:any[];
  tenantCode: string;



  constructor(private http: HttpClient, private activeRoute: ActivatedRoute, private router: Router,
              private cdr: ChangeDetectorRef, private toastrService: ToastrService, private readonly global: GlobalService,
              private modalService: NgbModal, private spinner: NgxSpinnerService, private checkStatusDocument: CheckStatusDocumentService, private location: Location) {
    this.activeRoute.queryParams.subscribe(
      (params) => {
        if (params['url'] != null) {
          this.url = params['url'];
        }
        if (params['id'] != null) {
          this.documentId = params['id'];
        }
      }
    );
    if (this.router.getCurrentNavigation().extras.state) {
      const state = this.router.getCurrentNavigation().extras.state;
      this.refNumber = state.refNumber;
      this.tenantCode = state.tenantCode;
      console.log(this.refNumber);
    }
  }

  async ngOnInit() {

    // Ketika sign document Digisign di embed V2, ada case localStorage dengan key 'user' terhapus.
    // localStorage dengan key 'user' ini diperlukan supaya redirect ke dashboard embed V2 berhasil.
    if (!(this.router.url.includes('/embed/') && this.router.url.includes('/V2/'))) {
      const msg = localStorage.getItem('msg');
      localStorage.clear();
      localStorage.setItem('msg', msg);
    }    

    if (this.global.user == null) {
      await this.router.navigate([PathConstant.LOGIN], { queryParams: { isReturnToSign: true, id: this.documentId } });
    } else if (this.url == null) {
      const request: SignDocumentRequest = new SignDocumentRequest();
      let signDocUrl = null;
      if(this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
        signDocUrl = URLConstant.SignDocumentEmbedV2;
      } else {
        signDocUrl = URLConstant.SignDocumentEmbed;
      }
      request.msg = this.global.msg;
      request.documentId = this.documentId;

      //check send document status
      const checkDocStatus = await this.checkStatusDocument.validate(this.documentId, this.global.msg, this.router.url, this.refNumber);
      if (checkDocStatus.documentSendStatus === '1') {
        await this.checkDocBeforeSign();
        await this.http.post<SignDocumentResponse>(signDocUrl, request).toPromise().then(
          async (response) => {
            if (response.status.code === 0) {
              if (response.docs != null && response.url != null) {
                const extras: NavigationExtras = {
                  state: {
                    data: response.docs,
                    msg: this.global.msg,
                    vendorCode:response.vendorCode,
                    tenantCode:this.tenantCode
                  }
                };
                if (this.router.url.includes('/V2/')){
                  this.router.navigate([PathConstant.EMBED_V2_BULK_SIGN], extras);
                } else {
                  this.router.navigate([PathConstant.EMBED_BULK_SIGN], extras);
                }
              }
              else if (response.url != null && this.router.url.includes('/V2/')) {
                this.setUrlSign(response.url);
              }
              else if (response.url != null) {
                const livenessRequest: GetTenantLivenessRequest = new GetTenantLivenessRequest;
                const getTenantLivenessUrl = URLConstant.GetTenantLivenessEmbed;
                livenessRequest.msg = this.global.msg;

                await this.http.post<GetTenantLivenessResponse>(getTenantLivenessUrl, livenessRequest).toPromise().then(
                  async (responseLiveness) => {
                    if (responseLiveness.status.code === 0) {
                      if (responseLiveness.statusFaceVerify === CommonConstant.STATUS_ACTIVE) {
                        this.modalService.open(LivenessGuidesComponent, { size: 'sm', backdrop: 'static', keyboard: false })
                          .dismissed.subscribe(() => {
                          const liveness = this.modalService.open(LivenessCheckComponent, { backdrop: 'static', keyboard: false });
                          liveness.componentInstance.refNumber = this.refNumber;
                          liveness.result.then(
                            () => {
                              this.setUrlSign(response.url);
                            }
                          );
                        });
                      } else {
                        this.setUrlSign(response.url);
                      }
                    }
                  }
                );
              }
            } else if (response.status.code === 20 && response.register === '1') {
              const extras: NavigationExtras = {
                state: {
                  data: {
                    msg: this.global.msg,
                    vendorCode: response.vendorCode
                  }
                }
              };

              this.toastrService.info('User belum melakukan registrasi, silahkan melakukan register terlebih dahulu', null, {
                positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
              });

              if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
                this.router.navigate([PathConstant.EMBED_REGISTER], extras);
              } else {
                this.router.navigate([PathConstant.REGISTER], extras);
              }
            } else if (response.status.code === 20) {
              const extras: NavigationExtras = {
                queryParams: {
                  msg: this.global.msg,
                  vendorCode: response.vendorCode
                }
              }

              this.toastrService.info('User belum melakukan aktivasi, silakan melakukan aktivasi terlebih dahulu', null, {
                positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
              });
              this.router.navigate([PathConstant.EMBED_ACTIVATION], extras);
            } else if (response.status.code === 10) {
              this.toastrService.info('Dokumen sudah ditandatangani oleh user', null, {
                positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
              });
              const extras: NavigationExtras = {
                queryParams: {
                  msg: this.global.msg
                }
              };
              if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
                this.router.navigate([PathConstant.EMBED_DASHBOARD], extras);
              } else {
                this.router.navigate([PathConstant.DASHBOARD]);
              }
            } else if (response.status.code === 30) {
              this.toastrService.info('Dokumen sudah tidak aktif lagi', null, {
                positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
              });
              const extras: NavigationExtras = {
                queryParams: {
                  msg: this.global.msg
                }
              };
              if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
                this.router.navigate([PathConstant.EMBED_DASHBOARD], extras);
              } else {
                this.router.navigate([PathConstant.DASHBOARD]);
              }
            }

            if (this.global.user.pathSrc !== CommonConstant.PATH_SRC_EMBED) {
            }
          }, (err) => {
            this.toastrService.error(err.message, null, { positionClass: 'toast-top-right' });
          }
        );
      }
    } else if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED && this.url != null) {
      window.location.href = this.url;
      this.isShow = true;
    } else if (this.url != null) {
      const livenessRequest: GetTenantLivenessRequest = new GetTenantLivenessRequest;
      let getTenantLivenessUrl;
      if (this.global.msg) {
        livenessRequest.msg = this.global.msg;
        getTenantLivenessUrl = URLConstant.GetTenantLivenessEmbed;
      } else {
        livenessRequest.tenantCode = this.global.user.role.tenantCode;
        getTenantLivenessUrl = URLConstant.GetTenantLiveness;
      }

      await this.http.post<GetTenantLivenessResponse>(getTenantLivenessUrl, livenessRequest).toPromise().then(
        async (responseLiveness) => {
          if (responseLiveness.status.code === 0) {
            if (responseLiveness.statusFaceVerify === CommonConstant.STATUS_ACTIVE) {
              this.modalService.open(LivenessGuidesComponent, { size: 'sm', backdrop: 'static', keyboard: false })
                .dismissed.subscribe(() => {
                const liveness = this.modalService.open(LivenessCheckComponent, { backdrop: 'static', keyboard: false });
                liveness.componentInstance.refNumber = this.refNumber;
                liveness.result.then(
                  () => {
                    this.setUrlSign(this.url);
                  }
                );
              });
            } else {
              this.setUrlSign(this.url);
            }
          }
        }
      );
    }
    
  }
  async checkDocBeforeSign(){
    const req = new CheckDocumentBeforeSigningRequest();
    req.listDocumentId.push(this.documentId);

    let url;
    if(this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
      req.msg = this.global.msg;
      url = URLConstant.checkDocumentBeforeSigningEmbedV2;
    } 
    else{
      req.loginId = this.global.user.loginId;
      url = URLConstant.checkDocumentBeforeSigning;
    }

    await this.http.post(url, req).toPromise().then(
      (response) => {
        if (this.router.url.includes('/embed/') && this.router.url.includes('/V2/') &&response["status"]["code"] === 0 && response["listCheckDocumentBeforeSigning"]["0"]["signingProcess"] === "2"){
          swalFunction.Error("Dokumen harus ditandatangani oleh user lain terlebih dahulu.");
          const extras: NavigationExtras = {
            queryParams: {
              msg: this.global.msg,
              tenantCode: this.global.user.role.tenantCode
            }
          };
          this.router.navigate([PathConstant.EMBED_V2_DASHBOARD], extras);       
         }
        else if (response["status"]["code"] === 0 && response["listCheckDocumentBeforeSigning"]["0"]["signingProcess"] === "2") {
          swalFunction.Error("Dokumen harus ditandatangani oleh user lain terlebih dahulu.");
         this.router.navigate([PathConstant.DASHBOARD])
        }
        else if (response["status"]["code"] === 0) {
          this.signProcess = response["listCheckDocumentBeforeSigning"];
          return;
        }
      }
    );
  }

  goToDashboard() {
    if(this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
      const extras: NavigationExtras = {
        queryParams: {
          msg: this.global.msg,
          tenantCode: this.global.user.role.tenantCode
        }
      };
      this.router.navigate([PathConstant.EMBED_V2_DASHBOARD], extras);
    } else {
      this.router.navigate([PathConstant.DASHBOARD]);
    }
  }

  setUrlSign(url) {
    this.url = url;
    if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      window.location.href = this.url;
    }
    this.spinner.hide();
    this.isShow = true;
  }

  ngAfterViewInit(): void {

  }

}
