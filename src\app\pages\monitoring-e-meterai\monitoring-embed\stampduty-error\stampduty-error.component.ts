import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, Input, NgZone, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FormModel } from 'app/shared/components/ms-form/models';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { BehaviorSubject } from 'rxjs';
import { Table } from "app/shared/components/msx-datatable/models/table";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { Monitoring } from 'app/model/monitoring';

@Component({
  selector: 'app-stampduty-error',
  templateUrl: './stampduty-error.component.html',
  styleUrls: ['./stampduty-error.component.scss']
})
export class StampdutyErrorComponent implements OnInit {

  //@Input() idErrorReport: string;
  view: MsxView;
  serviceUrl = URLConstant.ListMonitoringEmbed;

  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  
  @Input() errorMessage: string;
  searchFormObj: FormModel<any> = new FormModel<any>();

  constructor(public activeModal: NgbActiveModal, private global: GlobalService, private http: HttpClient,
    private ngZone: NgZone, private cdr: ChangeDetectorRef) { }


    ngOnInit(): void {
      this.initView();
    }

    initView() {
      const StampdutyErrorTable: Table<Monitoring> = {
         name: 'listMonitoring',
         list: [],
         columns: [
             {
                 type: ColumnType.Text,
                 prop: 'errorMessage',
                 label: 'Message',
                 width: 120
             }
         ]
      }

      this.view ={
        title: 'Stampduty Error Detail',
        components: [
            {
                type: WidgetType.Datatable,
                component: StampdutyErrorTable
            }
        ]
      }
  
    }

  dismiss() {
    this.activeModal.dismiss('0');
  }
}
