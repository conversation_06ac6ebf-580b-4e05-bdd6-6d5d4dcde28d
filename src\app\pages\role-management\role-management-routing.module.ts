import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ListRoleManagementComponent } from "./list-role-management/list-role-management.component";
import { ModifyMenuOfRoleComponent } from "./modify-menu-of-role/modify-menu-of-role.component";

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: '',
        component: ListRoleManagementComponent,
        data: {
          title: 'List Role'
        }
      },
      {
        path: 'manageMenu',
        component: ModifyMenuOfRoleComponent,
        data: {
          title: 'Manage Menus of Role'
        }
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RoleManagementRoutingModule { }