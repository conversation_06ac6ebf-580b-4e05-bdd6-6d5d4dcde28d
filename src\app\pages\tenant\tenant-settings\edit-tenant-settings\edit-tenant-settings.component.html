<form [formGroup]="templateForm" (ngSubmit)="onSubmit()">
    <div class="modal-body">
        <button type="button" class="close" aria-label="Close" (click)="dismiss()">
            <span aria-hidden="true">&times;</span>
        </button>
        
        <div class="mt-5">
            <div class="row justify-content-center align-items-center">
                <p class="font-large-1 text-center text-bold-700 px-5" translate>Setting - {{settingType}}</p>
            </div>
        </div>

        <div class="form-group">
            <label for="value" class="form-title" translate>Masukkan Value Setting</label>
            <input type="text" formControlName="settingValue" id="settingValue" class="form-control mb-2" placeholder="Setting Value"  maxlength="100" id="settingValue" value="{{value}}">
        </div>

        <div class="button-process">
            <button type="submit" class="btn btn-primary" [disabled]="templateForm.invalid" translate>Save</button>
         </div>
    </div>
</form>