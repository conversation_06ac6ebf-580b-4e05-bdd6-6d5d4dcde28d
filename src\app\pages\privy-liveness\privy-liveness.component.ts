import { Component, HostListener, Input, OnInit } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { DeviceDetectorService } from 'ngx-device-detector';

@Component({
  selector: 'app-privy-liveness',
  templateUrl: './privy-liveness.component.html',
  styleUrls: ['./privy-liveness.component.scss']
})
export class PrivyLivenessComponent implements OnInit {

  @Input() livenessUrl: string;
  iframeUrl: SafeResourceUrl;

  eventMethod: string;
  eventer: any;
  messageEvent: string;
  isMobile = false;

  constructor(private sanitizer: DomSanitizer, public activeModal: NgbActiveModal, private deviceService: DeviceDetectorService) {
    console.log('This is constructor');
    this.isMobile = deviceService.isMobile();
  }

  ngOnInit(): void {
    console.log('This is on init');
    this.iframeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.livenessUrl);
  }

  // To listen privy liveness message
  @HostListener('window:message', ['$event'])
  onMessage(event) {
    const key = event.message ? "message" : "data";
    const data = event[key];

    if (data.source === 'privypass_liveness') {

      const livenessResult = data.data;
      console.log(livenessResult);

      if (livenessResult["result"] === true) {
        this.activeModal.close(livenessResult["face_1"]);
      }

    }
  }

}
