
import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ReportLinkInvitationService } from 'app/services/api/report-link-invitation.service';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDate } from 'app/shared/components/ms-form/questions';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { DownloadReportLinkInvitationReportRequest } from 'app/shared/dto/report-link-invitation/download-report-link-invitation-report.request';
import { saveAs } from 'file-saver';
import { ReportLinkInvitationListView } from '../report-linl-list.view';

@Component({
  selector: 'app-report-link-invitation',
  templateUrl: './report-link-invitation.component.html',
  styleUrls: ['./report-link-invitation.component.scss']
})
export class ReportLinkInvitationComponent implements OnInit {

  view: MsxView;
  buttonList: Button[];
  routeData: any;
  serviceUrl = URLConstant.InvitationList;

  @Input()
  question: QuestionDate;

  constructor(private activatedRoute: ActivatedRoute, private router: Router, private modalService: NgbModal,
    private reportLinkInvitationService: ReportLinkInvitationService) {
    this.routeData = this.activatedRoute.snapshot.data;
  }

  ngOnInit(): void {
    this.view = ReportLinkInvitationListView;
    

  }


  private dateToString(dateObject: any) {
    return (dateObject['year']) ? dateObject['year'] + '-' + dateObject['month'] + '-' + dateObject['day'] : dateObject;
  }

  downloadReportLinkInvitationReport(params) {
    console.log('Download List Undangan in excel', params);
    const request = new DownloadReportLinkInvitationReportRequest();
    request.nama = params['nama'];
    request.pengirimanMelalui = params['pengirimanMelalui'];
    request.penerimaUndangan = params['penerimaUndangan'];
    request.tanggalPengirimanDari = this.dateToString(params['tanggalPengirimanDari']);
    request.tanggalPengirimanSampai = this.dateToString(params['tanggalPengirimanSampai']);
    request.statusRegistrasi = params['statusRegistrasi'];
    request.statusUndangan = params['statusUndangan'];
    
    this.reportLinkInvitationService.downloadReportLinkInvitationReport(request).subscribe(
      response => {
        if (response.status.code === 0) {
          const blob = this.b64toBlob(response.base64ExcelReport);
          saveAs(blob, response.filename);
        }
      }
    )
  }

  b64toBlob(b64Data, contentType = '', sliceSize = 512) {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }


  onBtnClickListener(event) {
    switch (event['name']) {
      case this.buttonList[0].name:
        this.router.navigate([PathConstant.REVERSAL_TOPUP_STAMP_DUTY]);
        break;
      case this.buttonList[1].name:
        this.router.navigate([PathConstant.TOPUP_STAMP_DUTY]);
        break;
      default:
        break;
    }
  }

}
