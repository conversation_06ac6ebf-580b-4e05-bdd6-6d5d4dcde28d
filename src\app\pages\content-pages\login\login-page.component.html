<!--Login Page Starts-->
<div *ngIf="!isMobile">
  <section id="login">
    <div class="row vh-100">
      <div class="col left-content">
        <img src="assets/img/logos/Pen.png">
        <p class="title" translate>An Easy Way to Create Digital Sign</p>
        <p class="desc" translate>Start paperless. Get your work done faster and easier with digital signatures that are secure and legally in Indonesia.</p>
      </div>
      <div class="col right-content">
        <div class="content-container">
          <img src="assets/img/logo-eSign.png">
          <h1 translate>Hi, Welcome back to eSignHub</h1>
          <p style="color: #6E7483; font-size: 14px;" translate>Login now to manage your digital sign.</p>
          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
            <div class="form-group">
              <p style="margin-bottom: 8px; font-size: 14px;" translate>Email / Phone Number</p>
              <div class="input">
                <i class="ft-mail"></i>
                <input type="text" formControlName="username" class="form-control"
                  [placeholder]="'Enter your email or phone number' | translate" size="36" [disabled]="loginFormSubmitted"
                  [ngClass]="{ 'is-invalid': loginFormSubmitted && lf.username.invalid, 'is-valid': loginFormSubmitted && !lf.username.invalid }"
                  required>
              </div>
            </div>
            <div class="form-group">
              <p style="margin-bottom: 8px; margin-top: 25px; font-size: 14px;" translate>Access Code</p>
              <div class="input">
                <i class="ft-lock"></i>
                <input [type]="isShowPassword ? 'text' : 'password'" formControlName="password" class="form-control" [placeholder]="'Enter your access code' | translate"
                  size="36" [disabled]="loginFormSubmitted"
                  [ngClass]="{ 'is-invalid': loginFormSubmitted && lf.password.invalid, 'is-valid': loginFormSubmitted && !lf.password.invalid }"
                  required>
                <i style="cursor: pointer;" [ngClass]="{'ft-eye-off': !isShowPassword, 'ft-eye': isShowPassword}" (click)="toggleIsShowPassword()"></i>
              </div>
            </div>
            <div class="d-sm-flex justify-content-between input" style="border: 0px; margin-top: 10px;">
              <!-- <div class="remember-me mb-2 mb-sm-0">
                <div class="checkbox auth-checkbox">
                  <input type="checkbox" formControlName="rememberMe" class="form-control" id="rememberMe">
                  <label for="rememberMe"><span class="font-small-2 mb-3 font-weight-normal">&nbsp;Remember Me</span></label>
                </div>
              </div> -->
              <a [routerLink]="['/pages/forgotaccesscode']" style="color: #2B57BB;" translate>Forgot Access Code?</a>
            </div>
            <button class="login-button" type="submit" [disabled]="loginForm.invalid" translate>Login</button>
          </form>
          <!-- <p class="not-registered-msg">Not registered yet? 
            <span class="create-account-msg">
              <a [routerLink]="['/register']">Create an Account</a>
            </span>
          </p> -->
        </div>
      </div>
    </div>
  </section>
</div>
<div *ngIf="isMobile" style="display: flex; flex-direction: column; height: 100vh;">
  <div class="container">
    <img src="assets/img/logo-eSign-centered.png" style="width: 100%; height: auto;"
      class="rounded mx-auto d-block mt-4 mb-5">
    <div class="row">
      <div class="col-12">
        <p style="font-size: 1.5rem;" class="text-center font-weight-bold" translate>Hi, Welcome back to eSignHub</p>
        <p style="font-size: 1.5rem;" class="text-center" translate>Login now to manage your digital sign.</p>
      </div>
    </div>
    <div class="row justify-content-center mt-5">
      <div class="col-12">
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
          <div class="form-group">
            <p style="margin-bottom: 8px; font-size: 1.3rem;" translate>Email / Phone Number</p>
            <div class="input-group mb-2 mr-sm-2">
              <div class="input-group-prepend">
                <div class="input-group-text">
                  <i style="font-size: 1.3rem;" class="ft-mail"></i>
                </div>
              </div>
              <input style="font-size: 1.3rem;" type="text" formControlName="username" class="form-control"
                [placeholder]="'Enter your email or phone number' | translate" [disabled]="loginFormSubmitted"
                [ngClass]="{ 'is-invalid': loginFormSubmitted && lf.username.invalid, 'is-valid': loginFormSubmitted && !lf.username.invalid }"
                required>
            </div>
          </div>
          <div class="form-group">
            <p style="margin-bottom: 8px; margin-top: 25px; font-size: 1.3rem;">Password</p>
            <div class="input-group mb-2 mr-sm-2">
              <div class="input-group-prepend">
                <span class="input-group-text">
                  <i style="font-size: 1.3rem;" class="ft-lock"></i>
                </span>
              </div>
              <input style="font-size: 1.3rem;" [type]="isShowPassword ? 'text' : 'password'" formControlName="password" class="form-control"
                [placeholder]="'Enter your access code' | translate" [disabled]="loginFormSubmitted"
                [ngClass]="{ 'is-invalid': loginFormSubmitted && lf.password.invalid, 'is-valid': loginFormSubmitted && !lf.password.invalid }"
                required>
              <div class="input-group-append">
                <span class="input-group-text">
                  <i style="font-size: 1.3rem; cursor: pointer;" [ngClass]="{'ft-eye-off': !isShowPassword, 'ft-eye': isShowPassword}" (click)="toggleIsShowPassword()"></i>
                </span>
              </div>
            </div>
          </div>
          <div class="d-sm-flex justify-content-between input" style="border: 0px; margin-top: 25px;">
            <!-- <div class="remember-me mb-2 mb-sm-0">
            <div class="checkbox auth-checkbox">
              <input type="checkbox" formControlName="rememberMe" class="form-control" id="rememberMe">
              <label for="rememberMe"><span class="font-small-2 mb-3 font-weight-normal">&nbsp;Remember Me</span></label>
            </div>
          </div> -->
            <a [routerLink]="['/pages/forgotaccesscode']" style="color: #2B57BB; font-size: 1.3rem;" translate>Forgot Access Code?</a>
          </div>
          <div class="mt-4">
            <button style="font-size: 1.5rem;" class="btn btn-primary btn-block" type="submit" [disabled]="loginForm.invalid" translate>Login</button>
          </div>
        </form>
      </div>
    </div>
  </div>
  <footer class="footer text-center" style="margin-top: auto; align-self: center; width: 100%;">
    <p class="copyright clearfix m-0" style="color: #6E7483;">
      {{'Copyrights' | translate}} &nbsp;&copy;&nbsp; 2021 eSignHub
      <span class="d-sm-inline-block">&nbsp;{{'. All rights reserved' | translate}}</span>
    </p>
  </footer>
</div>
<!--Login Page Ends-->
