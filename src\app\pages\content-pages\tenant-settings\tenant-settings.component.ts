import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, NgZone, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { GetLovRequest } from 'app/model/api/get.lov.request';
import { TenantService } from 'app/services/api/tenant.service';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { AutomaticStampingAfterSignRequest } from 'app/shared/dto/tenant-setting/automatic-stamping-after-sign.request';
import { GetUploadUrlTenantRequest } from 'app/shared/dto/tenant-setting/get-upload-url-tenant.request';
import { TenantSettingRequest } from 'app/shared/dto/tenant-setting/tenant-setting.request';
import { ThresholdBalance } from 'app/shared/dto/tenant-setting/threshold-balance';
import { UpdateAutomaticStampingAfterSignRequest } from 'app/shared/dto/tenant-setting/update-automatic-stamping-after-sign.request';
import { UpdateTenantSettingRequest } from 'app/shared/dto/tenant-setting/update-tenant-setting.request';
import { UpdateUploadUrlTenantRequest } from 'app/shared/dto/tenant-setting/updateUploadUrlTenantRequest.request';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import {Clipboard} from '@angular/cdk/clipboard';
import { TestCallbackRequest } from 'app/shared/dto/tenant-setting/test-callback.request';
import { errorMonitor } from 'events';
import { DeviceDetectorService } from 'ngx-device-detector';
import { CommonConstant } from 'app/shared/constant/common.constant';


@Component({
  selector: 'app-tenant-settings',
  templateUrl: './tenant-settings.component.html',
  styleUrls: ['./tenant-settings.component.scss']
})
export class TenantSettingsComponent implements OnInit {

  formColSize = 6;
  buttonColSize = 3;

  tenantSettingsForm: FormGroup;
  emailList = [];
  swal = swalFunction;
  validationMessages: any;
  template: any;
  log: string;x
  balances = [];
  balanceShow = [];
  balanceValue = new Map<string,any>();
  PATTERN_VALIDATION = '^(|https?:\/\/(.*)?)$';

  constructor(private formBuilder: FormBuilder, private tenantService: TenantService, private global: GlobalService,
    private location: Location, private cdr: ChangeDetectorRef, private toastrService: ToastrService,
    private http: HttpClient, private ngZone: NgZone, private clipboard: Clipboard,
    private deviceDetectorService: DeviceDetectorService) {

      if (this.deviceDetectorService.isMobile()) {
        this.formColSize = 12;
        this.buttonColSize = 4;
      }
    }

  ngOnInit(): void {
    this.getBalances();
    this.setProperties();
    this.setupForm();
    this.setForm();
    this.fillAutoStampSlider();
  }

  getBalances() {
    const request = new GetLovRequest();
    request.lovGroup = 'BALANCE_TYPE';
    this.http.post(URLConstant.GetLov, request).subscribe((response) => {
      if (response['status']['code'] !== 0) {
        this.toastrService.error(response['status']['message']);
        return;
        
      }

      this.ngZone.run(() => {
        this.balances = response['lovList'];
        this.balances.forEach((data) => {
          this.balanceShow[data.code] = false;
        });
      });

    });
  }

  setProperties() {
    this.template = {
      required: '{x} harus diisi',
      invalidurl: '{x} harus diawali http:// atau https://'
    }

    this.validationMessages = {
      refNumberLabel: {type: 'required', message: this.template.required.replace('{x}', 'Ref Number Label')},
      balanceReminderReceiver: {type: 'required', message: this.template.required.replace('{x}', 'Balance Reminder Email Receiver')},
      uploadUrl: {type: 'invalidurl', message: this.template.invalidurl.replace('{x}', 'Upload URL')},
      activationCallbackUrl: {type: 'invalidurl', message: this.template.invalidurl.replace('{x}', 'URL Callback Aktivasi')},
      clientCallbackUrl: {type: 'invalidurl', message: this.template.invalidurl.replace('{x}', 'URL Callback')},
      clientActivationRedirectUrl: {type: 'invalidurl', message: this.template.invalidurl.replace('{x}', 'URL Redirect Aktivasi')},
      clientSigningRedirectUrl: {type: 'invalidurl', message: this.template.invalidurl.replace('{x}', 'URL Redirect Tanda Tangan')},
    }
  }

  setupForm() {

    this.tenantSettingsForm = this.formBuilder.group({
      refNumberLabel: ['', Validators.required],
      uploadUrl: ['', Validators.pattern(this.PATTERN_VALIDATION)],
      activationCallbackUrl: ['', Validators.pattern(this.PATTERN_VALIDATION)],
      automaticSign: ['', Validators.required],
      clientCallbackUrl: ['', Validators.pattern(this.PATTERN_VALIDATION)],
      clientActivationRedirectUrl: ['', Validators.pattern(this.PATTERN_VALIDATION)],
      clientSigningRedirectUrl: ['', Validators.pattern(this.PATTERN_VALIDATION)],
      useWaMessage: ['', Validators.required]
    });
    
  }

  setForm() {
    const request = new TenantSettingRequest();
    request.audit = {callerId: this.global.user.email};
    request.tenantCode = this.global.user.role.tenantCode;
    this.tenantService.getTenantSettings(request).subscribe((response) => {
      if (response.status.code !== 0) {
        return;
      }

      for (let i = 0; i < response.emailReminderDest.length; i++) {
        this.emailList = [...this.emailList, {address: response.emailReminderDest[i]}];
      }
      
      this.tenantSettingsForm.patchValue({
        refNumberLabel: response.refNumberLabel,
        activationCallbackUrl: response.activationCallbackUrl,
        uploadUrl: response.uploadUrl,
        clientCallbackUrl: response.clientCallbackUrl,
        clientActivationRedirectUrl: response.clientActivationRedirectUrl,
        clientSigningRedirectUrl: response.clientSigningRedirectUrl,
        useWaMessage: response.useWaMessage
      });
      
      // Flagging which balance to show
      this.dynamicThresholdBalance(response.thresholdBalance);
    });
  }

  dynamicThresholdBalance(thresholdBalance) {
    this.balances.forEach(
      (data) => {
        if (thresholdBalance[data.code] != null || thresholdBalance[data.code] != undefined) {
          this.balanceShow[data.code] = true;
        }
      }
    );

    this.balanceValue = thresholdBalance;
  } 

  fillAutoStampSlider() {
    const request = new AutomaticStampingAfterSignRequest();
    request.tenantCode = this.global.user.role.tenantCode;

    this.tenantService.getAutomaticStampingAfterSign(request).subscribe((response) => {
      if (response.status.code !== 0) {
        return;
      }

      this.ngZone.run(() => {
        this.tenantSettingsForm.patchValue({
          automaticSign: response.automaticSign
        });
      });

    });

  }
  updateTenantSetting() {
    if (this.processEmailList().length === 0) {
      this.toastrService.error(this.validationMessages.balanceReminderReceiver.message, null, { positionClass: CommonConstant.TOAST_BOTTOM_RIGHT });
      return;
    }

    if (this.tenantSettingsForm.invalid) {
      return;
    }

    const formData = this.tenantSettingsForm.getRawValue();

    const request = new UpdateTenantSettingRequest();
    request.audit = {callerId: this.global.user.loginId};
    request.tenantCode = this.global.user.role.tenantCode;
    request.refNumberLabel = formData.refNumberLabel;
    request.thresholdBalance = this.proccessBalanceThreshold();
    request.emailReminderDest = this.processEmailList();
    request.uploadUrl  = formData.uploadUrl;
    request.activationCallbackUrl = formData.activationCallbackUrl;
    request.automaticSign = formData.automaticSign;
    request.clientCallbackUrl = formData.clientCallbackUrl;
    request.clientActivationRedirectUrl = formData.clientActivationRedirectUrl;
    request.clientSigningRedirectUrl = formData.clientSigningRedirectUrl;
    request.useWaMessage = formData.useWaMessage;

    this.tenantService.updateTenantSettings(request).subscribe((response) => {
        if (response.status.code === 0) {
          this.swal.Success('Pengaturan Tenant telah berhasil disimpan');
        }
    });
  }

  goBack() {
    this.location.back();
  }

  addEmail() {
    this.emailList = [...this.emailList, {address: ''}];
  }

  deleteEmail(index: number) {
    this.emailList.splice(index, 1);
  }

  processEmailList() {
    const emails = [];
    this.emailList.forEach(email => {
      if (email.address !== '') emails.push(email.address);
    });
    return emails;
  }

  proccessBalanceThreshold() {
    this.balances.forEach(
      (data) => {
        if ((<HTMLInputElement> document.getElementById(data.code)) != undefined) {
          this.balanceValue[data.code] = parseInt((<HTMLInputElement> document.getElementById(data.code)).value);
        }
      }
    );

    return this.balanceValue;
  }
  
  copyUploadUrl() {
    const formData = this.tenantSettingsForm.getRawValue();
    if (!formData.uploadUrl) {
      return;
    }
    
    this.clipboard.copy(formData.uploadUrl);
    this.toastrService.success('URL Upload tersalin', null, { positionClass: CommonConstant.TOAST_BOTTOM_RIGHT });
  }

  testCallbackUrl() {
    const formData = this.tenantSettingsForm.getRawValue();
    
    const request = new TestCallbackRequest();
    request.callbackType = 'ACTIVATION';
    request.url = formData.activationCallbackUrl;

    this.tenantService.testTenantCallback(request).subscribe(response => {
      if (response.status.code !== 0) {
        return;
      }

      if (response.errorMessage) {
        this.swal.Error('Failed to call activation callback URL with error: ' + response.errorMessage);
        return;
      }

      if (!response.responseCode.includes('200')) {
        this.swal.Error(CommonConstant.RESPONSE_CODE + response.responseCode);
        return;
      }

      this.swal.Success(CommonConstant.RESPONSE_CODE + response.responseCode + '. Response body: ' +response.responseBody);
    });
  }

  tryClientCallbackUrl() {
    const formData = this.tenantSettingsForm.getRawValue();
    const request = new TestCallbackRequest();
    request.url = formData.clientCallbackUrl;

    this.tenantService.tryCallback(request).subscribe(response => {
      if (response.status.code !== 0) {
        return;
      }

      if (response.errorMessage) {
        this.swal.Error('Failed to call callback URL with error: ' + response.errorMessage);
        return;
      }

      if (!response.responseCode.includes('200')) {
        this.swal.Error(CommonConstant.RESPONSE_CODE + response.responseCode);
        return;
      }

      this.swal.Success(CommonConstant.RESPONSE_CODE + response.responseCode + '. Response body: ' +response.responseBody);
    });
  }

}
