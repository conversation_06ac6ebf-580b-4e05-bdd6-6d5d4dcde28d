import { ErrorReportDetailRequest } from './../../../model/api/error.report.detail.request';
import { HttpClient } from '@angular/common/http';
import { GlobalService } from './../../../shared/data/global.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { BehaviorSubject } from 'rxjs';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { MsxView } from './../../../shared/components/msx-view/models/MsxView';
import { Component, OnInit, Input, NgZone, ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-error-report-detail',
  templateUrl: './error-report-detail.component.html',
  styleUrls: ['./error-report-detail.component.scss']
})
export class ErrorReportDetailComponent implements OnInit {
  @Input() idErrorReport: string;
  view: MsxView;
  serviceUrl = URLConstant.ListErrorReport;

  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  
  @Input() errorMessage: string;

  constructor(public activeModal: NgbActiveModal, private global: GlobalService, private http: HttpClient,
    private ngZone: NgZone, private cdr: ChangeDetectorRef) { }


    ngOnInit(): void {

    }

  dismiss() {
    this.activeModal.dismiss('0');
  }

}
