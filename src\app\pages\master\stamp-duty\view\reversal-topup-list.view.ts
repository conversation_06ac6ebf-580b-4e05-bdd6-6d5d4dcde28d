import { ReversalTopup } from "app/model/reversal-topup";
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { QuestionDropdown } from "app/shared/components/ms-form/questions";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { URLConstant } from "app/shared/constant/URLConstant";

const ReversalTopupSearchFilter: FormModel<string> = {
    name: 'reversalTopupSearchForm',
    direction: FormConstant.DIRECTION_HORIZONTAL,
    colSize: 6,
    components: [
        new QuestionDropdown({
            key: 'invoiceNo',
            label: 'List Invoice No',
            placeholder: 'Select list invoice no',
            serviceUrl: URLConstant.GetListReverseTopup,
            options: [
              {key: '', value: 'All'}
            ],
            args: {
              list: 'listStampDuty',
              key: 'invoiceNo',
              value: 'invoiceNo'
            },
            value: '',
            params: {
                isReversalSearchFilter: true
            }
          }),
    ],
    params: []
}

const ReversalTopupTable: Table<ReversalTopup> = {
    name: 'listStampDuty',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'invoiceNo',
            label: 'Invoice No',
            width: 150
        },
        {
            type: ColumnType.Date,
            format: 'DD-MMM-YYYY',
            prop: 'invoiceDate',
            label: 'Invoice Date',
            width: 120
        },
        {
            type: ColumnType.Currency,
            prop: 'stampDutyFee',
            label: 'Fee',
            width: 70
        },
        {
            type: ColumnType.Text,
            prop: 'vendorName',
            label: 'Vendor Name',
            width: 100
        },
        {
            type: ColumnType.Currency,
            prop: 'availableQty',
            label: 'Available Qty',
            width: 100
        },
        {
            type: ColumnType.Currency,
            prop: 'usedQty',
            label: 'Used Qty',
            width: 100
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [
                {
                    class: 'btn btn-danger',
                    label: 'Delete',
                    type: Act.Cancel
                }
            ]
        }
    ]
};

export const ReversalTopupView: MsxView = {
    title: 'Reversal Topup',
    components: [
        {
            type: WidgetType.SearchFilter,
            component: ReversalTopupSearchFilter
        },
        {
            type: WidgetType.Datatable,
            component: ReversalTopupTable
        }
    ]
};