import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, Input, NgZone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DetailImportBmRequest } from 'app/model/api/detail-import-bm.request';
import { DetailImportBm } from 'app/model/detail-import-bm';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Page } from 'app/shared/components/msx-datatable/models/page';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { url } from 'inspector';
import { DeviceDetectorService } from 'ngx-device-detector';
import { BehaviorSubject } from 'rxjs';

@Component({
  selector: 'app-detail-import-bm',
  templateUrl: './detail-import-bm.component.html',
  styleUrls: ['./detail-import-bm.component.scss']
})
export class DetailImportBmComponent implements OnInit {

  requestDate: string;
  fileName: string;

  view: MsxView;
  serviceUrl = URLConstant.detailImportBmAutosign;
  isMobile: boolean = false;

  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  detailTable: Table<DetailImportBm>;

  constructor(private cdr: ChangeDetectorRef, private ngZone: NgZone, private http: HttpClient,
    private global: GlobalService, private deviceService: DeviceDetectorService, private router: Router) { 
      this.isMobile = deviceService.isMobile();

      if (this.router.getCurrentNavigation().extras.state) {
        const state = this.router.getCurrentNavigation().extras.state;
        this.requestDate = state.requestDate;
        this.fileName = state.fileName;
      }
     }

  async ngOnInit() {
    this.initView();

    await this.listView().then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  initView() {
    this.detailTable = {
      name: 'details',
      list: [],
      columns: [
        {
          type: ColumnType.Text,
          prop: 'nik',
          label: 'NIK',
          width: 200
        },
        {
          type: ColumnType.Text,
          prop: 'email',
          label: 'Email',
          width: 200
        },
        {
          type: ColumnType.Text,
          prop: 'phone',
          label: 'Phone',
          width: 140
        },
        {
          type: ColumnType.Text,
          prop: 'keyUser',
          label: 'Key User',
          width: 140
        },
        {
          type: ColumnType.Text,
          prop: 'cvv',
          label: 'CVV',
          width: 100
        },
        {
          type: ColumnType.Text,
          prop: 'poaId',
          label: 'PoA Id',
          width: 140
        },
        {
          type: ColumnType.Text,
          prop: 'certExp',
          label: 'Signer Electronic Certificate Expiration',
          width: 140
        },
        {
          type: ColumnType.Text,
          prop: 'status',
          label: 'Status',
          width: 140
        },
        {
          type: ColumnType.Text,
          prop: 'notes',
          label: 'Notes',
          width: 160
        }
      ]
    }
  }

  async getPage(page: Page) {
    console.log('requestPage', page);
    await this.listView(page.pageNumber + 1).then(() => {
      this.ngZone.run(() => {
        this.cdr.detectChanges();
      })
    });
  }

  async listView(pageNumber: number = 1){
    const request = new DetailImportBmRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.requestDate = this.requestDate;
    request.fileName = this.fileName;
    request.page = pageNumber;
 
    await this.http.post<any>(this.serviceUrl, request).toPromise().then(
      response => {
        if (response.status.code === 0) {
          this.datasource.next(response);
          console.log('response', response)
          this.cdr.detectChanges();
        }
      }
    );
  }

  goBack() {
    this.router.navigate([PathConstant.IMPORT_AUTOSIGN_BM]);
  }
  
}
