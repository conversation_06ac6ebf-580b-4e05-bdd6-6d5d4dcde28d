import { AuditContext } from "../audit.context";
import { BaseRequest } from "./base.request";

export class BalanceMutationFileRequest extends BaseRequest {
    tenantCode: string;
    vendorCode: string;
    balanceType: string;
    transactionType: string;
    transactionDateStart: string;
    transactionDateEnd: string;
    documentType: string;
    referenceNo: string;
    documentName: string;
    officeCode: string;
    status: string;
    constructor() {
        super();
        this.audit = new AuditContext();
    }
}