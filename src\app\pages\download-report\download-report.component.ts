import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, NgZone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ListReportDownload } from 'app/model/list-report-download';
import { ListReportRequest } from 'app/model/api/list.report.request';
import { ListReportResponse } from 'app/model/api/list.report.response';
import { ActionModel } from 'app/shared/components/ms-form/models/action.model';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Page } from 'app/shared/components/msx-datatable/models/page';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject } from 'rxjs';
import { DownloadReportRequest } from 'app/model/api/download.report.request';
import { DownloadReportResponse } from 'app/model/api/download.report.response';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { ManualReportService } from 'app/services/api/manual-report.service';

@Component({
  selector: 'app-download-report',
  templateUrl: './download-report.component.html',
  styleUrls: ['./download-report.component.scss']
})
export class DownloadReportComponent implements OnInit {

  
  
  listReportTable: Table<ListReportDownload>;
  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
 

  constructor(private router: Router, private toastr: ToastrService, private http: HttpClient,
     private global: GlobalService, private manualReportService: ManualReportService,private ngZone: NgZone, private cdr: ChangeDetectorRef) { }

   async ngOnInit(){
    this.initateView();
     await this.listView().then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
     });
   
  }

  initateView(){
    this.listReportTable = {
      name: 'listReport',
      autoload: true,
      list: [],
      columns: [
        {
          type: ColumnType.Text,
          label: 'Periode',
          prop: 'periode',
          width: 200
        },
        {
          type: ColumnType.Text,
          label: 'Report Type',
          prop: 'reportType',
          width: 200
        },
        {
          type: ColumnType.Action,
          label: 'Action',
          width: 100,
          action: [
            {
              type: Act.Download,
              icon: CommonConstant.ICON_FT_DOWNLOAD,
              class: CommonConstant.TEXT_PRIMARY,
              descr: 'Download'
            }
          ]
        }
      ]
    }
  }

  async listView(pageNumber: number = 1){
    const request = new ListReportRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.page = pageNumber;
 
   await this.manualReportService.getListReport(request).toPromise().then(response => {
    if (response.status.code === 0) {
      this.datasource.next(response);
      console.log('response', response)
    }
   });
  }

  async getPage(page: Page) {
    console.log('requestPage', page);
    await this.listView(page.pageNumber + 1).then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  onItemClick(event) {
    const data = event['data'];

    switch(event.act.type) {
      case Act.Download:
      return this.gotoDownload(data,true);
    }
  }


  gotoDownload(data: ListReportDownload, download: boolean) {
    console.log('Data', data);
    const request: DownloadReportRequest = new DownloadReportRequest();
    request.idManualReport = data.idManualReport;

    this.manualReportService.downloadReport(request).toPromise().then(response => {
      if (response.status.code === 0) {
          const downloadLink = document.createElement('a');
          const fileName = data.filename;

          downloadLink.href = `data:application/xlsx;base64, ${response.xlBase64}`;
          downloadLink.download = fileName;
          downloadLink.click();
        }
      }
    );
  }
 
}
