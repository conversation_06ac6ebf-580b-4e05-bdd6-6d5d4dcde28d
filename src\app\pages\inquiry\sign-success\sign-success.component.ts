import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { GlobalService } from 'app/shared/data/global.service';
import {FeedbackComponent} from '../../../shared/components/feedback/feedback.component';

@Component({
  selector: 'app-sign-success',
  templateUrl: './sign-success.component.html',
  styleUrls: ['./sign-success.component.scss']
})
export class SignSuccessComponent implements OnInit {

  documentId : string;
  tenantCode: string;
  isFromParent: boolean = false;
  isEmbed: boolean = false;

  constructor(private modalService: NgbModal, private activatedRoute: ActivatedRoute, private route: Router,
    private global: GlobalService) {
    this.activatedRoute.queryParams.subscribe(
      (params) => {
        if (params['isFromParent'] != null) {
          this.isFromParent = params['isFromParent'];
        }
        if (params['documentId'] != null) {
          this.documentId = params['documentId'];
        }
        if (params['isEmbed'] != null) {
          this.isEmbed = params['isEmbed'];
        }
        if (params['tenantCode']) {
          this.tenantCode = params['tenantCode'];
        } else {
          this.tenantCode = this.global.user.role.tenantCode;
        }
      }
    );
   }

  async ngOnInit() {
    if (!this.isFromParent) {
      if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
        
        if (this.documentId != null) {
          window.location.href = window.location.href + '&isFromParent=true&isEmbed=true';
        } else {
          window.location.href = window.location.href + '?isFromParent=true&isEmbed=true';
        }
        this.global.user.pathSrc = '';

      } else if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {

        if (this.documentId != null) {
          window.parent.location.href = window.location.href + '&isFromParent=true&isEmbed=true';
        } else {
          window.parent.location.href = window.location.href + '?isFromParent=true&isEmbed=true';
        }
        this.global.user.pathSrc = '';

      } else {

        if (this.documentId != null) {
          window.top.location.href = window.location.href + '&isFromParent=true';
        } else {
          window.top.location.href = window.location.href + '?isFromParent=true';
        }

      }
    } else {
      
      const modal = this.modalService.open(FeedbackComponent);
      modal.componentInstance.documentId = this.documentId;
      modal.componentInstance.tenantCode = this.tenantCode;
      modal.dismissed.subscribe(result => {
        console.log('Result', result);
      });

      if (this.isEmbed) {

        if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {

          const extras: NavigationExtras = {
            queryParams: {
              msg: this.global.msg,
              tenantCode: this.tenantCode
            }
          };

          this.route.navigate([PathConstant.EMBED_V2_DASHBOARD], extras);

        } else {

          const extras: NavigationExtras = {
            queryParams: {
              msg: this.global.msg,
            }
          };
          this.route.navigate([PathConstant.EMBED_DASHBOARD], extras);
          
        }

      } else {
        this.route.navigate([PathConstant.DASHBOARD]);
      }
    }
  }
}
