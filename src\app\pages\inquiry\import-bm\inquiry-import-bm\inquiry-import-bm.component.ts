import { Component, OnInit } from '@angular/core';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { But<PERSON> } from 'app/shared/components/msx-view/models/Button';
import { NavigationExtras, Router } from '@angular/router';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { UserManagement } from 'app/model/user-management';
import { QuestionTextbox, QuestionDropdown, QuestionDate } from 'app/shared/components/ms-form/questions';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { InquiryImportBm } from 'app/model/InquiryImporBm';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Console } from 'console';
import { DownloadTemplateInsertAutosignBmRequest } from 'app/model/api/downloadTemplateInsertAutosignBmRequest';
import { AuditContext } from 'app/model/audit.context';
import { GlobalService } from 'app/shared/data/global.service';
import { saveAs } from 'file-saver';
import { HttpClient } from '@angular/common/http';


@Component({
  selector: 'app-inquiry-import-bm',
  templateUrl: './inquiry-import-bm.component.html',
  styleUrls: ['./inquiry-import-bm.component.scss']
})
export class InquiryImportBmComponent implements OnInit {

  view: MsxView;
  buttonList: Button[];
  serviceUrl = URLConstant.inquiryImportExcelAutosignBm;

  constructor(private router: Router,  private global: GlobalService, private http: HttpClient) { }

  ngOnInit(): void {
    this.initiateView();
    this.buttonList = [
      {name: 'Download Template', class: 'btn btn-secondary', hide: false, icon: 'ft-download'},
      {name: 'New', class: 'btn btn-primary', hide: false, icon: 'ft-plus'}
    ]
  }

  initiateView() {
    const searchFilter = {
      name: 'listUserManagementSearchFilter',
      colSize: 6,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        new QuestionDate({
          key: 'importTimeStart',
          label: 'Import Time Start',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDate({
          key: 'importTimeEnd',
          label: 'Import Time End',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDropdown({
          key: 'statusImport',
          label: 'Delivery Status',
          placeholder: 'Select Delivery Status',
          options: [
              {key: '', value: 'All'},
              {key: 'Waiting', value: 'Waiting'},
              {key: 'In Process', value: 'In Process'},
              {key: 'Done', value: 'Done'}
          ],
          value: ''
        }),
      ],
      params: [
        {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
        }
      ]
    };

    const inquirtImportBmTable: Table<InquiryImportBm> = {
      name: 'importProcessAutosignBmBean',
      list: [],
      columns: [
        {
          type: ColumnType.Date,
          format: CommonConstant.FORMAT_DATE_WITHTIME_SECOND_MILISECOND,
          prop: 'requestImportTime',
          label: "Request Import Time",
          width: 150
        },
        {
          type: ColumnType.Text,
          label: 'Execute Type',
          prop: 'executeType',
          width: 150
        },
        {
          type: ColumnType.Text,
          label: 'Status',
          prop: 'statusImport',
          width: 150
        },
        {
          type: ColumnType.Text,
          label: 'Filename',
          prop: 'fileName',
          width: 150
        },
        {
          type: ColumnType.Text,
          label: 'Total',
          prop: 'total',
          width: 150
        },
        {
          type: ColumnType.Action,
          label: 'Action',
          width: 100,
          action: [
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-eye',
              type: Act.View,
              condition: true,
              conditionedClass: 'd-none',
              conditionVariable: 'statusImport',
              conditionExpected: 'Waiting',
              descr: 'View'
            }
          ]
        }
      ]
    };

    this.view = {
      title: 'Inquiry Proses Data Autosign BM',
      components: [
        {
          type: WidgetType.SearchFilter,
          component: searchFilter
        },
        {
          type: WidgetType.Datatable,
          component: inquirtImportBmTable
        }
      ]
    }

  }

  onBtnClickListener(event) {
    switch (event['name']) {
      case this.buttonList[0].name:
        const request = new DownloadTemplateInsertAutosignBmRequest();
        request.audit = new AuditContext();
        request.audit.callerId = this.global.user.loginId;

        
        this.http.post<any>(URLConstant.downloadTemplateExcelBmAutosign, request).subscribe(response => {
          if (response['status']['code'] === 0) {
            const blob = this.b64toBlob(response['excelBase64']);
            saveAs(blob, response['filename']);
          }
        });

        break;
      case this.buttonList[1].name:
        console.log(PathConstant.IMPORT_EXCEL_AUTOSIGN_BM);
        this.router.navigate([PathConstant.IMPORT_EXCEL_AUTOSIGN_BM]);
        break;
      default:
        break;
    }
  }

  
  onItemClick(event) {
    const data = event['data'];

    switch (event['act']['type']) {
      case Act.View:
        const extras: NavigationExtras = {
          state: {
            requestDate: event.data.requestImportTime,
            fileName: event.data.fileName
          }
        };

        this.router.navigate([PathConstant.DETAIL_IMPORT_BM], extras);
    }
  }
  
  b64toBlob(b64Data, contentType = '', sliceSize = 512) {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }

}
