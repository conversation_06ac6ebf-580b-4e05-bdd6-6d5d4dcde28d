import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { InquiryRoutingModule } from './inquiry-routing.module';
import { InquiryComponent } from './inquiry.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {NgSelectModule} from '@ng-select/ng-select';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap';
import {PipeModule} from '../../shared/pipes/pipe.module';
import {NgxDatatableModule} from '@swimlane/ngx-datatable';
import {SharedModule} from '../../shared/shared.module';
import {PdfJsViewerModule} from 'ngx-pdfjs-viewer';
import { InquiryDetailComponent } from './inquiry-detail/inquiry-detail.component';
import { SignDocumentComponent } from './sign-document/sign-document.component';
import { SignerComponent } from './modal/signer/signer.component';
import { SignSuccessComponent } from './sign-success/sign-success.component';
import { DemoComponent } from './modal/demo/demo.component';
import { BulkSignComponent } from './bulk-sign/bulk-sign.component';
import { BulkSignDigisignComponent } from './bulk-sign-digisign/bulk-sign-digisign.component';
import { ViewDocumentInquiryComponent } from './view-document-inquiry/view-document-inquiry.component';
import { NgxSpinnerModule} from 'ngx-spinner';
import { SigDocumentEmbedComponent } from './sig-document-embed/sig-document-embed.component';
import { InvitationRegisterComponent } from './invitation-register/invitation-register.component';
import { ViewInvitationLinkComponent } from './invitation-register/modal/view-invitation-link/view-invitation-link.component';
import { CheckStatusDocumentService } from 'app/services/check-status-document.service';
import { ProgressSigningComponent } from './progress-signing/progress-signing.component';
import { BulkSignEsignComponent } from './bulk-sign-esign/bulk-sign-esign.component';
import { SignerSigningOtpVerificationComponent } from './signer-signing-otp-verification/signer-signing-otp-verification.component';
import { SigningRequestCompleteComponent } from './signing-request-complete/signing-request-complete.component';
import { SignerSigningVerificationComponent } from './modal/signer-signing-verification/signer-signing-verification.component';
import { MustLivenessPopupComponent } from './modal/must-liveness-popup/must-liveness-popup/must-liveness-popup.component';
import { CertificateExpiredRegenerateInvLinkComponent } from './modal/certificate-expired-regenerate-inv-link/certificate-expired-regenerate-inv-link.component';
import { InquiryImportBmComponent } from './import-bm/inquiry-import-bm/inquiry-import-bm.component';
import { MsFormModule } from "../../shared/components/ms-form/ms-form.module";
import { ImportBmExcelComponent } from './import-bm/import-bm-excel/import-bm-excel.component';
import { DetailImportBmComponent } from './import-bm/detail-import-bm/detail-import-bm.component';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { InquiryAuditTrailComponent } from './audit-trail/inquiry-audit-trail/inquiry-audit-trail.component';
import { ModalAuditTrailDetailComponent } from './audit-trail/modal-audit-trail-detail/modal-audit-trail-detail.component';
import { ModalAuditRelatedDocumentComponent } from './audit-trail/modal-audit-related-document/modal-audit-related-document.component';
import { SendNotifitationTestingComponent } from './send-notifitation-testing/send-notifitation-testing.component';


@NgModule({
  declarations: [
    InquiryComponent,
    InquiryDetailComponent,
    SignDocumentComponent,
    SignerComponent,
    SignSuccessComponent,
    DemoComponent,
    BulkSignComponent,
    BulkSignDigisignComponent,
    ViewDocumentInquiryComponent,
    SigDocumentEmbedComponent,
    InvitationRegisterComponent,
    ViewInvitationLinkComponent,
    ProgressSigningComponent,
	  BulkSignEsignComponent,
    SignerSigningVerificationComponent,
    SignerSigningOtpVerificationComponent,
    SigningRequestCompleteComponent,
    MustLivenessPopupComponent,
    CertificateExpiredRegenerateInvLinkComponent,
    InquiryImportBmComponent,
    ImportBmExcelComponent,
    DetailImportBmComponent,
    InquiryAuditTrailComponent,
    ModalAuditTrailDetailComponent,
    ModalAuditRelatedDocumentComponent,
    SendNotifitationTestingComponent
  ],

  imports: [
    CommonModule,
    InquiryRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgbModule,
    PipeModule,
    NgxDatatableModule,
    SharedModule,
    PdfJsViewerModule,
    NgxSpinnerModule,
    MsFormModule,
    PdfViewerModule
],
  entryComponents: [
    SignerComponent,
    SignSuccessComponent,
    BulkSignComponent,
    ViewDocumentInquiryComponent
  ],
  providers: [
    CheckStatusDocumentService
  ]
})
export class InquiryModule { }
