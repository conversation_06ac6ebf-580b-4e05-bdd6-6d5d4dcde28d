import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { FormModel } from 'app/shared/components/ms-form/models';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { URLConstant } from 'app/shared/constant/URLConstant';
import * as swalFunction from '../../../../shared/data/sweet-alerts';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { GlobalService } from 'app/shared/data/global.service';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { TenantSettings } from 'app/model/tenant-settings';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { Tenant } from 'app/model/tenant';
import { ActivatedRoute, Router } from '@angular/router';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { EditTenantSettingsComponent } from '../edit-tenant-settings/edit-tenant-settings.component';

@Component({
  selector: 'app-tenant-settings-list',
  templateUrl: './tenant-settings-list.component.html',
  styleUrls: ['./tenant-settings-list.component.scss']
})
export class TenantSettingsListComponent implements OnInit {

  state: any;
  routeData: any;

  view: MsxView;
  searchFormObj: FormModel<any> = new FormModel<any>();
  form: FormGroup;
  serviceUrl= URLConstant.GetTenantSettingsList;
  swal = swalFunction;
  buttonList: Button[] = [];

  constructor(private global: GlobalService, private router: Router, private activatedRoute: ActivatedRoute,
    private modalService: NgbModal
  ) { 
    this.routeData = this.activatedRoute.snapshot.data;

    this.activatedRoute.queryParams.subscribe(() => {
      this.state = this.router.getCurrentNavigation().extras.state;
      console.log('state', this.state);
    })
  }

  ngOnInit(): void {
    this.initView();
    this.buttonList = [
      {name: 'Back', class: 'btn btn-primary', hide: false, icon: 'ft-rotate-ccw'}
    ]
  }

  onBtnClickListener(event) {
    switch (event['name']) {
      case this.buttonList[0].name:
        this.router.navigate([PathConstant.TENANT]);
        break;
      default:
        break;
    }
  }

  initView() {
    this.searchFormObj = {
      name: 'SearchForm',
      colSize: 6,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      autoload: true,
      components: [
        {
          key: 'tenantCode',
          label: 'Tenant Code',
          controlType: FormConstant.TYPE_TEXT,
          value: this.state.tenantCode,
          readonly: true
        },
        new QuestionDropdown({
          key: 'settingType',
          label: 'Setting Type',
          placeholder: 'Select Setting Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'TENANT_SETTING_TYPE'
          }
        }),
        new QuestionDropdown({
          key: 'configured',
          label: 'Configured',
          placeholder: 'Select Configuration',
          options: [
            {key: '', value: 'All'},
            {key: 'Yes', value: 'Yes'},
            {key: 'No', value: 'No'}
          ],
        })
      ],
      params: [
        {
          key: 'audit',
          controlType: FormConstant.TYPE_HIDDEN,
          value: {
            callerId: this.global.user.loginId
          }
        },
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        }
      ]
    };

    const TenantSettingsTable: Table<TenantSettings> = {
      name: 'tenantSettings',
      list: [],
      columns: [
        {
          type: ColumnType.Number,
          prop: 'no',
          label: 'No.',
          width: 5
        },
        {
          type: ColumnType.Text,
          prop: 'settingType',
          label: 'Setting Type',
          width: 80
        },
        {
          type: ColumnType.Text,
          prop: 'value',
          label: 'Value',
          width: 80
        },
        {
          type: ColumnType.Action,
          label: 'Action',
          width: 100,
          action: [
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-edit',
              type: Act.Edit,
              descr: 'Edit'
            }
          ]
        }
      ]
    };

    this.view = {
      title: 'Tenant Settings',
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: TenantSettingsTable
        }
      ]
    }
  }

  onItemClickListener(event) {
    const data = event['data'];

    switch(event.act.type) {
      case Act.Edit:
        const modal = this.modalService.open(EditTenantSettingsComponent, { backdrop: 'static', keyboard: false, size: 'l' });
        modal.componentInstance.settingType = data.settingType;
        modal.componentInstance.settingTypeCode = data.settingTypeCode;
        modal.componentInstance.tenantCode = this.state.tenantCode;
        modal.componentInstance.value = data.value;
    }
  }
}
