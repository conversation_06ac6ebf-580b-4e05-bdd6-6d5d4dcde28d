import { BalanceMutation } from "app/model/balance-mutation";
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { CommonConstant } from "app/shared/constant/common.constant";

const BalanceMutationFilter: FormModel<string> = {
    name: 'BalanceMutationSearchForm',
    direction: FormConstant.DIRECTION_HORIZONTAL,
    colSize: 6,
    components: [
      {
        key: 'balanceType',
        label: 'Balance Type',
        placeholder: 'Select Balance Type',
        controlType: FormConstant.TYPE_DROPDOWN,
        value: ''
      },
      {
        key: 'referenceNo',
        label: 'Reference Number',
        placeholder: 'Type reference number here',
        controlType: FormConstant.TYPE_TEXT,
        value: ''
      },
      {
        key: 'transactionType',
        label: 'Transaction Type',
        placeholder: 'Select Transaction Type',
        controlType: FormConstant.TYPE_DROPDOWN,
        value: ''
      },
      {
        key: 'documentName',
        label: 'Document Name',
        placeholder: 'Type document name here',
        controlType: FormConstant.TYPE_TEXT,
        value: ''
      },
      {
        key: 'transactionDateStart',
        label: 'Transaction Date from',
        placeholder: CommonConstant.FORMAT_DATE,
        controlType: FormConstant.TYPE_DATE
      },
      {
        key: 'transactionDateEnd',
        label: 'Transaction Date to',
        placeholder: CommonConstant.FORMAT_DATE,
        controlType: FormConstant.TYPE_DATE
      },
      {
        key: 'documentType',
        label: 'Document Type',
        controlType: FormConstant.TYPE_DROPDOWN,
        placeholder: 'Select Document Type',
      }
    ],
    params: [
      {
        key: 'page',
        controlType: FormConstant.TYPE_HIDDEN,
        value: 1
      }
    ]
  };
  
  const BalanceMutationTable: Table<BalanceMutation> = {
    name: 'listBalanceMutation',
    list: [],
    columns: [
      {
        type: ColumnType.Text,
        prop: 'transactionDate',
        label: 'Trx Date',
        width: 60
      },
      {
        type: ColumnType.Text,
        prop: 'transactionType',
        label: 'Trx Type',
        width: 100
      },
      {
        type: ColumnType.Text,
        prop: 'customerName',
        label: 'Trx By',
        width: 200
      },
      {
        type: ColumnType.Text,
        prop: 'refNumber',
        label: 'Ref No',
        width: 50
      },
      {
        type: ColumnType.Text,
        prop: 'documentType',
        label: 'Doc Type',
        width: 50
      },
      {
        type: ColumnType.Text,
        prop: 'documentName',
        label: 'Doc Name',
        width: 50
      },
      {
        type: ColumnType.IsActive,
        prop: 'qty',
        label: 'Qty',
        width: 50
      },
      {
        type: ColumnType.IsActive,
        prop: 'balance',
        label: 'Balance',
        width: 50
      }
    ]
  };
  
  export const BalanceMutationListView: MsxView = {
    title: 'Balance Mutation',
    components: [
      {
        type: WidgetType.SearchFilter,
        component: BalanceMutationFilter
      },
      {
        type: WidgetType.Datatable,
        component: BalanceMutationTable
      }
    ]
  };