import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { TranslateModule } from "@ngx-translate/core";
import { PrivacyPolicyRoutingModule } from "./privacy-policy-routing.module";
import { PrivacyPolicyComponent } from "./privacy-policy.component";

@NgModule({
    declarations: [PrivacyPolicyComponent],
    imports: [
      CommonModule,
      NgbModule,
      ReactiveFormsModule,
      PrivacyPolicyRoutingModule,
      TranslateModule
    ]
  })
  export class PrivacyPolicyModule { }