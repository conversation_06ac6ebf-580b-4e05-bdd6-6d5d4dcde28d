import { Component, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { VerifyChangeComponent } from '../verify-change/verify-change.component';
import {NavigationExtras, Router} from '@angular/router';
import {PathConstant} from '../../../shared/constant/PathConstant';

@Component({
  selector: 'app-change-data',
  templateUrl: './change-data.component.html',
  styleUrls: ['./change-data.component.scss']
})
export class ChangeDataComponent implements OnInit {

  emailChecked = false;
  noHpChecked = false;
  prevEmailValue = false;
  prevNoHpValue = false;

  constructor(private modalService: NgbModal, private router: Router) { }

  ngOnInit(): void {
  }

  onClickEmail() {
    this.emailChecked = (this.prevEmailValue) ? false : true;
    this.noHpChecked = false;
    this.prevNoHpValue = false;
    this.prevEmailValue = this.emailChecked;
  }

  onClickNoHp() {
    this.noHpChecked = (this.prevNoHpValue) ? false : true;
    this.emailChecked = false;
    this.prevEmailValue = false;
    this.prevNoHpValue = this.noHpChecked;
  }

  openOtpModal() {
    if (!this.emailChecked && !this.noHpChecked) {
      return;
    }

    const modal = this.modalService.open(VerifyChangeComponent, { backdrop: 'static', keyboard: false });
    modal.componentInstance.isEmail = this.emailChecked;

    modal.result.then(
      (response) => {
        if (response['status']['code'] === 0) {
          const extras: NavigationExtras = {
            state: {
              isEmail: this.emailChecked
            }
          }

          this.router.navigate([PathConstant.CHANGE_DATA_USER_INPUT], extras);
        }
      }
    )
  }

}
