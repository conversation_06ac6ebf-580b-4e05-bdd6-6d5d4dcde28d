<div id="navtop" class="navbar  " >
    <a class="navbar-brand" translate>Sign Document</a>

</div>

<div id='main-body'>
    <div id="pdf-main-container" style="margin-bottom: 57px; position: relative;">
      <div>
        <ul class="list-group">
          <li class="list-group-item" style="word-break: break-all;" *ngFor="let index of documents">
            <input type="checkbox" class="option-input checkbox" id="checker"  value="483874" checked disabled style="float: left; margin-bottom: 1.0rem;"/>
            <label style="margin-top: 1.3rem;width: 65%;display: block;float: left;">{{ index.docTemplateName }}</label>
          </li> 
        </ul>
      </div>
      <div id="navbottoms" class="navbars" >
        <button class="btn btn-success block" style="font-size: 1.5rem; width: 100%;" (click)="getSignBalance()" translate>Process</button>
      </div>
      
    </div>
    <ngx-spinner style="visibility: hidden;"></ngx-spinner>
</div>

<app-footer></app-footer>