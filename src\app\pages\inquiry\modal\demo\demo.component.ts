import {Component, Input, OnInit, ViewEncapsulation} from '@angular/core';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import {FormBuilder, Validators} from '@angular/forms';
import {ToastrService} from 'ngx-toastr';
import * as swalFunction from '../../../../shared/data/sweet-alerts';
import {Router} from '@angular/router';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-demo',
  templateUrl: './demo.component.html',
  styleUrls: ['./demo.component.scss', '/assets/sass/libs/datatables.scss'],
  encapsulation: ViewEncapsulation.None
})
export class DemoComponent implements OnInit {
  @Input()
  image: string;
  templateForm: any;

  isOtp = false;
  isOtpSms = false;
  isOtpEmail = false;
  timeLeft: number;
  interval;

  swal = swalFunction;

  constructor(public activeModal: NgbActiveModal, private formBuilder: FormBuilder, private toastrService: ToastrService,
              private router: Router) { }

  ngOnInit(): void {
    this.templateForm = this.formBuilder.group({
      isOtp: ['', Validators.required],
      reason: [''],
      otp: ['']
    });
  }

  dismiss() {
    this.activeModal.dismiss({act: 'signing-success'});
  }

  onSubmit() {
    this.swal.Confirm('Melakukan tandatangan pada dokumen ini',
      'Apakah anda setuju?').then(result => {
      if (result.isConfirmed === true) {
        if (this.templateForm.controls['otp'].value === '') {
          this.swal.Error('Kode OTP tidak boleh kosong!');
        } else if (this.templateForm.controls['otp'].value === '123456') {
          if (this.templateForm.controls['isOtp'].value === 'Iya') {
            this.activeModal.dismiss({act: 'signing-success'});
            this.toastrService.success(`Dokumen sudah tertandatangan`, null, {
              positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
            });
          } else {
            this.activeModal.dismiss({act: 'signing-failed'});
            this.router.navigate(['inquiry', 'detail'], {queryParams: {contractRef: '0002AG202'}});
          }
        } else {
          this.swal.Error('Kode OTP tidak sesuai!');
        }
      }
    });
  }

  sendOtpSms() {
    this.isOtp = true;
    this.isOtpSms = true;
    this.isOtpEmail = false;
    this.startTimer();
  }

  sendOtpEmail() {
    this.isOtp = true;
    this.isOtpSms = false;
    this.isOtpEmail = true;
    this.startTimer();
  }

  startTimer() {
    this.timeLeft = 10;
    this.interval = setInterval(() => {
      if (this.timeLeft > 1) {
        this.timeLeft--;
      } else {
        this.isOtp = false;
        this.timeLeft = -1;
        clearInterval(this.interval);
      }
    }, 1000);
  }
}
