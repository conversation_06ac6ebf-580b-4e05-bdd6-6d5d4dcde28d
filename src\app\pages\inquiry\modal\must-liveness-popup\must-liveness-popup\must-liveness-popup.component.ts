import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BaseResponse } from 'app/model/api/base.response';
import { SignerSigningOtpVerificationComponent } from 'app/pages/inquiry/signer-signing-otp-verification/signer-signing-otp-verification.component';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { SentOtpSigningVerificationRequest } from 'app/shared/dto/otp/sent-otp-signing-verification.request';
import * as swalFunction from '../../../../../shared/data/sweet-alerts';
import { Document } from 'app/model/document';
import { GetAvailableOtpSendingPointsRequest } from 'app/model/api/get-available-otp-sendingpoints.request';
import { TenantService } from 'app/services/api/tenant.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-must-liveness-popup',
  templateUrl: './must-liveness-popup.component.html',
  styleUrls: ['./must-liveness-popup.component.scss']
})
export class MustLivenessPopupComponent implements OnInit {

  @Input() phoneNo: string;
  @Input() vendorCode: string;
  @Input() tenantCode: string;
  @Input() verificationType: string;
  @Input() documents : Document[];
  @Input() loginId: string;
  @Input() msg: string;
  @Input() message: string;
  swal = swalFunction;

  isWa: boolean;
  isSms: boolean;
  isEmail: boolean;
  sendMedia: string;
  sendMediaForm: FormGroup;

  constructor(public activeModal: NgbActiveModal, private modalService: NgbModal, private router: Router,
    private global: GlobalService, private http: HttpClient, private tenantService: TenantService,
    private formBuilder: FormBuilder) { }

  async ngOnInit(): Promise<void> {
    this.sendMediaForm = this.formBuilder.group({
      sendMedia: ['', Validators.required]
    });

    await this.getAvailableSendingPoint();
  }

  goToOTP(){
    this.modalService.dismissAll();

    let urlOtp: string;
    const request = new SentOtpSigningVerificationRequest();

    request.documentId = [];
    for (const doc of this.documents) {
      request.documentId.push(doc.documentId);
    }

    if (this.msg && this.router.url.includes('/embed/') &&  this.router.url.includes('/V2/')) {

      request.phoneNo = this.phoneNo;
      request.vendorCode = this.vendorCode;
      request.tenantCode = this.tenantCode;
      request.sendingPointOption = this.sendMediaForm.controls.sendMedia.value;
      request.msg = this.msg;
      urlOtp = URLConstant.sentOtpSigningVerificationEmbedV2;

    } else {
      request.phoneNo = this.phoneNo;
      request.vendorCode = this.vendorCode;
      request.tenantCode = this.global.user.role.tenantCode;
      request.sendingPointOption = this.sendMediaForm.controls.sendMedia.value;
      urlOtp = URLConstant.sentOtpSigningVerification;
    }
    
    this.http.post<BaseResponse>(urlOtp, request).subscribe((response) => {
      if (response.status.code !== 0) {

        this.swal.Error(response.status.message);
        console.log('Error', response.status.message);

      } else {
        const modal = this.modalService.open(SignerSigningOtpVerificationComponent, { backdrop: 'static', keyboard: false, size: 'l' });
        modal.componentInstance.phoneNo = this.phoneNo;
        modal.componentInstance.vendorCode = this.vendorCode;
        modal.componentInstance.documents = this.documents;
        modal.componentInstance.msg = this.msg;
        modal.componentInstance.tenantCode = this.tenantCode;
        modal.componentInstance.loginId = this.loginId;
        modal.componentInstance.isSms = this.isSms;
        modal.componentInstance.isWa = this.isWa;
        modal.componentInstance.isEmail = this.isEmail;
        modal.componentInstance.sendMedia = this.sendMediaForm.controls.sendMedia.value;
        modal.componentInstance.durationResendOTP = response['durationResendOTP'];
        console.log(response['durationResendOTP']);
      }
    });
  }

  async getAvailableSendingPoint() {
    let getAvailableSendingPointUrl;
    const request = new GetAvailableOtpSendingPointsRequest();
    request.tenantCode = this.tenantCode;
    request.vendorCode = this.vendorCode;

    if (this.msg && this.router.url.includes('/embed/') &&  this.router.url.includes('/V2/')) {
      getAvailableSendingPointUrl = URLConstant.getAvailableSendingOptionsEmbedV2;
      request.msg = this.msg;
    }else {
      getAvailableSendingPointUrl = URLConstant.GetAvailableSendingPoint;
      request.loginId = this.loginId;
    }


    this.tenantService.getAvailableSendingPointSecured(request, getAvailableSendingPointUrl).subscribe(
      response => {
        if (response.status.code === 0) {
          const available = response.listAvailableOptionSendingPoint;
          if(available.length > 1) {
            if (available.includes('SMS')) {
              this.isSms = true;
            }
  
            if (available.includes('WA')) {
              this.isWa = true;
            }

            if (available.includes('EMAIL')) {
              console.log("email");
              this.isEmail = true;
            }
  
            this.sendMediaForm.patchValue({
              sendMedia: response.defaultAvailableOptionSendingPoint
            });
          } else {
            if (available[0] === 'SMS') {
              this.isSms = true;
            } else if (available[0] === 'WA') {
              this.isWa = true;
            } else {
              this.isEmail = true;
            }
            this.sendMediaForm.patchValue({
              sendMedia: available[0]
            });
          }
        }
      }
    )
  }

  cancel(){
    this.modalService.dismissAll();
  }
}
