import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { SendOtpChangeProfileRequest } from 'app/shared/dto/otp/send-otp-change-profile.request';
import { VerifyOtpChangeProfilelRequest } from 'app/shared/dto/otp/verify-otp-change-profile.request';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from '../../../shared/data/sweet-alerts';

@Component({
  selector: 'app-verify-change',
  templateUrl: './verify-change.component.html',
  styleUrls: ['./verify-change.component.scss']
})
export class VerifyChangeComponent implements OnInit {
  @Input() isEmail: boolean;
  @Input() verificationType: string;
  @Input() fullname: string;
  @Input() msg: string;
  templateForm: any;
  emailOrPhone: string;

  timeLeft = 60;
  interval;

  swal = swalFunction;

  constructor(public activeModal: NgbActiveModal, private formBuilder: FormBuilder, private toastrService: ToastrService,
              private router: Router, private http: HttpClient, private global: GlobalService) {
  }

  ngOnInit(): void {
    this.templateForm = this.formBuilder.group({
      otp: ['', Validators.required]
    });
    if (localStorage.getItem('timeLeft') != null) {
      this.timeLeft = Number(localStorage.getItem('timeLeft'));
      this.startTimer();
    }
    this.sendOtp();
  }

  dismiss() {
    this.activeModal.dismiss('0');
    clearInterval(this.interval);
    localStorage.setItem('timeLeft', this.timeLeft.toString());
    localStorage.setItem('oldEmail', this.emailOrPhone);
  }

  openSuccessPopup() {
    this.toastrService.success(`Verifikasi berhasil`, null, {
      positionClass: 'toast-top-right'
    });
  }

  openWrongCodePopup() {
    this.swal.Error('Kode OTP tidak sesuai!');
  }

  onSubmit() {
    var request = new VerifyOtpChangeProfilelRequest();
    request.audit = this.global.audit;
    request.loginId = this.global.user.loginId;
    request.otpCode = this.templateForm.controls.otp.value;
    this.http.post(URLConstant.VerifyOtpChangeProfile, request).subscribe(
      (response) => {
        if (response['status']['code'] !== 0) {
          this.openWrongCodePopup();
          console.log('Error', response);
          return;
        }

        this.openSuccessPopup();
        this.activeModal.close(response);
      }
    )
  }

  sendOtp() {
    var request = new SendOtpChangeProfileRequest();
    if (this.isEmail) {
      request.isEmail = "1";
    } else {
      request.isEmail = "0";
    }
    request.tenantCode = this.global.user.role?.tenantCode;
    request.audit = this.global.audit;
    this.http.post(URLConstant.SendOtpChangeProfile, request).subscribe(
      (response) => {
        if (response["status"]["code"]  != 0) {
          this.swal.Error(response["status"]["message"]);
          console.error(response);
          return;
        } else {
          this.emailOrPhone = response["emailOrPhone"];
        }
      }
    )

    this.timeLeft = 60;
    clearInterval(this.interval);
    this.startTimer();
  }

  startTimer() {
    this.interval = setInterval(() => {
      if (this.timeLeft > 1) {
        this.timeLeft--;
      } else {
        this.timeLeft = -1;
      }
    }, 1000);
  }

}
