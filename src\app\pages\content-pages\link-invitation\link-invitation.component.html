<div class="row" style="margin-top: 15px;">
    <div class="col-6">
        <div class="content-header" style="margin-top: 0 !important;" translate>
            SMS Delivery Setting
        </div>
    </div>
</div>

<div class="row match-height">
    <div class="col-6">
        <div class="card">
            <div class="card-content">
                <div class="card-body">
                    <form [formGroup]="linkSettingsForm">
                        <p class="content-header" style="font-size: 13.75px;" translate>Additional Settings for Sending Links to SMS For Email Data</p>
                        <div class="d-flex justify-content-between">
                            <div>
                                <p translate>Invitation Link</p>
                            </div>
                            <div class="form-group">
                                <label class="switch">
                                    <input type="checkbox" formControlName="addonInvLink" id="addonInvLink" (change)="fieldsChangeInvite($event)">
                                    <span class="slider round"></span>
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <div>
                                <p translate>Sign Link</p>
                            </div>
                            <div class="form-group">
                                <label class="switch">
                                    <input type="checkbox" formControlName="addonSignLink" id="addonSignLink" (change)="fieldsChangeSign($event)">
                                    <span class="slider round"></span>
                                </label>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 text-center">
                                <button class="btn btn-light mr-2 mt-1" (click)="goBack()" translate>Cancel</button>
                                <button class="btn btn-info mt-1" (click)="updateLinkSetting()" translate>Save</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>