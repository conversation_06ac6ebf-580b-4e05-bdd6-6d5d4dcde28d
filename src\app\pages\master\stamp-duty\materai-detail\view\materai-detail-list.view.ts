import { StampDutyDetail } from "app/model/stamp-duty-detail";
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { CommonConstant } from "app/shared/constant/common.constant";

export const StampDutyDetailTable: Table<StampDutyDetail> = {
    name: 'stampDutyHist',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'trxNo',
            label: 'Trx No',
            width: 70
        },
        {
            type: ColumnType.Text,
            prop: 'refNo',
            label: 'Ref No',
            width: 120
        },
        {
            type: ColumnType.Text,
            prop: 'docName',
            label: 'Doc Name',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'custName',
            label: CommonConstant.LABEL_CUSTOMER_NAME,
            width: 120
        },
        {
            type: ColumnType.Text,
            prop: 'trxType',
            label: 'Trx Type',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'trxDate',
            label: 'Trx Date',
            width: 120
        },
    ]
}

export const StampDutyDetailListView: MsxView = {
    title: 'Stamp Duty Detail',
    components: [
        {
            type: WidgetType.Datatable,
            component: StampDutyDetailTable
        }
    ]
}