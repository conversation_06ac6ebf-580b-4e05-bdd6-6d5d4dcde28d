import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { StampDutyDetailRequest } from 'app/model/api/stamp.duty.detail.request';
import { StampDutyDetail } from 'app/model/stamp-duty-detail';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { BehaviorSubject } from 'rxjs';
import { StampDutyDetailListView, StampDutyDetailTable } from './view/materai-detail-list.view';

@Component({
  selector: 'app-materai-detail',
  templateUrl: './materai-detail.component.html',
  styleUrls: ['./materai-detail.component.scss']
})

export class MateraiDetailComponent implements OnInit {

  @Input() idStampDuty: string;
  view: MsxView;
  serviceUrl = URLConstant.StampDutyDetail;
  stampDutyDetailTable = StampDutyDetailTable;
  stampDutyHist: {}[] = [];

  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  
  constructor(public activeModal: NgbActiveModal, private global: GlobalService, private http: HttpClient,
    private ngZone: NgZone, private cdr: ChangeDetectorRef) { }

  async ngOnInit() {
    await this.getStampDutyDetail().then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  dismiss() {
    this.activeModal.dismiss('0');
  }

  async getStampDutyDetail() {
    const request = new StampDutyDetailRequest();
    request.idStampDuty = Number(this.idStampDuty);
    request.loginId = this.global.user.loginId;
    request.tenantCode = this.global.user.role.tenantCode;
    await this.http.post(URLConstant.StampDutyDetail, request).toPromise().then(
      (response) => {
        if (response['status']['code'] === 0) {
          this.datasource.next(response);
        }
      }
    )
  }

}
