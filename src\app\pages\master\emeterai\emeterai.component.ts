import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {FormModel} from '../../../shared/components/ms-form/models';
import {BehaviorSubject} from 'rxjs';
import {DocumentTemplate} from '../../../model/template';
import {ActivatedRoute, Router} from '@angular/router';
import {HttpClient} from '@angular/common/http';
import {ToastrService} from 'ngx-toastr';
import {FormConstant} from '../../../shared/components/ms-form/constants/form.constant';
import {QuestionDate, QuestionDropdown, QuestionTextbox} from '../../../shared/components/ms-form/questions';
import {CommonConstant} from '../../../shared/constant/common.constant';
import {URLConstant} from '../../../shared/constant/URLConstant';
import {QuestionFile} from '../../../shared/components/ms-form/questions/question-file';
import {UserProfile} from '../../../model/user-profile';
import {GlobalService} from '../../../shared/data/global.service';
import {PathConstant} from '../../../shared/constant/PathConstant';
import {Signer} from '../../../shared/components/document-anotate/model/signer';
import {FormGroup, Validators} from '@angular/forms';

@Component({
  selector: 'app-emeterai',
  templateUrl: './emeterai.component.html',
  styleUrls: ['./emeterai.component.scss']
})
export class EmeteraiComponent implements OnInit {

  mForm: FormModel<any>;
  vForm: FormGroup;

  state: any;
  msg: string;
  routeData: any;
  refs: any[];
  signers: Signer[];

  private documentTemplate: string;
  dataSubj: BehaviorSubject<DocumentTemplate> = new BehaviorSubject<DocumentTemplate>(null);

  constructor(private activeRoute: ActivatedRoute, private router: Router, private http: HttpClient,
              private toastrService: ToastrService, private global: GlobalService, private cdr: ChangeDetectorRef) {
    this.routeData = this.activeRoute.snapshot.data;
    this.refs = [];
    this.activeRoute.queryParams.subscribe(params => {
      if (params['msg']) {
        this.msg = params['msg'];
      }
    });

    if (this.msg) {
      const user: UserProfile = new UserProfile();
      user.pathSrc = 'Embed';
      user.loginId = 'CONFINS';
      user.role = {
        tenantCode: '',
        tenantName: '',
        roleCode: '',
        roleName: ''
      };
      user.roles = [{
        tenantCode: '',
        tenantName: '',
        roleCode: '',
        roleName: ''
      }];
      this.global.msg = this.msg;
      this.global.user = user;
    }
  }

  ngOnInit(): void {
    localStorage.clear();
    this.initView();
  }

  initView() {
    this.mForm = {
      mode: CommonConstant.MODE_ADD,
      name: 'addEditEmeteraiTemplate',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionTextbox(
          {
            key: 'documentNo',
            label: CommonConstant.LABEL_DOCUMENT_NUMBER,
            placeholder: 'Type document number here',
            maxLength: 20,
            required: true,
            validations: [
              {type: 'required', message: 'Document Number harus di isi.'},
              {type: 'maxlength', message: 'Maksimal jumlah karakter Nomor Dokumen adalah 20'}
            ]
          }),
        new QuestionTextbox(
          {
            key: 'documentName',
            label: 'Document Name',
            placeholder: 'Type document name here',
            maxLength: 100,
            required: true,
            validations: [
              {type: 'required', message: 'Document Name harus di isi.'},
              {type: 'maxlength', message: 'Maksimal jumlah karakter Document Name adalah 100'}
            ]
          }),
        new QuestionDate({
          key: 'documentDate',
          label: 'Document Date',
          placeholder: CommonConstant.FORMAT_DATE,
          width: 140,
          required: true,
          validations: [
            {type: 'required', message: 'Document Date harus di isi.'}
          ]
        }),
        new QuestionDropdown({
          key: 'documentTypePeruri',
          label: 'Document Peruri Type',
          placeholder: 'Select Document Type Peruri',
          serviceUrl: URLConstant.ListMeteraiType,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'documentEMateraiList',
            key: 'peruriDocId',
            value: 'documentName'
          },
          params: {
            msg: this.msg
          },
          required: true,
        }),
        new QuestionDropdown({
          key: 'documentType',
          label: 'Document Type',
          placeholder: 'Select Document Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'DOC_TYPE'
          },
          required: true,
        }),
        new QuestionDropdown({
          key: 'templateDocument',
          label: 'Templat Dokumen',
          placeholder: 'Select Document Template',
          serviceUrl: URLConstant.GetDocumentTemplateEmbed,
          options: [
            { key: 'MANUAL', value: 'MANUAL' }
          ],
          value: 'MANUAL',
          args: {
            list: 'listDocumentTemplate',
            key: 'documentTemplateCode',
            value: 'documentTemplateName'
          },
          params: {
            msg: this.msg
          },
          required: true,
        }),
        {
          controlType: FormConstant.TYPE_CURRENCY,
          key: 'documentAmount',
          label: 'Nominal Dokumen',
          placeholder: 'Type document amount here',
          maxLength: 50,
          required: true,
          validations: [
            {type: 'required', message: 'Document Amount harus di isi.'},
            {type: 'maxlength', message: 'Maksimal jumlah karakter Document Amount adalah 200'}
          ]
        },
        new QuestionFile({
          key: 'documentExample',
          label: 'Document',
          placeholder: 'Choose File',
          accept: 'application/pdf',
          required: true
        }),
        new QuestionDropdown({
          key: 'identityType',
          label: 'Tipe Identitas',
          placeholder: 'Select Tipe Identitas',
          required: true,
          serviceUrl: URLConstant.GetLov,
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'ID_TYPE'
          }
        }),
        new QuestionTextbox(
          {
            key: 'identityNo',
            label: 'No. Identitas',
            placeholder: 'Type identity number here',
            maxLength: 50,
            required: true,
            validations: [
              {type: 'required', message: 'Identity Number harus di isi.'},
              {type: 'minlength', message: 'Minimal jumlah karakter Nomor Identitas adalah 16'},
              {type: 'maxlength', message: 'Maksimal jumlah karakter Nomor Identitas adalah 16'}
            ]
          }),
        new QuestionTextbox(
          {
            key: 'debtName',
            label: 'Nama Debitur',
            placeholder: 'Type debtor name here',
            maxLength: 100,
            required: true,
            validations: [
              {type: 'required', message: 'Debtor Name harus di isi.'},
              {type: 'maxlength', message: 'Maksimal jumlah karakter Debtor Name adalah 100'}
            ]
          }),
      ],
      params: [
      ]
    }
  }

  onNext(data) {
    if (data['isTrusted']) {
      return;
    }

    console.log('mForm', data);
    this.router.navigate([PathConstant.SETTING_DOCUMENT_TEMPLATE_CONFINS], {
      state: {...data, ...{rawTemplate: this.documentTemplate, extras: this.refs, sdt: true, signer: this.signers}},
      queryParams: {msg: this.msg, mode: CommonConstant.MODE_ADD}
    });
  }

  onTemplateFile(data) {
    this.documentTemplate = data;
  }

  onSelect(event) {
    console.log('Event:', event);

    if (event['prop'] === 'identityType') {
      if (event['data']['key'] === 'NIK') {
        const question = this.mForm.components.find(x => x.key === 'identityNo');
        const validation = question.validations.find(v => v.type === 'minlength');
        validation.message = 'Minimal jumlah karakter Nomor Identitas adalah 16';
        this.vForm.get('identityNo').setValidators([Validators.required, Validators.minLength(16), Validators.maxLength(16)]);
        this.cdr.detectChanges();
      } else {
        const question = this.mForm.components.find(x => x.key === 'identityNo');
        const validation = question.validations.find(v => v.type === 'minlength');
        validation.message = 'Minimal jumlah karakter Nomor Identitas adalah 15';
        this.vForm.get('identityNo').setValidators([Validators.required, Validators.minLength(15), Validators.maxLength(16)]);
        this.cdr.detectChanges();
      }

      this.vForm.get('identityNo').updateValueAndValidity();
      this.vForm.get('debtName').setValidators([Validators.required]);
      this.vForm.get('debtName').updateValueAndValidity();
      return;
    }

    if (this.refs.indexOf(event['data']) === -1) {
      this.refs.push(event['data']);
    }

    if (event['prop'] === 'templateDocument') {
      this.getTemplateDoc(event['data']);
    }

    console.log('Selected: ', this.refs);
  }

  onInput(event) {
    console.log('input', event);
    if (event.target && event.target['files']) {
      const files = event.target.files;
      if (files[0].type !== 'application/pdf') {
        this.vForm.get('documentExample').reset();
        this.toastrService.warning('Silahkan pilih dokumen dengan format pdf!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }

      if (files[0].size > 10e+6) {
        this.vForm.get('documentExample').reset();
        this.toastrService.warning('Ukuran file tidak boleh dari 10MB', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }
    }
  }

  onForm(event) {
    this.vForm = event;
  }

  getTemplateDoc(params: {key: string, value: string}) {
    this.http.post<any>(URLConstant.GetTemplateEmbed, {msg: this.msg, documentTemplateCode: params.key})
      .subscribe(result => {
        this.signers = result['listDocumentTemplateSignLocation'];
        console.log('Template Detail', this.signers);
      })
  }

}
