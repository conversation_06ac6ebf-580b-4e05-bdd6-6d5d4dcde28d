import { Component, OnInit } from '@angular/core';
import { QuestionDropdown, QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { FormModel } from 'app/shared/components/ms-form/models';
import { FormGroup } from '@angular/forms';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import {ToastrService} from 'ngx-toastr';
import * as swalFunction from 'app/shared/data/sweet-alerts'
import { URLConstant } from 'app/shared/constant/URLConstant';
import {QuestionFile} from 'app/shared/components/ms-form/questions/question-file';
import { insertExcelAutosignBmRequest } from 'app/model/api/insertExcelAutosignBmRequest';
import { GlobalService } from 'app/shared/data/global.service';
import { AuditContext } from 'app/model/audit.context';
import { PathConstant } from 'app/shared/constant/PathConstant';

@Component({
  selector: 'app-import-bm-excel',
  templateUrl: './import-bm-excel.component.html',
  styleUrls: ['./import-bm-excel.component.scss']
})
export class ImportBmExcelComponent implements OnInit {

  constructor(private toastrService: ToastrService, private http: HttpClient, private router: Router, private fcs: MsxFormControlService,  private global: GlobalService) { }

  // constructor(private fcs: MsxFormControlService,) { }

  rawFile: string;
  formObj: FormModel<any>;
  msxForm: FormGroup;
  formData: any;
  colsize = 6;
  fileName: string;
  change: string;
  base64: string;

  ngOnInit(): void {
    this.setupQuestion();
    const controls = [...this.formObj.params, ...this.formObj.components];
    this.msxForm = this.fcs.toFormGroup(controls);
    console.log('form value', this.msxForm.getRawValue())
  }

  setupQuestion() {
    const time = Date.now();
    const nowday = new Date(time);
    this.formObj = {
      name: 'insertExcelAutosignBm',
      mode: CommonConstant.MODE_ADD,
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionDropdown({
          key: 'executeTime',
          label: 'Execute Type',
          placeholder: 'Execute Type',
          options: [
            { key: 'Now', value: 'Now' },
            { key: 'Next Day', value: 'Next Day' }
          ],
          value: 'Now',
          required: true
        }),
        new QuestionDropdown({
          key: 'psreCode',
          label: 'PSrE',
          placeholder: 'Select PSrE',
          serviceUrl: URLConstant.GetVendorListV2,
          options: [
            { key: '', value: 'Pilih PSrE' }
          ],
          value: '',
          args: {
            list: 'vendorList',
            key: 'code',
            value: 'name'
          },
          params: {
            vendorTypeCode: CommonConstant.VENDOR_TYPE_PSRE
          },
          required: true,
        }),
        new QuestionFile({
          key: 'documentExample',
          label: 'Document',
          placeholder: 'Choose File',
          accept:  ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv', 'application/vnd.ms-excel'],
          required: true
        }),
      ],
      params: []
    }

    const quest = this.getQuestionkey('documentName');
    console.log('documentName', quest);
    console.log('document date', {year: nowday.getFullYear(), month: nowday.getMonth(), day: nowday.getUTCDay} )
  
  
  }

  getQuestionkey(key:string) {
    return this.formObj.components.find(x => x.key === key);
  }

  onSubmit() {
    this.formData = this.msxForm.getRawValue();
    const request  = new insertExcelAutosignBmRequest();
    request.psreCode = this.formData['psreCode'];
    request.executeTime = this.formData['executeTime'];
    request.tenantCode = this.global.user.role.tenantCode;
    request.audit = new AuditContext();
    request.audit.callerId = this.global.user.loginId;
    request.excelBase64 = this.base64;
    request.fileName = this.fileName;

    this.http.post<any>(URLConstant.insertExcelAutosignBm, request).subscribe(response => {
      if (response['status']['code'] === 0) {
        this.toastrService.success('Permintaan pendaftaran BM Autosign berhasil dibuat.', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
      }
      this.router.navigate([PathConstant.IMPORT_AUTOSIGN_BM])
      })
  }

  doBack() {
    this.router.navigate([PathConstant.IMPORT_AUTOSIGN_BM]);
  }

  onInput(event) {
    console.log('input', event);
    if (event.target && event.target['files']) {
      const files = event.target.files;
      const file = files[0];
  
      // Get the filename and file type
      this.fileName = file.name;
      const fileType = file.type;
  
      // Check for valid file types
      const validFileTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv', 'application/vnd.ms-excel'];
      if (!validFileTypes.includes(fileType)) {
        this.msxForm.get('documentExample').reset();
        this.toastrService.warning('Silahkan pilih dokumen dengan format xlsx, csv, atau xls!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }
  
      if (file.size > 10e+6) {
        this.msxForm.get('documentExample').reset();
        this.toastrService.warning('Ukuran file tidak boleh lebih dari 10MB', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }
  
      // Convert file to base64
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.base64 = e.target.result.split(',')[1]; // Extract base64 string
      };
      reader.readAsDataURL(file);
    }
  }
  
  onBase64Data($event) {
    this.rawFile = $event;
  }

  
  
  
}
