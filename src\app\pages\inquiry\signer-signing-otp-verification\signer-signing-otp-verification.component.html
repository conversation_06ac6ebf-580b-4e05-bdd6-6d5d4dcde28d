<form [formGroup]="templateForm" (ngSubmit)="onSubmit()">
    <div class="modal-body">
        <button type="button" class="close" aria-label="Close" (click)="dismiss()">
            <span aria-hidden="true">&times;</span>
        </button>

        <div class="mt-5">
            <div class="row justify-content-center align-items-center">
                <p class="font-large-1 text-center text-bold-700 px-5" translate>Request OTP</p>
            </div>
        </div>

        <div class="form-group">
            <label for="phone" class="form-title" translate>Email / No HP</label>
            <input type="phone" formControlName="phone" id="phone" class="form-control mb-2" placeholder="Insert Email / Phone Number" 
              value="{{sendOtpPlatform}}" required disabled>
            <div class="help-block mt-1 text-success">Berhasil kirim OTP</div>
        </div>

        <div class="form-group">
            <label for="otp" class="form-title" translate>Input OTP</label>
            <input type="tel" formControlName="otp" id="otp" class="form-control mb-2" placeholder="Insert OTP Code"  maxlength="6" id="otp" value="">
        </div>
      
        <div class="timer-box">
            <div *ngIf="timeLeft > 0" class="timer">{{ timeLeft }} s</div>
            <div *ngIf="timeLeft === -1" style="margin-bottom: 5px;" id="ketotp" translate>Didn't get the code?</div>
        </div>

        <div *ngIf="timeLeft === -1" class="send-code-wrapper">
            <form [formGroup]="sendMediaForm" *ngIf="!isPrivy">
                <!-- otp media option -->
                <div class="form-group">
                  <label for="gender" class="form-title" translate>OTP Delivery Medium<span class="mandatory text-danger">*</span></label>
                    <div class="row">
                      <div class="col-12" *ngIf="isSms" [ngStyle]="{ 'margin-top.px': '0' }">
                        <span class="border rounded border-primary d-block">
                          <div class="form-check form-check-inline align-middle p-3">
                            <input class="form-check-input" type="radio" id="sms" formControlName="sendMedia" value="SMS">
                            <label class="form-check-label label-radio-button" for="sms" translate>SMS</label>
                          </div>
                        </span>
                      </div>
                      <div class="col-12" *ngIf="isWa" [ngStyle]="{ 'margin-top.px': '10' }">
                        <span class="border rounded border-primary d-block">
                          <div class="form-check form-check-inline align-middle p-3">
                            <input class="form-check-input" type="radio" id="wa" formControlName="sendMedia" value="WA">
                            <label class="form-check-label label-radio-button" for="wa" translate>WhatsApp</label>
                          </div>
                        </span>
                      </div>
                      <div class="col-12" *ngIf="isEmail" [ngStyle]="{ 'margin-top.px': '10' }">
                        <span class="border rounded border-primary d-block">
                          <div class="form-check form-check-inline align-middle p-3">
                            <input class="form-check-input" type="radio" id="email" formControlName="sendMedia" value="EMAIL">
                            <label class="form-check-label label-radio-button" for="email" translate>Email</label>
                          </div>
                        </span>
                      </div>
                    </div>
                  </div>
              </form>
              <div><a class="reset-button" (click)="sendOtp()" [disabled]="isPrivy || (sendMediaForm.invalid && otpByEmail != '1')" translate>Send code again</a></div>
        </div>
        
        <div class="button-process">
            <button type="button" class="btn btn-light" (click)="dismiss()">Back</button>
            <button type="submit" class="btn btn-proccess" [disabled]="templateForm.invalid" translate>Process</button>
        </div>
    </div>
</form>
