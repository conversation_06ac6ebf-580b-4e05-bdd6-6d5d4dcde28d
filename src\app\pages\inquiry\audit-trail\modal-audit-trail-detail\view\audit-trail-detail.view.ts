import { InquiryAuditTrail } from "app/model/InquiryAuditTrail";
import { Signer } from "app/model/signer";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { CommonConstant } from "app/shared/constant/common.constant";

export const AuditTrailDetailTable: Table<InquiryAuditTrail> = {
    name: 'listinquiryAuditTrailSignProcess',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'notificationType',
            label: "Notification Type",
            width: 150
          },
          {
            type: ColumnType.Text,
            prop: 'notificationVendor',
            label: 'Notification Vendor',
            width: 150
          },
          {
            type: ColumnType.Text,
            prop: 'sendingPoint',
            label: 'Sending Point',
            width: 150
          },
          {
            type: ColumnType.Text,
            prop: 'otpCode',
            label: 'Otp Code',
            width: 150
          },
          {
            type: ColumnType.Text,
            prop: 'notes',
            label: 'Notes',
            width: 150
          }
    ]
}