import { ErrorReport } from './../../model/error-report';
import { Act } from './../../shared/components/msx-datatable/enums/act';
import { QuestionDate } from './../../shared/components/ms-form/questions/question-date';
import { URLConstant } from './../../shared/constant/URLConstant';
import { QuestionDropdown } from './../../shared/components/ms-form/questions/question-dropdown';
import { QuestionTextbox } from './../../shared/components/ms-form/questions/question-textbox';
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { CommonConstant } from 'app/shared/constant/common.constant';

const ErrorReportSearchFilter: FormModel<string> = {
    name: 'errorReportSearchForm',
    direction: FormConstant.DIRECTION_HORIZONTAL,
    colSize: 6,
    components: [
        new QuestionDropdown({
          key: 'modul',
          label: 'Module',
          placeholder: 'Select Module Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'ERR_HIST_MODULE'
          }
        }),
        new QuestionTextbox({
            key: 'refNumber',
            label: 'Ref Number',
            placeholder: '',
            value: ''
        }),
          new QuestionTextbox({
            key: 'namaKonsumen',
            label: 'Consumer Name',
            placeholder: '',
            value: ''
        }),
          new QuestionTextbox({
            key: 'businessLine',
            label: 'Business Line',
            placeholder: '',
            value: ''
        }),
          new QuestionTextbox({
            key: 'cabang',
            label: 'Branch',
            placeholder: '',
            value: ''
        }),
          new QuestionTextbox({
            key: 'region',
            label: 'Region',
            placeholder: '',
            value: ''
        }),
          new QuestionDate({
            key: 'tanggalDari',
            label: 'Date From',
            placeholder: CommonConstant.FORMAT_DATE,
            isDateStart: true
        }),
        new QuestionDate({
            key: 'tanggalSampai',
            label: 'Date To',
            placeholder: CommonConstant.FORMAT_DATE,
            isDateEnd: true
        }),
          new QuestionDropdown({
            key: 'tipe',
            label: 'Tipe',
            placeholder: 'Select Type',
            options: [
              {key: '', value: 'All'},
              {key: 'REJECT', value: 'REJECT'},
              {key: 'ERROR', value: 'ERROR'}
            ],
        })
    ],
    params: [
      {
        key: 'page',
        controlType: FormConstant.TYPE_HIDDEN,
        value: 1
      }
    ]
};

const ErrorReportTable: Table<ErrorReport> = {
    name: 'listErrorHistory',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'modul',
            label: 'Module',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'refNumber',
            label: 'Ref Number',
            width: 110
        },
        {
            type: ColumnType.Text,
            prop: 'namaKonsumen',
            label: 'Consumer Name',
            width: 120
        },
        {
            type: ColumnType.Text,
            prop: 'cabang',
            label: 'Branch',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'region',
            label: 'Region',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'businessLine',
            label: 'Business Line',
            width: 100
        },
        {
            type: ColumnType.Text,
            prop: 'tipe',
            label: 'Type',
            width: 80
        },
        {
            type: ColumnType.Date,
            prop: 'tanggal',
            format: 'YYYY-MM-DD HH:mm:ss',
            label: 'Date',
            width: 150
        },
        {
            type: ColumnType.Text,
            prop: 'vendorCode',
            label: 'Vendor',
            width: 60
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-eye',
                descr: 'Lihat Pesan',
                type: Act.View
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-user',
                descr: 'Activation Status',
                type: Act.ViewActStatus
              }
            ]
        }
    ]
};

export const ErrorReportListView: MsxView = {
    title: 'Error Report',
    components: [
        {
            type: WidgetType.SearchFilter,
            component: ErrorReportSearchFilter
        },
        {
            type: WidgetType.Datatable,
            component: ErrorReportTable
        }
    ]
};
