import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ManualReportRoutingModule } from "./manual-report-routing.module";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { NgSelectModule } from "@ng-select/ng-select";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { PipeModule } from "app/shared/pipes/pipe.module";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { SharedModule } from "app/shared/shared.module";
import { MsFormModule } from "app/shared/components/ms-form/ms-form.module";
import { NgxSpinnerModule } from "ngx-spinner";
import { ManualReportListComponent } from "./manual-report-list/manual-report-list.component";
import { UploadManualReportComponent } from './upload-manual-report/upload-manual-report.component';

@NgModule({
    imports: [
        CommonModule,
        ManualReportRoutingModule,
        FormsModule,
        ReactiveFormsModule,
        NgSelectModule,
        NgbModule,
        PipeModule,
        NgxDatatableModule,
        SharedModule,
        MsFormModule,
        NgxSpinnerModule
      ],
      declarations: [
        ManualReportListComponent,
        UploadManualReportComponent
      ]
})

export class ManualReportModule { }