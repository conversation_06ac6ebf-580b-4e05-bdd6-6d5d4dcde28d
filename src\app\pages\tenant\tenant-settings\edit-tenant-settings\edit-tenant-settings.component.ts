import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON>uilder, Validators } from '@angular/forms';
import { BaseResponse } from 'app/model/api/base.response';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { SaveTenantSettingsRequest } from 'app/shared/dto/tenant-setting/save-tenant-setting.request';
import * as swalFunction from '../../../../shared/data/sweet-alerts';
import { HttpClient } from '@angular/common/http';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AuditContext } from 'app/model/audit.context';
import { GlobalService } from 'app/shared/data/global.service';

@Component({
  selector: 'app-edit-tenant-settings',
  templateUrl: './edit-tenant-settings.component.html',
  styleUrls: ['./edit-tenant-settings.component.scss']
})
export class EditTenantSettingsComponent implements OnInit {

  @Input() settingType: string;
  @Input() settingTypeCode: string;
  @Input() value: string;
  @Input() tenantCode: string;

  templateForm: any;

  swal = swalFunction;

  constructor(private formBuilder: FormBuilder, public activeModal: NgbActiveModal,
    private http: HttpClient, private global: GlobalService
  ) { }

  ngOnInit(): void {
    this.templateForm = this.formBuilder.group({
      settingValue: [this.value]
    });
  }

  dismiss() {
    this.activeModal.dismiss();
    
  }

  onSubmit() {
    const request = new SaveTenantSettingsRequest();
    request.tenantCode = this.tenantCode;
    request.tenantSettingsCode = this.settingTypeCode;
    request.tenantSettingsValue = this.templateForm.controls.settingValue.value;
    request.audit = this.global.audit;

    this.http.post<BaseResponse>(URLConstant.SaveTenantSettings, request).subscribe(
      async (response) => {
        if (response.status.code!== 0) {
          this.swal.Error(response.status.message);
          console.log('Error', response.status.message);
          return;
        }
        
        this.swal.Success('Perubahan value berhasil!');
        this.activeModal.close();
      }
    );
  }
}
