<section id="feedbackPage">
    <div class="row justify-content-center">
        <div class="col-{{colSize}}">
            <div class="card align-items-center scroll">
                <div class="card-content">
                    <div class="card-body">
                        <form [formGroup]="templateForm" (ngSubmit)="onSubmit()" >
                            <div class="modal-body">
                              <div class="modal-content">
                                <img src="../../../../assets/img/photos/feedback-star.png" class="mx-auto d-block" width="150" height="auto">
                              </div>
                          
                              <div class="row justify-content-center align-items-center">
                                  <p class="font-large-1 text-center text-bold-700 px-5" translate>Rate your Experience</p>
                              </div>
                          
                              <div class="mt-4">
                                <div class="row justify-content-center align-items-center">
                                  <p class="font-medium-5 text-center text-bold-300" translate>Your rating will help us improve our service</p>
                                </div>
                              </div>
                          
                              <div class="row mt-2 justify-content-center">
                                <ngb-rating id="rating" max="5" formControlName="rating">
                                  <ng-template let-fill="fill">
                                    <span class="ft-star-on px-2" style="font-size: 3rem" [class.filled]="fill === 100" [class.default]="fill !== 100"></span>
                                  </ng-template>
                                </ngb-rating>
                              </div>
                          
                              <div class="row mt-4 mx-4 px-4 align-items-center">
                                <textarea formControlName="comment" class="form-control" name="comment" id="comment" 
                                  [placeholder]="'Tell us how was your experience?' | translate" maxlength="150" rows="5" style="resize: none;"
                                  (ngModelChange)="valueChange($event)"></textarea>
                                <span class="remaning mx-4 px-4">{{remainingText}} / 150</span>
                              </div>
                          
                              <div class="row my-4 mx-4 px-4">
                                <div class="col-12 text-center">
                                  <button *ngIf="!waiting" type="submit" class="btn btn-primary btn-block" [disabled]="enableSubmit" title="Fill stars to submit!" translate>Submit</button>
                                </div>
                                <div *ngIf="waiting" class="d-flex justify-content-center">
                                  <div class="spinner-border text-secondary" role="status">
                                    <span class="sr-only">Loading...</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                        </form>  
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
