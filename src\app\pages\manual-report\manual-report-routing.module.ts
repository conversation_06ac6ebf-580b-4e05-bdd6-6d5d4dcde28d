import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManualReportListComponent } from "./manual-report-list/manual-report-list.component";
import { UploadManualReportComponent } from "./upload-manual-report/upload-manual-report.component";

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: 'list',
        component: ManualReportListComponent,
        data: {
          title: 'Manual Report List'
        }
      },
      {
        path: 'upload-manual-report',
        component: UploadManualReportComponent,
        data: {
          title: 'Upload Manual Report'
        }
      }
    ]
  }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
  })
  export class ManualReportRoutingModule { }