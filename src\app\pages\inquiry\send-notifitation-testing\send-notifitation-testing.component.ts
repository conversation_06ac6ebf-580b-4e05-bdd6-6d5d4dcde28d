import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { GetLovRequest } from 'app/model/api/get.lov.request';
import { AuditContext } from 'app/model/audit.context';
import { GetListMessageDeliverySendNotifTest } from 'app/model/getListMessageDeliverySendNotifTest';
import { DataService } from 'app/services/api/data.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { QuestionTextbox, QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { <PERSON>x<PERSON>ie<PERSON> } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { BehaviorSubject } from 'rxjs';
import { ListInquirySendNotificationMessageDelivery } from './view/ListInquirySendNotificationMessageDelivery';
import { SendNotificationTest } from 'app/model/send-notification-test';
import { raw } from 'core-js/core/string';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-send-notifitation-testing',
  templateUrl: './send-notifitation-testing.component.html',
  styleUrls: ['./send-notifitation-testing.component.scss']
})
export class SendNotifitationTestingComponent implements OnInit {

  container: MsxView;
  buttonList: Button[];
  serviceUrl = URLConstant.GetListAuditTrail;
  notifForm: FormModel<any>;
  xForm: FormGroup;
  title: String;
  col = 6;
  ListInquirySendNotificationMessageDelivery = ListInquirySendNotificationMessageDelivery;

  selectNotifString = 'Select Notification';

  public WidgetType = WidgetType;
  public pageNumber: BehaviorSubject<number> = new BehaviorSubject(1);
  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);

  
  constructor(private dataService: DataService, private cdr: ChangeDetectorRef, private fcs: MsxFormControlService, 
    private http: HttpClient, private global: GlobalService, private toastrService: ToastrService) { 

  }

  ngOnInit(): void {
    this.initiateView();
    const controls = [...this.notifForm.params, ...this.notifForm.components];
    this.xForm = this.fcs.toFormGroup(controls);
  }

  initiateView() {
    this.title = 'Check Sending Notification';
    this.notifForm = {
      mode: CommonConstant.MODE_ADD,
      name: 'sendNotif',
      direction: FormConstant.DIRECTION_VERTICAL,
      colSize: 12,
      components: [
        new QuestionTextbox({
          key : 'phoneNo',
          label : "Phone No",
          placeholder: "Please type Phone No here",
          required : true,
          validations: [
            {type: 'required', message: 'Phone harus di isi.'}
          ],
        }),
        new QuestionDropdown({
          key: 'messageMedia',
          label: 'Message Media',
          placeholder: 'Select Message Media',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'Select Message Media' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          required: true,
          validations: [
            {type: 'required', message: 'Message Media Harus Diisi..'}
          ],
          params: {
            lovGroup: 'MESSAGE_MEDIA'
          }
        }),
        new QuestionDropdown({
          key: 'notifGateway',
          label: 'Notification Gateway',
          placeholder: this.selectNotifString,
          required: true,
          validations: [
            {type: 'required', message: 'Notification Gateway harus di isi.'}
          ],
          options: [
            { key: '', value: this.selectNotifString }
          ],
          value: '',
        }),
        new QuestionTextbox({
          key : 'message',
          label : "Message",
          required: true,
          validations: [
            {type: 'required', message: 'Message harus di isi.'}
          ],
          placeholder: "Type message here",
        }),
      ],
      params: [
        {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
        }
      ]
    };
  }

  onSelect($event) {
    console.log('select event', $event);
    switch ($event.prop) {
      case 'messageMedia':
        this.fetchGateway($event.data.key);
        break;
    }
  }

  getQuestion(key: string) {
    return this.notifForm.components.find(q => q.key === key);
  }

  getOptions(key: string) {
    const question = this.getQuestion(key);
    return question.options;
  }

  fetchGateway(messageMedia: string) {
    const request: GetLovRequest = new GetLovRequest();
    let lovGroup;

    console.log("message media", messageMedia);

    if (messageMedia !== "") {
      if (messageMedia === 'SMS') {
        lovGroup = "SMS_GATEWAY";
      } else if (messageMedia === 'WA') {
        lovGroup = "WA_GATEWAY";
      }

      request.lovGroup = lovGroup;
      this.xForm.get('notifGateway').reset();
  
  
      this.dataService.getLov(request).subscribe(res => {
        console.log('listLov Response', res);
        const question = this.notifForm.components.find(c => c.key === 'notifGateway');
        const defaultAnswer = [{ key: '', value: this.selectNotifString }];
        let options = [];
  
        for (const lov of res.lovList) {
          const option = {key: lov.code, value: lov.description};
          options.push(option);
        }
      
        options = defaultAnswer.concat(options);
        question.options = options;
        console.log("key", question.key, question.options);
  
        this.cdr.detectChanges();
  
      });
    } else {
      this.xForm.get('notifGateway').reset();
    }
  }

  getDeliveryReport() {
    const rawData = this.xForm.getRawValue();

    const request: GetListMessageDeliverySendNotifTest = new GetListMessageDeliverySendNotifTest();
    request.messageMedia = rawData.messageMedia;
    request.phoneNo = rawData.phoneNo;
    request.audit = new AuditContext();
    request.audit.callerId = this.global.user.loginId;

    this.http.post(URLConstant.listMessageDeliverySendNotif, request).subscribe(response => {
      if (response['status']['code'] === 0) {
        this.datasource.next(response);    
      }

      this.cdr.detectChanges();
    });
  }

  onNext() {
    const rawData = this.xForm.getRawValue();

    const request: SendNotificationTest = new SendNotificationTest();
    request.phoneNo = rawData.phoneNo;
    request.messageMedia = rawData.messageMedia;
    request.notifGateway = rawData.notifGateway;
    request.message = rawData.message;
    request.audit = new AuditContext();
    request.audit.callerId = this.global.user.loginId;

    this.http.post(URLConstant.sendNotificationTest, request).subscribe(resp => {
        if (resp['status']['code'] == 0) {
          this.openSuccessPopup();
        }
      })
  }

  openSuccessPopup() {
    this.toastrService.success(`Kirim Notifikasi berhasil`, null, {
      positionClass: 'toast-top-right'
    });
  }
}
