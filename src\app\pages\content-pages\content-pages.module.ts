import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';

import { ContentPagesRoutingModule } from './content-pages-routing.module';

import { ComingSoonPageComponent } from './coming-soon/coming-soon-page.component';
import { ErrorPageComponent } from './error/error-page.component';
import { ForgotPasswordPageComponent } from './forgot-password/forgot-password-page.component';
import { LockScreenPageComponent } from './lock-screen/lock-screen-page.component';
import { MaintenancePageComponent } from './maintenance/maintenance-page.component';
import { RegisterPageComponent } from './register/register-page.component';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import {HttpClient, HttpClientModule} from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import {LoginPageComponent} from './login/login-page.component';
import {NgSelectModule} from '@ng-select/ng-select';
import {ArchwizardModule} from 'angular-archwizard';
import {CustomFormsModule} from 'ngx-custom-validators';
import {MatchHeightModule} from '../../shared/directives/match-height.directive';
import {UiSwitchModule} from 'ngx-ui-switch';
import {QuillModule} from 'ngx-quill';
import {TagInputModule} from 'ngx-chips';
import { VerificationEmailComponent } from './verification-email/verification-email.component';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { MsFormModule } from 'app/shared/components/ms-form/ms-form.module';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { ChangePasswordComponent } from './change-password/change-password.component';
import { ResetPasswordVendorComponent } from './reset-password-vendor/reset-password-vendor.component';
import { TenantSettingsComponent } from './tenant-settings/tenant-settings.component';
import {NgxSpinnerModule} from 'ngx-spinner';
import {NgxPhotoEditorModule} from 'ngx-photo-editor';
import { PreventDoubleClickDirective } from 'app/shared/directives/prevent-double-click.directive';
import {LinkInvitationComponent} from './link-invitation/link-invitation.component';
import { ChangePasswordModalComponent } from './change-password/change-password-modal/change-password-modal.component';
import { MyProfileComponent } from './my-profile/my-profile.component';
import { SuccessRegisterComponent } from './success-register/success-register.component';
import { ReregisterModalComponent } from './register/reregister-modal/reregister-modal.component';
import { ActivationFromComponent } from './activation-from/activation-from.component';
import { ModalRequestOtpComponent } from './modal-request-otp/modal-request-otp.component';
import { SuccessRegisterVidaComponent } from './success-register-vida/success-register-vida.component';
import { PageDummyComponent } from './page-dummy/page-dummy.component';

export function createTranslateLoader(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  imports: [
    CommonModule,
    ContentPagesRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgbModule,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: createTranslateLoader,
        deps: [HttpClient]
      }
    }),
    CommonModule,
    ArchwizardModule,
    CustomFormsModule,
    MatchHeightModule,
    NgbModule,
    UiSwitchModule,
    QuillModule.forRoot(),
    NgSelectModule,
    TagInputModule,
    NgxDatatableModule,
    MsFormModule,
    NgxSpinnerModule,
    NgxPhotoEditorModule,
    HttpClientModule
  ],
  exports: [
    PreventDoubleClickDirective
  ],
  declarations: [
    ComingSoonPageComponent,
    ErrorPageComponent,
    ForgotPasswordPageComponent,
    LoginPageComponent,
    LockScreenPageComponent,
    MaintenancePageComponent,
    RegisterPageComponent,
    VerificationEmailComponent,
    ResetPasswordComponent,
    ChangePasswordComponent,
    ResetPasswordVendorComponent,
    TenantSettingsComponent,
    LinkInvitationComponent,
    ChangePasswordModalComponent,
    MyProfileComponent,
    SuccessRegisterComponent,
    ReregisterModalComponent,
    ActivationFromComponent,
    ModalRequestOtpComponent,
    SuccessRegisterVidaComponent,
    PreventDoubleClickDirective,
    PageDummyComponent
  ]
})
export class ContentPagesModule { }
