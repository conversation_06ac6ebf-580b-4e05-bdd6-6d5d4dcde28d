import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationModalComponent } from 'app/pages/users/inquiry-user/modal/confirmation-modal/confirmation-modal.component';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { UpdateReregistrationRequest } from 'app/shared/dto/reregistration/update-reregistration.request';
import { url } from 'inspector';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-reregister-modal',
  templateUrl: './reregister-modal.component.html',
  styleUrls: ['./reregister-modal.component.scss']
})
export class ReregisterModalComponent implements OnInit {

  @Input() invitationCode: string;
  @Input() vendorCode: string;
  @Input() tenantCode: string;
  @Input() idNo: string;
  @Input() email: string;
  @Input() phone: string;
  @Input() embedMsg: string;
  reregisForm: FormGroup;
  waiting: boolean;

  fields: any;
  validationMessages: any;
  template: any;

  constructor(private formBuilder: FormBuilder, private http: HttpClient, public activeModal: NgbActiveModal,
    private toastrService: ToastrService, public modal: NgbModal) { }

  ngOnInit(): void {
    if (this.embedMsg) {
      localStorage.clear();
    }
    this.waiting = false;
    this.initView();
    this.reregisForm.patchValue({
      email: this.email,
      phone: this.phone
    });
  }

  initView() {
    this.reregisForm = this.formBuilder.group({
      phone: ['', Validators.compose([
        Validators.required, Validators.maxLength(15),
        Validators.pattern('^08[1-9][0-9]{6,10}$')
      ])],
      email: ['', Validators.compose([
        Validators.pattern('[\\w\\-\\._]+@[\\w\\-\\._]+\\.\\w{2,10}'),
        Validators.maxLength(64)]
      )]
    });

    if (this.email === undefined || this.email === "") {
      this.reregisForm.get('email').disable();
    }
  }

  setProperties() {
    // Message Template
    this.template = {
      required: '{x} harus diisi',
      maxlength: 'Maksimal jumlah karakter {x} adalah {v}',
      minlength: 'Minimal jumlah karakter {x} adalah {v}',
      pattern: 'Silahkan isi {x} dengan format yang benar.',
    };

    this.fields = {
      phone: {
        label: 'No. Telp',
        prop: {
          maxlength: 15
        }
      },
      email: {
        label: 'Email',
        prop: {
          maxlength: 64
        }
      }
    };

    this.validationMessages = {
      phone: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.phone.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.phone.label)
            .replace('{v}', this.fields.phone.prop.maxlength)
        },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.phone.label) },
      ],
      email: [
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.email.label)
            .replace('{v}', this.fields.email.prop.maxlength)
        },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.email.label) }
      ]
    }
  }

  get rf() {
    return this.reregisForm.controls;
  }

  updateReregistration() {
    const confModal = this.modal.open(ConfirmationModalComponent);
    confModal.componentInstance.resetOtp = false;
    confModal.componentInstance.reregis = true;
    confModal.result.then((result) => {
      if (result === 'agree') {
        this.sendUpdateReregistration();
      }
    })
  }

  sendUpdateReregistration() {
    console.log(this.reregisForm.controls['email'].value);
    console.log(this.reregisForm.controls['phone'].value);
    this.activeModal.dismiss({action: 'refresh'});

    const request: UpdateReregistrationRequest = new UpdateReregistrationRequest();
    request.tenantCode = this.tenantCode;
    request.vendorCode = this.vendorCode;
    request.idNo = this.idNo;
    if (this.invitationCode) {
      request.msg = this.invitationCode;
    } else {
      request.msg = this.embedMsg;
    }
    request.email = this.reregisForm.controls['email'].value;
    request.phone = this.reregisForm.controls['phone'].value;

    let url;
    if (this.embedMsg) {
      url = URLConstant.UpdateReregistrationUserEmbed;
      request.tenantCode = null;
    } else {
      url = URLConstant.UpdateReregistrationUser;
    }

    this.http.post(url, request).subscribe(
      (response) => {
        if (response['status']['code'] === 0) {
          this.toastrService.info(`Registrasi ulang berhasil didaftarkan`, null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          });
          this.activeModal.dismiss('submit');
          window.location.reload();
        }
      })
  }

}
