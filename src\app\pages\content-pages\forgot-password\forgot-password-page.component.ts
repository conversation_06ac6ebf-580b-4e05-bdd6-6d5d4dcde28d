import { HttpClient } from '@angular/common/http';
import { Component, ViewChild } from '@angular/core';
import { FormGroup, NgForm } from '@angular/forms';
import { Router, ActivatedRoute, NavigationExtras } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'app/services/api/user.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ForgotPasswordRequest } from 'app/shared/dto/password/forgot-password.request';
import { ToastrService } from 'ngx-toastr';
import { VerificationEmailComponent } from '../verification-email/verification-email.component';

@Component({
    selector: 'app-forgot-password-page',
    templateUrl: './forgot-password-page.component.html',
    styleUrls: ['./forgot-password-page.component.scss']
})

export class ForgotPasswordPageComponent {
    @ViewChild('f') forogtPasswordForm: NgForm;

    formObj: FormModel<any>;
    xForm: FormGroup;
    currentDate = new Date();

    constructor(private router: Router,
        private route: ActivatedRoute, private global: GlobalService,
        private modalService: NgbModal, private http: HttpClient,
        private toastService: ToastrService, private userService: UserService) { }
        
    ngOnInit(): void {
        this.initView();
    }    

    initView() {
        this.formObj = {
          name: 'forgotPassword',
          colSize: 12,
          direction: FormConstant.DIRECTION_VERTICAL,
          components: [
            {
              key: 'email',
              label: 'Email / Phone Number',
              placeholder: 'Enter your email or phone number',
              controlType: FormConstant.TYPE_TEXT,
              icon: 'ft-mail',
              required: true
            }
          ],
          params: [
          ]
        }
      }

    onForm(form: FormGroup) {
        this.xForm = form;
    }

    onNext(data: any) {
      if (data['email']) {
        const request = new ForgotPasswordRequest();
        request.loginId = data['email'];
        request.audit = {
          callerId: data['email']
        }

        this.userService.checkUserActivationStatusResetPassword(data['email']).subscribe(response => {
          if (response.status.code === 0) {
            // open modal
            const modal = this.modalService.open(VerificationEmailComponent, { backdrop: 'static', keyboard:false });
            modal.componentInstance.phone = response.latestRecepientPhone;
            modal.componentInstance.email = response.latestRecipientEmail;
            modal.componentInstance.verificationType = 'resetCode';
            modal.componentInstance.defaultOtpSendingOptions = response.defaultAvailableOptionSendingPoint;
            modal.componentInstance.otpSendingOptions = response.listAvailableOptionSendingPoint;
            
            modal.result.then(response => {
              localStorage.removeItem('oldEmail');
              localStorage.removeItem('timeLeft');
              if (response) {
                // buka page reset password
                const extras: NavigationExtras = {
                  state: {
                    loginId: data['email'],
                    resetCode: response
                  }
                };
                this.router.navigate([PathConstant.RESET_PASSWORD_PAGE], extras);
              }
            }).catch((error) => {
              console.log(error);
            });
          }
        });
      } 
    }
}
