import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ViewRequestParamComponent } from './view-request-param.component';

describe('ViewRequestParamComponent', () => {
  let component: ViewRequestParamComponent;
  let fixture: ComponentFixture<ViewRequestParamComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ViewRequestParamComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ViewRequestParamComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
