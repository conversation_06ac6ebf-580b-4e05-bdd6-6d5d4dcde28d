import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {FormModel} from '../../../shared/components/ms-form/models';
import {Location} from '@angular/common';
import {FormConstant} from '../../../shared/components/ms-form/constants/form.constant';
import {ChangeProfileDataRequest} from '../../../shared/dto/change-profile/change-profile-data.request';
import {HttpClient} from '@angular/common/http';
import {URLConstant} from '../../../shared/constant/URLConstant';
import {ChangeProfileDataResponse} from '../../../shared/dto/change-profile/change-profile-data.response';
import {GlobalService} from '../../../shared/data/global.service';
import {UserProfile} from '../../../model/user-profile';
import {PathConstant} from '../../../shared/constant/PathConstant';

@Component({
  selector: 'app-change-data-input',
  templateUrl: './change-data-input.component.html',
  styleUrls: ['./change-data-input.component.scss']
})
export class ChangeDataInputComponent implements OnInit {

  isEmail = false;
  formObj: FormModel<any>;

  constructor(private router: Router, private location: Location, private http: HttpClient,
              private global: GlobalService) {
    this.isEmail = this.router.getCurrentNavigation().extras.state.isEmail;
    this.initView();
  }

  ngOnInit(): void {
  }

  initView() {
    this.formObj = {
      name: 'changeProfileData',
      colSize: 12,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        {
          key: 'emailOrPhone',
          label: this.isEmail ? 'Email' : 'No Handphone',
          placeholder: this.isEmail ? 'Enter your email' : 'Enter your phone number',
          controlType: FormConstant.TYPE_TEXT,
          maxLength: 100,
          required: true,
          validations: [
            {type: 'required', message: this.isEmail ? 'Email must be filled' : 'Phone number must be filled'}
          ]
        }
      ],
      params: [
      ]
    }
  }

  onSubmit(data: any) {
    if (data['isTrusted']) {
      return;
    }
    console.log('Data form', data);

    const request: ChangeProfileDataRequest = new ChangeProfileDataRequest();
    request.emailOrPhone = data.emailOrPhone;

    this.http.post<ChangeProfileDataResponse>(URLConstant.ChangeProfileData, request).subscribe(
      (response) => {
        if (response.status.code === 0) {
          const profile = this.global.user;

          if (this.isEmail) {
            profile.loginId = data.emailOrPhone;
          } else {
            profile.tlp = data.emailOrPhone;
          }

          this.global.user = profile;
          this.router.navigate([PathConstant.DASHBOARD]);
        }
      }
    );
  }

  onCancel(data: any) {
    this.location.back();
  }
}
