import { ErrorReportDetailComponent } from './error-report-detail/error-report-detail.component';
import { ErrorReportListView } from './error-report-list.view';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ActivatedRoute, Router } from '@angular/router';
import { URLConstant } from './../../shared/constant/URLConstant';
import { Button } from './../../shared/components/msx-view/models/Button';
import { MsxView } from './../../shared/components/msx-view/models/MsxView';
import { Component, OnInit } from '@angular/core';
import { ActStatusComponent } from './modal/act-status/act-status.component';
import { ErrorReport } from 'app/model/error-report';
import { Act } from 'app/shared/components/msx-datatable/enums/act';

@Component({
  selector: 'app-error-report',
  templateUrl: './error-report.component.html',
  styleUrls: ['./error-report.component.scss']
})
export class ErrorReportComponent implements OnInit {

  view: MsxView;
  buttonList: Button[];
  routeData: any;
  serviceUrl = URLConstant.ListErrorReport;

  constructor(private activatedRoute: ActivatedRoute, private router: Router, private modalService: NgbModal) {
    this.routeData = this.activatedRoute.snapshot.data;
  }

  ngOnInit(): void {
    this.view = ErrorReportListView;
  }

  onItemClickListener(result: any) {
    const data = result['data'];

    switch (result['act']['type']) {
      case Act.View:
        return this.openErrorMessageModal(data);
      
      case Act.ViewActStatus:
        return this.openActStatusListModal(data);

    }
  }

  openActStatusListModal(data: ErrorReport) {
    const modal = this.modalService.open(ActStatusComponent, { size: 'lg' });
    modal.componentInstance.idErrorHistory = data.idErrorHistory;
  }

  openErrorMessageModal(data) {
    const modal = this.modalService.open(ErrorReportDetailComponent, { backdrop: 'static', keyboard: false, size: 'l' });
        modal.componentInstance.errorMessage = data['errorMessage'];
  }
}
