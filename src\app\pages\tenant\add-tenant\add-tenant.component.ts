import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { GetLovRequest } from 'app/model/api/get.lov.request';
import { Tenant } from 'app/model/tenant';
import { TenantService } from 'app/services/api/tenant.service';
import { FormModel } from 'app/shared/components/ms-form/models';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ThresholdBalance } from 'app/shared/dto/tenant-setting/threshold-balance';
import { AddTenantRequest } from 'app/shared/dto/tenant/add-tenant.request';
import { EditTenantRequest } from 'app/shared/dto/tenant/edit-tenant.request';
import { TenantDetailRequest } from 'app/shared/dto/tenant/tenant-detail.request';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject } from 'rxjs';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { Code } from 'app/model/code';
import { GetLovResponse } from 'app/model/api/get.lov.response';

@Component({
  selector: 'app-add-tenant',
  templateUrl: './add-tenant.component.html',
  styleUrls: ['./add-tenant.component.scss']
})
export class AddTenantComponent implements OnInit {

  addEditTenantForm: FormGroup;
  mForm: FormModel<any>;
  mode = 'Add';
  emailList = [];
  balances = [];
  balanceShow = [];
  tenantSetting = [];
  tenantSettingResult = [];
  dropdownOptions: Record<string, any[]> = {};
  balanceValue = new Map<string,any>();
  swal = swalFunction;
  state: any;
  routeData: any;
  validationMessages: any;
  template: any;
  result: any;
  processedSettings = [];
  tcReadOnly: boolean;

  dataSubj: BehaviorSubject<Tenant> = new BehaviorSubject<Tenant>(null);

  TypeMapping: Record<string, { type: string; description: string; args: string }> = {
    aesEncryptKey: {
      type: "string",
      description: "AES Encrypt Key",
      args: ""
    },
    livenessKey: {
      type: "string",
      description: "Liveness Key",
      args: ""
    },
    meteraiStampingResultUrl:{
      type: "string",
      description: "Materai Stamping Result Url",
      args: ""
    },
    activationCallbackUrl: {
      type: "string",
      description: "Activation Callback Url",
      args: ""
    },
    emailService:{
      type: "boolean",
      description: "Email Service",
      args: ""
    },
    invitationLinkActiveDuration:{ 
      type: "number",
      description: "Invitation Link Active Duration",
      args: ""
    },
    sendCertNotifBySms: {
      type: "boolean",
      description: "Send Certif Notif by SMS",
      args: ""
    },
    useStandardUrl: {
      type: "boolean",
      description: "Use Standard Url",
      args: ""
    },
    livenessFacecompareServices: {
      type: "boolean",
      description: "Liveness Face Compare Service",
      args: ""
    },
    splitLivenessFacecompareBill: {
      type: "boolean",
      description: "Split Liveness Face Compare Bill",
      args: ""
    },
    useLivenessFacecompareFirst: {
      type: "boolean",
      description: "Use Liveness Face Compare First",
      args: ""
    },
    vendorStamping: {
      type: "dropdown",
      description: "Vendor Stamping",
      args: "LOV_VENDOR_STAMPING"
    },
    automaticStampingAfterSign: {
      type: "boolean",
      description: "Automatic Stamping After Sign",
      args: ""
    },
    signRequestNotification: {
      type: "boolean",
      description: "Sign Request Notification",
      args: ""
    },
    needOtpForSigning: {
      type: "boolean",
      description: "Need Otp For Signing",
      args: ""
    },
    otpActiveDuration: {
      type: "number", 
      description: "OTP Active Duration",
      args: ""
    },
    useCustomSignImage: {
      type: "boolean",
      description: "Use Custom Sign Image",
      args: ""
    },
    smsGateway: {
      type: "dropdown",
      description: "SMS Gateway",
      args: "SMS_GATEWAY"
    },
    defaultOtpSendingOptions: {
      type:  "dropdown",
      description: "Default OTP Sending Point",
      args: "DEFAULT_SENDING_OPTION"
    },
    sendOtpByEmail: {
      type: "boolean",
      description: "Send OTP by Email",
      args: ""
    },
  };

  constructor(private formBuilder: FormBuilder, private tenantService: TenantService, private global: GlobalService,
    private activatedRoute: ActivatedRoute, private router: Router, private http: HttpClient, private cdr: ChangeDetectorRef,
    private location: Location, private toastrService: ToastrService) { 
      this.routeData = this.activatedRoute.snapshot.data;
      this.mode = this.routeData['mode'];
      this.tcReadOnly = this.mode == CommonConstant.MODE_EDIT;

      this.activatedRoute.queryParams.subscribe(() => {
        this.state = this.router.getCurrentNavigation().extras.state;
        this.dataSubj.next(this.state);
        console.log('state', this.state);
      })

      this.getBalances();
      // this.getStampingVendorLov();
    }

  ngOnInit(): void {
    console.log('route data', this.routeData);
    this.setProperties();
    this.setupForm();
    this.setForm();
  }

  setupForm() {
    if (this.mode == CommonConstant.MODE_ADD) {
      this.addEditTenantForm = this.formBuilder.group({
        tenantName: ['', Validators.required],
        tenantCode: ['', Validators.required],
        apiKey: ['', Validators.required],
        refNumberLabel: ['', Validators.required],
        balanceThreshold: [{}],
        balanceReminderReceiver: [''],
        emailUserAdmin: ['', Validators.required],
        passwordUserAdmin: ['', Validators.required]
      });
    } else {
      this.addEditTenantForm = this.formBuilder.group({
        tenantName: ['', Validators.required],
        tenantCode: ['', Validators.required],
        apiKey: ['', Validators.required],
        refNumberLabel: ['', Validators.required],
        balanceThreshold: [{}],
        balanceReminderReceiver: [''],
        emailUserAdmin: [''],
        passwordUserAdmin: ['']
      });
    }

    this.balances.forEach(
      (data) => {
        this.balanceShow[data.code] = false;
      }
    );
  }

  setProperties() {
    this.template = {
      required: '{x} harus diisi'
    }

    this.validationMessages = {
      tenantName: {type: 'required', message: this.template.required.replace('{x}', 'Tenant Name')},
      tenantCode: {type: 'required', message: this.template.required.replace('{x}', 'Tenant Code')},
      refNumberLabel: {type: 'required', message: this.template.required.replace('{x}', 'Ref Number Label')},
      apiKey: {type: 'required', message: this.template.required.replace('{x}', 'API Key')},
      doc: {type: 'required', message: this.template.required.replace('{x}', 'Document')},
      fvrf: {type: 'required', message: this.template.required.replace('{x}', 'Face Verification')},
      otp: {type: 'required', message: this.template.required.replace('{x}', 'OTP')},
      sdt: {type: 'required', message: this.template.required.replace('{x}', 'Stamp Duty')},
      sgn: {type: 'required', message: this.template.required.replace('{x}', 'Sign')},
      sms: {type: 'required', message: this.template.required.replace('{x}', 'SMS Notif')},
      vrf: {type: 'required', message: this.template.required.replace('{x}', 'Verification')},
      balanceReminderReceiver: {},
      emailUserAdmin: {type: 'required', message: this.template.required.replace('{x}', 'Email User Admin')},
      passwordUserAdmin: {type: 'required', message: this.template.required.replace('{x}', 'Kode Akses User Admin')}
    }
  }

  setForm() {
    const request = new TenantDetailRequest();
    request.tenantCode = this.state.tenantCode;
    this.tenantService.tenantDetail(request).subscribe(
      (response) => {
        if (response.status.code === 0) {
          for (let i = 0; i < response.emailReminderDest.length; i++) {
            this.emailList = [...this.emailList, {address: response.emailReminderDest[i]}];
          }
          
          this.addEditTenantForm.patchValue({
            tenantCode: response.tenantCode,
            tenantName: response.tenantName,
            apiKey: response.apiKey,
            refNumberLabel: response.refNumberLabel,
          });

          this.dynamicThresholdBalance(response.thresholdBalance);

          this.tenantSetting = this.createCodeObjects(this.TypeMapping, response);

          console.log("objectnya", this.tenantSetting);
          this.cdr.detectChanges();
        }
      }
    );
  }

  collectFormValues() {
    const result = this.tenantSetting
      .filter(setting => setting.exist || setting.type === "number")
      .map(setting => {
        return {
          code: setting.code,
          value: setting.value,
          type: setting.type
        };
      });

    console.log(result);
    return result;
  }

  createCodeObjects = (
    codeMetaMapping: Record<
      string,
      { type: string; description: string; args: string }
    >,
    apiResponse: any
  ): Code[] => {
    Object.keys(codeMetaMapping).forEach((code) => {
      const meta = codeMetaMapping[code];
      if (meta.type === "dropdown" && meta.args) {
        this.fetchDropdownOptions(meta.args, code);
      }
    });
  
    return Object.keys(codeMetaMapping).map((code) => {
      const meta = codeMetaMapping[code];
      const value = apiResponse[code] || null;
      console.log("hasil", code, apiResponse[code]);
      return {
        code,
        type: meta.type,
        description: meta.description,
        value,
        exist: meta.type === "number" ? value > 0 : apiResponse.hasOwnProperty(code),
      };
    });
  };

  // createCodeObjects = (
  //   codeMetaMapping: Record<
  //     string,
  //     { type: string; description: string; args: string }
  //   >,
  //   apiResponse: any
  // ): Code[] => {
  //   Object.keys(codeMetaMapping).forEach((code) => {
  //     const meta = codeMetaMapping[code];
  //     if (meta.type === "dropdown" && meta.args) {
  //       this.fetchDropdownOptions(meta.args, code);
  //     }
  //   });

  //   return Object.keys(codeMetaMapping).map((code) => ({
  //     code,
  //     type: codeMetaMapping[code].type,
  //     description: codeMetaMapping[code].description,
  //     value: apiResponse[code] || null,
  //     exist: apiResponse.hasOwnProperty(code),
  //   }));
  // };
  

fetchDropdownOptions(lovGroup: string, code: string) {
  const request = new GetLovRequest();
  request.lovGroup = lovGroup;

  this.http.post<GetLovResponse>(URLConstant.GetLov, request).subscribe(
    (response) => {
      this.dropdownOptions[code] = response.lovList;
    },
    (error) => {
      console.error(`Error fetching dropdown options for ${code}:`, error);
      this.dropdownOptions[code] = [];
    }
  );
}

  
  dynamicThresholdBalance(thresholdBalance) {
    console.log("bbbbbbbbbbb ", thresholdBalance);
    this.balances.forEach(
      (data) => {
        console.log("dataaaaa" + data.code);
        if (thresholdBalance[data.code] != undefined) {
          this.balanceShow[data.code] = true;
        }
      }
    );

    this.balanceValue = thresholdBalance;
  } 

  generateApiKey() {
    var result           = '';
    var characters       = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for ( var i = 0; i < 6; i++ ) {
      result += characters.charAt(Math.floor(Math.random() * 
        charactersLength));
    }
   this.addEditTenantForm.patchValue({
     apiKey: result
   });
  }

  generateAESKey() {
    var result           = '';
    var characters       = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for ( var i = 0; i < 16; i++ ) {
      result += characters.charAt(Math.floor(Math.random() * 
        charactersLength));
    }
    const index = this.tenantSetting.findIndex(item => item.code === 'aesEncryptKey');

    if (index !== -1) {
      this.tenantSetting[index].value = result;
    }
  }

  goBack() {
    this.location.back();
  }

  addEmail() {
    this.emailList = [...this.emailList, {address: ''}];
  }

  deleteEmail(index: number) {
    this.emailList.splice(index, 1);
  }

  toggleSwitch(setting: any) {
    setting.value = setting.value === '1' ? '0' : '1';
  }

  processEmailList() {
    const emails = [];
    this.emailList.forEach(email => {
      if (email.address !== '') emails.push(email.address);
    });
    return emails;
  }

  addEditTenant() {
    this.tenantSettingResult = this.collectFormValues();

    const formData = this.addEditTenantForm.getRawValue();

    if (this.mode === CommonConstant.MODE_ADD) {
      const request = new AddTenantRequest();
      request.audit = {callerId: this.global.user.loginId};
      request.tenantCode = formData.tenantCode;
      localStorage.setItem('tenantCode', formData.tenantCode);
      request.tenantName = formData.tenantName;
      request.apiKey = formData.apiKey;
      request.emailReminderDest = this.processEmailList();
      request.thresholdBalance = this.proccessBalanceThreshold();
      request.refNumberLabel = formData.refNumberLabel;
      request.emailUserAdmin = formData.emailUserAdmin;
      request.passwordUserAdmin = formData.passwordUserAdmin;

      this.http.post(URLConstant.AddTenant, request).subscribe(
        (response) => {
          if (response["status"]["code"] === 0) {
            this.swal.Success('Tenant has been successfully added');
            this.goBack();
          } else {
            return;
          }
        }
      );
    } else {
      const request = new EditTenantRequest();
      request.audit = {callerId: this.global.user.loginId};
      request.tenantCode = formData.tenantCode;
      request.tenantName = formData.tenantName;
      request.apiKey = formData.apiKey;
      request.emailReminderDest = this.processEmailList();
      request.thresholdBalance = this.proccessBalanceThreshold();
      request.refNumberLabel = formData.refNumberLabel;
      this.tenantSettingResult.forEach(setting => {
          console.log("hasil1", setting.code, setting.value);
          console.log("hasil2", setting.code);

          (request as any)[setting.code] = setting.type === 'number' && setting.value === null ? 0 : setting.value;

          // this.processedSettings.push({
          //   code: setting.code,
          //   value: setting.type === 'number' && setting.value === null ? 0 : setting.value,
          // });
      });

      // console.log(this.processedSettings)

      // this.processedSettings.forEach(setting => {
      //   (request as any)[setting.code] = setting.value;
      // });
      
      this.http.post(URLConstant.EditTenant, request).subscribe(
        (response) => {
          if (response["status"]["code"] === 0) {
            this.swal.Success('Tenant has been successfully changed');
            this.goBack();
          } else {
            return;
          }
        }
      );
    }
  }

  getBalances() {
    const request = new GetLovRequest();
    request.lovGroup = 'BALANCE_TYPE';
    this.http.post(URLConstant.GetLov, request).subscribe(
      (response) => {
        if (response['status']['code'] === 0) {
          this.balances = response['lovList'];
        } else {
          this.toastrService.error(response['status']['message']);
        }
      }
    )
  }

  proccessBalanceThreshold() {
    console.log("balVal", this.balanceValue);

    this.balances.forEach(
      (data) => {
        if ((<HTMLInputElement> document.getElementById(data.code)) != undefined) {
          this.balanceValue[data.code] = parseInt((<HTMLInputElement> document.getElementById(data.code)).value);
        }
      }
    );

    console.log(this.balanceValue);

    return this.balanceValue;
  }

  addBalanceThreshold(code: string) {
    this.balanceShow[code] = true;
    this.balanceValue[code] = 0;
  }

  deleteBalanceThreshold(code: string) {
    this.balanceShow[code] = false;
    delete this.balanceValue[code];
  }

  addTenantSetting(code: string) {
    const index = this.tenantSetting.findIndex(item => item.code === code);

    if (index !== -1) {
      this.tenantSetting[index].exist = true;
      if (this.tenantSetting[index].type === "number") {
        this.tenantSetting[index].value = 0;
      }
    }  
  }

  deleteTenantSetting(code: string) {
    const index = this.tenantSetting.findIndex(item => item.code === code);

    if (index !== -1) {
      if (this.tenantSetting[index].type === "number") {
        this.tenantSetting[index].value = 0;
      } 
      this.tenantSetting[index].exist = false;
    }  
  }
}
