import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { DeviceDetectorService } from 'ngx-device-detector';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from '../../shared/data/sweet-alerts';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-feedback-menu',
  templateUrl: './feedback-menu.component.html',
  styleUrls: ['./feedback-menu.component.scss']
})
export class FeedbackMenuComponent implements OnInit {
  @Input() loginId : string = this.global.user.loginId;
  templateForm: FormGroup;
  tmpFile: string;
  waiting: boolean;
  enableSubmit: boolean;
  remainingText: number = 0;
  isMobile = false;
  colSize = 5;

  swal = swalFunction;

  constructor(private formBuilder: FormBuilder, private toastrService: ToastrService,
    private http: HttpClient, private router: Router, private global: GlobalService,
    private deviceService: DeviceDetectorService) {
      if (deviceService.isMobile()) {
        this.isMobile = true;
      }
    }

  ngOnInit(): void {
    if (this.isMobile) {
      this.colSize = 12;
    }

    this.waiting = false;
    this.enableSubmit = true;
    this.templateForm = this.formBuilder.group({
      rating: [''],
      comment: ['', Validators.maxLength(200)]
    });

    this.onChanges();
  }

  onChanges(): void {
    this.templateForm.valueChanges.subscribe(value => {
      if (value.rating !== '') {
        this.enableSubmit = false;
      }
    })
  }

  cancel() {
    this.router.navigate(['dashboard']);
  }

  onSubmit() {
    console.log(this.templateForm.controls['rating'].value);
    console.log(this.templateForm.controls['comment'].value);

    const request = {
      audit: {
        callerId: this.loginId
      },
      comment: this.templateForm.controls['comment'].value,
      loginId : this.loginId,
      feedbackValue : this.templateForm.controls['rating'].value
    };
    this.http.post(URLConstant.SendFeedback, request).subscribe(
      (response) => {
        if (response["status"]["code"] != 0) {
          this.swal.Error(response["status"]["message"]);
        } else {
          this.toastrService.info(`Terimakasih telah memberikan feedback kepada kami`, null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          });
          this.router.navigate(['dashboard']);
        }
      }
    );
  }

  //Testing Howen
  valueChange(text){
    this.remainingText = text.length;
  }

}
