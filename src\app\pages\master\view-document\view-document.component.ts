import {AfterViewInit, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {Location} from '@angular/common';
import {ActivatedRoute, Router} from '@angular/router';
import {Signer} from '../../../shared/components/document-anotate/model/signer';
import {FormModel} from '../../../shared/components/ms-form/models';
import {FormConstant} from '../../../shared/components/ms-form/constants/form.constant';
import { DeviceDetectorService } from 'ngx-device-detector';
import { CountlyService } from 'app/services/api/countly.service';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-view-document',
  templateUrl: './view-document.component.html',
  styleUrls: ['./view-document.component.scss']
})
export class ViewDocumentComponent implements OnInit, AfterViewInit {

  public state: any;
  public mode: string;
  public signer: Signer[];
  mForm: FormModel<any>;
  col = 6;

  constructor(private location: Location, private router: Router, private activeRoute: ActivatedRoute,
    private deviceService: DeviceDetectorService,private countlyService: CountlyService
    ) {
    if (deviceService.isMobile()) {
      this.col = 12;
    }
    this.activeRoute.queryParams.subscribe(params => {
      this.mode   = params['mode'];
      this.state  = this.router.getCurrentNavigation().extras.state;
      this.signer = this.state['signer'];
    });
  }

  ngOnInit(): void {
    this.countlyService.initiate();
    this.initForm();
  }

  doBack() {
    this.location.back();
  }

  ngAfterViewInit(): void {
  }

  initForm() {
    this.mForm = {
      name: 'settingDocumentTemplate',
      colSize: this.col,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        {
          key: 'documentTemplateCode',
          label: 'Document Template Code',
          placeholder: 'Document template code',
          controlType: FormConstant.TYPE_TEXT,
          value: this.state['documentTemplateCode'],
          readonly: true
        },
        {
          key: 'documentTemplateName',
          label: 'Document Template Name',
          placeholder: CommonConstant.DOCUMENT_TEMPLATE_NAME,
          controlType: FormConstant.TYPE_TEXT,
          value: this.state['documentTemplateName'],
          readonly: true
        },
        {
          key: 'documentTemplateDescription',
          label: 'Description',
          placeholder: 'Description',
          controlType: FormConstant.TYPE_TEXT,
          value: this.state['documentTemplateDescription'],
          readonly: true
        },
        {
          key: 'isActive',
          label: 'Status',
          controlType: FormConstant.TYPE_TEXT,
          readonly: true,
          value: this.state['isActive'] === '1' ? 'Active' : 'Inactive'
        }
      ],
      params: []
    }
  }

}
