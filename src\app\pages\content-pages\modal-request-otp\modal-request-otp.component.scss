.letterotp {
    display: block;
    margin: auto;
    border: none;
    padding: 0;
    width: 9ch;
    background: repeating-linear-gradient(90deg, dimgrey 0, dimgrey 1ch, transparent 0, transparent 1.5ch) 0 100%/ 8.5ch 2px no-repeat;
    font: 5ch droid sans mono, consolas, monospace;
    letter-spacing: 0.5ch;
  }
.letterotp:focus {
    outline: none;
    color: dodgerblue;
}


.form-phone{
  margin-top: 15px;
  width: 300px;
  height: auto;
  background: #FFFFFF;
  border: 1px solid #E0E0E0;

    display: flex;
    flex-direction: row;
    align-items: center;
}

.button-process{
  text-align: end;
}

.btn-proccess{
  background-color: #338939;
  color: #ffff;
}

.timer {
    color: #ffff;
    height: auto;
    padding-top: 7px;
    padding-bottom: 7px;
    padding-left: 10px;
    padding-right: 10px;
    width:55px;
    background-color: #78b3ef;
    border: 1px solid #84b8ed;
    border-radius: 3px;
    text-align: center;
}

.reset-button{
  color: #ffff;
    height: auto;
    padding-top: 7px;
    padding-bottom: 7px;
    padding-left: 5px;
    padding-right: 5px;
    width: 100px;
    background-color: #78b3ef;
    border-radius: 3px;
}