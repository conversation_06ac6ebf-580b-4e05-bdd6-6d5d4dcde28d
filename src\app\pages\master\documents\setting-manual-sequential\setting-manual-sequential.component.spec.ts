import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SettingManualSequentialComponent } from './setting-manual-sequential.component';

describe('SettingManualSequentialComponent', () => {
  let component: SettingManualSequentialComponent;
  let fixture: ComponentFixture<SettingManualSequentialComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SettingManualSequentialComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SettingManualSequentialComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
