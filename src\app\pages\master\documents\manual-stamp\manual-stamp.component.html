<!-- <div class="row" style="margin-top: 15px">
    <div class="col-6">
      <div class="content-header" style="margin-top: 0 !important;">{{'Stamping e-Meterai'}}</div>
    </div>
  </div>
  
  <app-form [formObj]="mForm" (change)="onInput($event)"></app-form> -->
  

  <div class="row" style="margin-top: 15px">
    <div class="col-6">
      <div class="content-header" style="margin-top: 0 !important;" translate>Stamping e-Meterai</div>
    </div>
  </div>

  <div class="row match-height">
    <div class="col-6">
        <div class="card">
            <div class="card-content">
                <div class="card-body">
                    <form [formGroup]="vForm" (ngSubmit)="onNext" id="vForm" (form)="onForm($event)" >
                        <ng-container *ngIf="formObj?.params && formObj.params?.length > 0">
                            <input *ngFor="let param of formObj.params" type="hidden" [formControlName]="param.key"
                                   [value]="param.value" [id]="param.key" />
                          </ng-container>

                          <div class="row">
                            <div class="col-{{formObj.colSize}}">
                              <app-text [form]="vForm" [question]="getQuestion('documentNo')" [direction]="formObj.direction"></app-text>
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-{{formObj.colSize}}">
                              <app-text [form]="vForm" [question]="getQuestion('documentName')" [direction]="formObj.direction"></app-text>
                            </div>
                          </div>

                          <div class="row">
                            <div class="col-{{formObj.colSize}}">
                              <app-date [form]="vForm" [question]="getQuestion('documentDate')" [direction]="formObj.direction"></app-date>
                            </div>
                          </div>
                          <div class="row" >
                            <div class="col-{{formObj.colSize}}">
                              <app-select [form]="vForm" [question]="getQuestion('documentType')"
                                          [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
                            </div>
                          </div>
                          <div class="row" >
                            <div class="col-{{formObj.colSize}}">
                              <app-select [form]="vForm" [question]="getQuestion('documentTypePeruri')"
                                          [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
                            </div>
                          </div>

                          <div class="row">
                            <div class="col-{{formObj.colSize}}">
                              <app-file [form]="vForm" [direction]="formObj.direction" [question]="getQuestion('documentExample')" (change)="onInput($event)" (rawFile)="onBase64Data($event)"></app-file>
                            </div>
                          </div>

                          <div class="row">
                            <div class="col-12 text-center">
                              <button class="btn btn-light mr-2" type="button" (click)="onCancel()">{{'Cancel' | translate}}</button>
                              <button class="btn btn-info" type="submit" [disabled]="vForm.invalid" (click)="onNext($event)" >Next</button>
                            </div>
                          </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
  </div>