import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {MsxFormControlService} from '../../../../shared/components/ms-form/msx-form-control.service';
import {FormModel} from '../../../../shared/components/ms-form/models';
import {CommonConstant} from '../../../../shared/constant/common.constant';
import {FormConstant} from '../../../../shared/components/ms-form/constants/form.constant';
import {QuestionDate, QuestionDropdown, QuestionTextbox} from '../../../../shared/components/ms-form/questions';
import {URLConstant} from '../../../../shared/constant/URLConstant';
import {QuestionFile} from '../../../../shared/components/ms-form/questions/question-file';
import {FormGroup, Validators} from '@angular/forms';
import {AddSignerComponent} from '../modal/add-signer/add-signer.component';
import {Signer} from '../../../../shared/components/document-anotate/model/signer';
import {DataService} from '../../../../services/api/data.service';
import {DataRequest} from '../../../../model/api/data.request';
import {ToastrService} from 'ngx-toastr';
import {BaseRequest} from '../../../../model/api/base.request';
import {PathConstant} from '../../../../shared/constant/PathConstant';
import {QuestionBase} from '../../../../shared/components/ms-form/questions/question-base';
import {GetLovRequest} from '../../../../model/api/get.lov.request';
import {UserService} from '../../../../services/api/user.service';
import {AutofillUserRequest} from '../../../../model/api/autofill.user.request';
import { DeviceDetectorService } from 'ngx-device-detector';
import { GlobalService } from 'app/shared/data/global.service';
import { Vendor } from 'app/shared/constant/vendor';

@Component({
  selector: 'app-manual-upload',
  templateUrl: './manual-upload.component.html',
  styleUrls: ['./manual-upload.component.scss']
})
export class ManualUploadComponent implements OnInit {

  refs: any[];
  formObj: FormModel<any>;
  msxForm: FormGroup;
  signers: Signer[];
  isAutoStamping: string;
  hasEmeterei: boolean;
  rawFile: string;
  maxFileSize: any;
  paymentTypeList: {key: string; value: string}[] = [];
  officeList: {key: string; value: string}[] = [];
  useSignQrOptionList: {key: string; value: string}[] = [];
  
  isMobile = false;
  colsize = 6;
  pilihCabang = 'Pilih Cabang';
  showPaymentTypeDropdown = true;
  psreCode: string;

  constructor(private router: Router, private ngModal: NgbModal, private fcs: MsxFormControlService,
              private cdr: ChangeDetectorRef, private dataService: DataService, private userService: UserService,
              private toastrService: ToastrService, private deviceService: DeviceDetectorService, private global: GlobalService) {
    this.hasEmeterei = false;
    if (deviceService.isMobile()) {
      this.isMobile = true;
      this.colsize = 10;
    }
  }

  ngOnInit(): void {
    this.refs = [];
    this.setupForm();
    this.isAutoStamping  = '';
    const controls = [...this.formObj.params, ...this.formObj.components];
    this.msxForm = this.fcs.toFormGroup(controls);
    this.msxForm.get('isMeterei')?.setValue(false);
    this.signers = [];
    this.cdr.detectChanges();
    this.getAutomaticStampingStatus();
    this.getMaxFileSize();
  }

  setupForm() {
    const nowday = new Date();
    this.formObj = {
      mode: CommonConstant.MODE_ADD,
      name: 'manualUploadSign',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionDropdown({
          key: 'psreCode',
          label: 'PSrE',
          placeholder: 'Select PSrE',
          serviceUrl: URLConstant.GetVendorListV2,
          options: [
            { key: '', value: 'Pilih PSrE' }
          ],
          value: '',
          args: {
            list: 'vendorList',
            key: 'code',
            value: 'name'
          },
          params: {
            vendorTypeCode: CommonConstant.VENDOR_TYPE_PSRE
          },
          required: true,
        }),
        new QuestionTextbox(
          {
            key: 'referenceNo',
            label: CommonConstant.LABEL_DOCUMENT_NUMBER,
            placeholder: 'Type document number here',
            maxLength: 100,
            required: true,
            validations: [
              {type: 'required', message: 'Document Number harus di isi.'},
              {type: 'maxlength', message: 'Maksimal jumlah karakter Document Number adalah 100'}
            ]
          }),
        new QuestionTextbox(
          {
            key: 'documentName',
            label: 'Document Name',
            placeholder: 'Type document name here',
            maxLength: 100,
            required: true,
            validations: [
              {type: 'required', message: 'Document Name harus di isi.'},
              {type: 'maxlength', message: 'Maksimal jumlah karakter Document Name adalah 100'}
            ]
          }),
        new QuestionDate({
          key: 'documentDate',
          label: 'Document Date',
          placeholder: CommonConstant.FORMAT_DATE,
          width: 140,
          isDateStart: true,
          required: true,
          value: {year: nowday.getFullYear(), month: nowday.getMonth() + 1, day: nowday.getDate()},
          validations: [
            {type: 'required', message: 'Document Date harus di isi.'}
          ]
        }),
        new QuestionBase({
          key: 'isMeterei',
          label: 'Apakah dokumen membutuhkan e-Meterai?',
          value: false
        }),
        new QuestionDropdown({
          key: 'documentTypePeruri',
          label: 'Tipe Dokumen Peruri',
          placeholder: 'Select Document Type Peruri',
          serviceUrl: URLConstant.ListDocTypePeruri,
          options: [
            { key: '', value: 'Pilih Tipe Dokumen Peruri' }
          ],
          value: '',
          args: {
            list: 'documentEMateraiList',
            key: 'peruriDocId',
            value: 'documentName'
          },
          required: false,
        }),
        new QuestionDropdown({
          key: 'isAutomaticStamp',
          label: 'Stamp Meterai Otomatis',
          placeholder: 'Select Stamp Meterai Otomatis',
          options: [
            { key: '1', value: 'Ya' },
            { key: '0', value: 'Tidak' }
          ],
          value: this.isAutoStamping || '0',
          required: false,
        }),
        new QuestionDropdown({
          key: 'paymentType',
          label: 'Payment Type',
          placeholder: 'Select Payment Type',
          options: [
            { key: '', value: 'Pilih Jenis Pembayaran' }
          ],
          value: '',
          required: true,
        }),
        new QuestionFile({
          key: 'documentExampleVIDA',
          label: 'Document. Max document file size 10 MB',
          placeholder: CommonConstant.LABEL_CHOOSE_FILE,
          accept: CommonConstant.DATA_ACCEPT_APPLICATION_PDF,
          required: true
        }),
        new QuestionFile({
          key: 'documentExamplePRIVY',
          label: 'Document. Max document file size 5 MB',
          placeholder: CommonConstant.LABEL_CHOOSE_FILE,
          accept: CommonConstant.DATA_ACCEPT_APPLICATION_PDF,
          required: true
        }),
        new QuestionFile({
          key: 'documentExample',
          label: 'Document. Max document file size 10 MB',
          placeholder: CommonConstant.LABEL_CHOOSE_FILE,
          accept: CommonConstant.DATA_ACCEPT_APPLICATION_PDF,
          required: true
        }),
        new QuestionDropdown({
          key: 'isSequence',
          label: 'Sequential Signing',
          placeholder: 'Select Sequential Sign',
          options: [
            { key: '1', value: 'Ya' },
            { key: '0', value: 'Tidak' }
          ],
          value: '0',
          required: true
        }),
        new QuestionDropdown({
          key: 'useSignQr',
          label: 'Use QR for Sign Tracking',
          placeholder: 'Use QR for Sign Tracking',
          options: [
            { key: '1', value: 'Ya' },
            { key: '0', value: 'Tidak' }
          ],
          value: '0',
          required: true
        }),
        new QuestionDropdown({
          key: 'businessLineCode',
          label: 'Business Line',
          placeholder: 'Select Business Line',
          serviceUrl: URLConstant.BusinessLineList,
          options: [
            { key: '', value: 'Pilih Lini Bisnis' }
          ],
          value: '',
          args: {
            list: 'businessLineList',
            key: 'businessLineCode',
            value: 'businessLineName'
          },
          params: {
            tenantCode: this.global.user.role.tenantCode
          },
        }),
        new QuestionDropdown({
          key: 'regionCode',
          label: 'Region',
          placeholder: 'Select Region',
          serviceUrl: URLConstant.RegionList,
          options: [
            { key: '', value: 'Pilih Wilayah' }
          ],
          value: '',
          args: {
            list: 'regionList',
            key: 'regionCode',
            value: 'regionName'
          },
          params: {
            tenantCode: this.global.user.role.tenantCode
          },
        }),
        new QuestionDropdown({
          key: 'officeCode',
          label: 'Office',
          placeholder: 'Select Office',
          options: [
            { key: '', value: this.pilihCabang }
          ],
          value: ''
        })
      ],
      params: [
      ]
    }
  }

  onSelect($event) {
    console.log('select', $event);

    switch ($event.prop) {
        case 'psreCode':
            this.getPaymentTypeList('psreCode');
            this.changeSignQrOptions($event.data.key);
            this.validateSigner();
            break;

        case 'regionCode':
            this.getOfficeList($event.data.key);
            break;
    }

    console.log('event data', $event['data']);

    if (this.refs.indexOf($event['data']) === -1) {
        this.refs.push($event['data']);
    }

    console.log('refs', this.refs);
  }

  onInputFile($event) {
    //
  }

  openPrompt() {
    //
  }

  onChange($event) {
    this.hasEmeterei = $event.target.checked;
    console.log('$event', $event.target.checked);

    if (this.hasEmeterei) {
      const tmp = this.formObj.components.find(x => x.key === 'isAutomaticStamp');
      tmp.required = true;

      this.msxForm.get('isAutomaticStamp').setValidators([Validators.required]);
      this.msxForm.updateValueAndValidity();
    } else {
      const tmp = this.formObj.components.find(x => x.key === 'isAutomaticStamp');
      tmp.required = false;

      this.msxForm.get('isAutomaticStamp').setValidators([]);
      this.msxForm.updateValueAndValidity();
    }
  }

  onInput(event) {
    console.log('input', event);
    if (event.target && event.target['files']) {
      const files = event.target.files;
      if (files[0].type !== CommonConstant.DATA_ACCEPT_APPLICATION_PDF) {
        this.msxForm.get('documentExample').reset();
        this.toastrService.warning('Silahkan pilih dokumen dengan format pdf!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }


      const psreCode = this.msxForm.get('psreCode').value;

      console.log('psrecode' + psreCode);


      if (!psreCode) {
        this.msxForm.get('documentExample').reset();
        this.toastrService.warning(`Mohon pilih PSrE terlebih dahulu`, null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });      
      } else if (psreCode === CommonConstant.PSRE_CODE_VIDA) {
            this.maxFileSize = 10e+6;
            this.psreCode = CommonConstant.PSRE_CODE_VIDA;
      } else if (psreCode === CommonConstant.PSRE_CODE_PRIVY) {
            this.maxFileSize = 5e+6;
            this.psreCode = CommonConstant.PSRE_CODE_PRIVY;
      } else {
        this.maxFileSize = 10e+6;
      }

      if (files[0].size > this.maxFileSize) {
        const inputFileName = 'documentExample' + this.psreCode;
        this.msxForm.get(inputFileName).reset();
        const maxFileSizeMB = this.maxFileSize / 1e+6; // Convert to MB
        this.toastrService.warning(`Ukuran file tidak boleh lebih dari ${maxFileSizeMB} MB`, null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }


    }
  }

  onBase64Data($event) {
    console.log('rawFile', $event);
    this.rawFile = $event;
  }

  onCancel($event) {
    //
  }

  onSubmit() {
    if (this.signers.length === 0) {
      this.toastrService.warning('Silahkan tambah penanda tangan terlebih dulu!', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });

      this.openNewSignerModal();
      return;
    }

    const data = this.msxForm.getRawValue();
    console.log('manual signer form', this.msxForm.getRawValue());
    console.log('signers', this.signers);
    console.log('data',)

    this.router.navigate([PathConstant.SETTING_DOCUMENT_TEMPLATE], {
      state: {...data, ...{rawTemplate: this.rawFile, users: this.signers, extras: this.refs}},
      queryParams: {mode: CommonConstant.MODE_MANUAL_SIGNER}
    });
  }

  getQuestion(key: string) {
    return this.formObj.components.find(q => q.key === key);
  }

  changeSignQrOptions(vendorCode: string) {

    if (Vendor.PRIVY !== vendorCode) {
      const question = this.formObj.components.find(c => c.key === 'useSignQr');
      const options = [
        { key: '1', value: 'Ya' },
        { key: '0', value: 'Tidak' }
      ];

      question.options = options;
      this.useSignQrOptionList = options;
      this.msxForm.get('useSignQr').setValue('0');
      return;
    }

    // Set options to Yes only
    const question = this.formObj.components.find(c => c.key === 'useSignQr');
    const options = [{ key: '1', value: 'Ya' }];
    question.options = options;
    this.useSignQrOptionList = options;
    this.msxForm.get('useSignQr').setValue('1');
    this.cdr.detectChanges();
  }

  getPaymentTypeList(key: string) {
    // Reset current payment type value
    // this.msxForm.get('paymentType').reset();

    const request = new DataRequest();
    request.vendorCode = this.msxForm.get(key).value;
    this.dataService.getPaymentTypeList(request).subscribe(response => {
      console.log('payment list', response);
      const question = this.formObj.components.find(c => c.key === 'paymentType');
      const defaultAnswer = [{ key: '', value: 'Pilih Jenis Pembayaran' }];
      let options = [];

      for (const paymentType of response.paymentSignTypeList) {
        const option = {key: paymentType.code, value: paymentType.description};
        options.push(option);
      }

      options = defaultAnswer.concat(options);
      question.options = options;
      this.paymentTypeList = question.options;
      console.log(question.key, question.options);

      if (response.paymentSignTypeList.length === 1) {
        this.msxForm.get('paymentType').setValue(response.paymentSignTypeList[0].code);
        this.showPaymentTypeDropdown = false;
        // this.msxForm.get('paymentType').setValidators()
      } else if (response.paymentSignTypeList.length === 0) {
        this.showPaymentTypeDropdown = false;
      } else {
        this.showPaymentTypeDropdown = true;
      }

      
      this.msxForm.get('documentExamplePRIVY').setValidators([Validators.required]);
      this.msxForm.get('documentExampleVIDA').setValidators([Validators.required]);
      this.msxForm.get('documentExample').setValidators([Validators.required]);

      this.msxForm.get('documentExamplePRIVY').updateValueAndValidity();
      this.msxForm.get('documentExampleVIDA').updateValueAndValidity();
      this.msxForm.get('documentExample').updateValueAndValidity();

      const psre = this.msxForm.get('psreCode').value;

      if (psre === CommonConstant.PSRE_CODE_VIDA) {
        const tmp1 = this.formObj.components.find(x => x.key === 'documentExamplePRIVY');
        tmp1.required = false;
        const tmp2 = this.formObj.components.find(x => x.key === 'documentExample');
        tmp2.required = false;
        this.msxForm.get('documentExamplePRIVY').setValidators([]);
        this.msxForm.get('documentExample').setValidators([]);
        this.msxForm.updateValueAndValidity();
      } else if (psre === CommonConstant.PSRE_CODE_PRIVY) {
        const tmp1 = this.formObj.components.find(x => x.key === 'documentExampleVIDA');
        tmp1.required = false;
        const tmp2 = this.formObj.components.find(x => x.key === 'documentExample');
        tmp2.required = false;
        this.msxForm.get('documentExampleVIDA').setValidators([]);
        this.msxForm.get('documentExample').setValidators([]);
        this.msxForm.updateValueAndValidity();
      } else if (psre !== CommonConstant.PSRE_CODE_VIDA || psre !== CommonConstant.PSRE_CODE_PRIVY)  {
        const tmp1 = this.formObj.components.find(x => x.key === 'documentExamplePRIVY');
        tmp1.required = false;
        const tmp2 = this.formObj.components.find(x => x.key === 'documentExampleVIDA');
        tmp2.required = false;
        this.msxForm.get('documentExampleVIDA').setValidators([]);
        this.msxForm.get('documentExamplePRIVY').setValidators([]);
        this.msxForm.updateValueAndValidity();
      }

      console.log(this.psreCode);

      console.log('psrecode' + psre);

      this.psreCode = psre;

      console.log(this.psreCode);
      


      this.cdr.detectChanges();
    })
  }

  

  getOfficeList(regionCode: string) {

    this.msxForm.get('officeCode').reset();

    if (!regionCode) {
      this.officeList = [{ key: '', value: this.pilihCabang }];
      return;
    }

    this.dataService.getOfficeList(regionCode, this.global.user.role.tenantCode).subscribe(response => {

      const question = this.formObj.components.find(c => c.key === 'officeCode');
      const defaultAnswer = [{ key: '', value: this.pilihCabang }];
      let options = [];

      for (const paymentType of response.officeList) {
        const option = {key: paymentType.officeCode, value: paymentType.officeName};
        options.push(option);
      }

      options = defaultAnswer.concat(options);
      question.options = options;
      this.officeList = question.options;

      if (response.officeList.length === 1) {
        this.msxForm.get('officeCode').setValue(response.officeList[0].officeCode);
      }

      this.cdr.detectChanges();
    })
  }

  getAutomaticStampingStatus() {
    const request = new BaseRequest();
    this.dataService.getAutomaticStampingStatus(request).subscribe(response => {
      this.isAutoStamping = response?.statusStampingOtomatis;
      this.msxForm.get('isAutomaticStamp').setValue(this.isAutoStamping);
      this.cdr.detectChanges();
      console.log('automatic stamping', response?.statusStampingOtomatis);
    })
  }

  getMaxFileSize() {
    const request = new GetLovRequest();
    request.lovGroup = 'MAX_FILE_SIZE';

    this.dataService.getLov(request).subscribe(response => {
      console.log('max file size', response.lovList);
      const data = response.lovList[0];

      if (data) {
        this.maxFileSize = Number(data?.code);
      }
    })
  }

  async openNewSignerModal() {
    // Check selected psre vendor
    if (this.msxForm.get('psreCode').invalid) {
      this.toastrService.warning('Silahkan pilih salah satu vendor PSrE terlebih dulu!', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }

    const modal = this.ngModal.open(AddSignerComponent);
    modal.componentInstance.signers = this.signers;
    modal.componentInstance.vendorCode = this.msxForm.get('psreCode').value;

    modal.dismissed.subscribe(result => {
      console.log('data', result);
      const signer  = new Signer();
      signer.name   = result.data.name;
      signer.phone  = result.data.phone;
      signer.email  = result.data.email;

      const idx = this.signers.indexOf(signer);
      if (idx !== -1) {
        return;
      }

      this.signers.push(signer);
      this.signers.sort((x, y) => x.name.localeCompare(y.name));
      console.log('signers', this.signers);
      this.cdr.detectChanges();
    })
  }

  validateSigner() {
    if (this.signers.length === 0) {
      return;
    }

    for (const signer of this.signers) {
      const idxSigner = this.signers.indexOf(signer);
      const request = new AutofillUserRequest();
      request.vendorCode = this.msxForm.get('psreCode').value;
      request.checkRegisterDetail = signer.email;

      this.userService.autofilUser(request).subscribe(response =>  {
        console.log(`check ${signer.email}`, response);
        if (response.status.code !== 0) {
          this.signers.splice(idxSigner, 1);
          console.log('update signers', this.signers);
        }
      })
    }
  }

  deleteSigner(signer: Signer){
    const index = this.signers.indexOf(signer);
    this.signers.splice(index, 1);
  }

}
