export class UserProfile {
  adminEmail: string;
  nama: string;
  idKtp: string;
  kelurahan: string;
  kecamatan: string;
  kota: string;
  provinsi: string;
  provinceId?: number;
  districtId?: number;
  subdistrictId?: number;
  kodePos: string;
  loginId: string;
  alamat: string;
  jenisKelamin: string;
  tlp: string;
  tglLahir: string;
  tmpLahir: string;
  email: string;
  idPhoto: string;
  selfPhoto: string;
  npwp: string;
  npwpPhoto: string;
  ttd: string;
  psreCode: string;
  redirect: true;
  fullname: string;
  autosignStatus: string;
  changePwdLogin: string;
  role?: {
    tenantCode: string;
    tenantName: string;
    roleCode: string;
    roleName: string;
  };
  roles: [{
    tenantCode: string;
    tenantName: string;
    roleCode: string;
    roleName: string;
  }];
  officeCode: string;
  pathSrc: string;
}
