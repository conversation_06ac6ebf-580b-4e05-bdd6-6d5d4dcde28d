import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, NgModel, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmSignDocumentRequest } from 'app/model/api/confirmSignDocument.request';
import { IpService } from 'app/services/api/ip.service';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { DeviceDetectorService } from 'ngx-device-detector';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { Document } from 'app/model/document';
import { SigningRequestCompleteComponent } from '../signing-request-complete/signing-request-complete.component';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { CountlyService } from 'app/services/api/countly.service';
import { CommonConstant } from 'app/shared/constant/common.constant';
@Component({
  selector: 'app-progress-signing',
  templateUrl: './progress-signing.component.html',
  styleUrls: ['./progress-signing.component.scss']
})
export class ProgressSigningComponent implements OnInit {
  @Input() documents : Document[];
  @Input() loginId : string;
  @Input() msg: string;
  @Input() tenantCode: string;
  
  templateForm: FormGroup;
  remainingText: number = 0;
  waiting: boolean;
  tmpFile: string;
  enableSubmit: boolean;
  progressFull:any;
  progressvalue:any;
  totalDoc: any;
  totalSuccess: any;
  totalFailed: any;

  isRedirect: boolean;
  redirectUrl: string;
  url: string;
  redirectCountDown: number;

  //ipAddress: any;

  swal = swalFunction;
  constructor(private formBuilder: FormBuilder, private toastrService: ToastrService, public activeModal: NgbActiveModal,
    private http: HttpClient, private router: Router, private global: GlobalService, private ipService: IpService,
    private deviceService: DeviceDetectorService, private modalService: NgbModal, private countlyService: CountlyService) { 
}

  async ngOnInit(): Promise<void> {
    this.progressFull = 100;
    this.progressvalue = 0;
    this.totalSuccess = 0;
    this.totalFailed = 0;

    this.waiting = false;
    this.enableSubmit = true;
    this.templateForm = this.formBuilder.group({
      rating: [''],
      comment: ['', Validators.maxLength(200)]
    });
    
    //ambil ip address dari be
    //await this.getIdAddress();
    await this.confirmSignDocument();
    this.onChanges();
    this.countlyService.initiate();
  }

  // async getIdAddress(){
  //   await this.ipService.getIpAddress().toPromise().then(ipResponse =>{
  //     this.ipAddress = ipResponse.ip;
  //   })
  // }

  async confirmSignDocument(){
    const request = new ConfirmSignDocumentRequest;
    for (let i = 0; i < this.documents.length; i++) {
      if (this.msg) {
        request.documentIds.push(this.documents[i].documentId);
      } else {
        request.documentId.push(this.documents[i].documentId);
      }
    }
    request.email = this.loginId;
    //request.ipAddress = this.ipAddress;
    request.browser = navigator.userAgent;

    let url = URLConstant.confirmSignDocument;

    if (this.msg) {
      request.tenantCode = this.tenantCode;
      request.msg = this.msg;
      url = URLConstant.confirmSignDocumentEmbedV2;
    }
    
    await this.http.post(url, request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
          console.log(response);
        } 

        if (response['redirectCountDown'] !== 0 && this.msg) {
          this.isRedirect = true;
          this.redirectCountDown = response['redirectCountDown'];
          this.redirectUrl = response['redirectUrl'] 
          if (!this.redirectUrl.startsWith('http://') && !this.redirectUrl.startsWith('https://')) {
            this.url = 'https://' + this.redirectUrl; // You can default to 'http://'
          } else {
            this.url = this.redirectUrl;
          }

          console.log(this.isRedirect);
          console.log(this.redirectCountDown);
          console.log(this.url);
        }
        this.countProgressBar(response);
        this.enableSubmit = false;
      }
    )
  }
  
  countProgressBar(response){
    this.progressvalue = this.progressFull / this.documents.length;
    console.log('progress value', this.progressvalue);
    for (let i = 0; i < this.documents.length; i++) {
        if (response['status']['code'] == 0) {
          this.totalSuccess++;
        } else{
          this.totalFailed++;
        }
        
        this.progressvalue = this.progressvalue + this.progressvalue;
    }
  }

  onChanges(): void {
    this.templateForm.valueChanges.subscribe(value => {
      if (value.rating !== '') {
       // this.enableSubmit = false;
      }
    })
  }
  
  cancel() {
    this.router.navigate(['dashboard']);
  }

  onSubmit() {
    const rating = this.templateForm.controls['rating'].value;
    const comment = this.templateForm.controls['comment'].value;

    console.log(rating);
    console.log(comment);

    if (this.isRatingMissingWithComment(rating, comment)) {
        return this.showRatingMissingInfo();
    }

    if (rating !== '') {
        return this.handleFeedbackSubmission(rating, comment);
    }

    this.handleDismissAndOpenModal();
  }

  private isRatingMissingWithComment(rating: string, comment: string): boolean {
      return rating === '' && comment !== '';
  }

  private showRatingMissingInfo() {
      return this.toastrService.info(
          'Mohon isi bintang, jika ingin mengisi komen',
          null,
          { positionClass: CommonConstant.TOAST_BOTTOM_RIGHT }
      );
  }

  private handleFeedbackSubmission(rating: string, comment: string) {
      const request = this.createFeedbackRequest(rating, comment);
      const url = this.getFeedbackUrl();

      this.http.post(url, request).subscribe((response) => {
          if (response["status"]["code"] !== 0) {
              this.swal.Error(response["status"]["message"]);
          } else {
              this.handleSuccessfulFeedback();
          }
      });
  }

  private createFeedbackRequest(rating: string, comment: string) {
      return {
          audit: {
              callerId: this.loginId
          },
          comment,
          loginId: this.loginId,
          feedbackValue: rating,
          msg: this.msg,
          tenantCode: this.tenantCode
      };
  }

  private getFeedbackUrl(): string {
      return this.msg ? URLConstant.SendFeedbackEmbedV2 : URLConstant.SendFeedback;
  }

  private handleSuccessfulFeedback() {
      this.toastrService.info(
          'Terimakasih telah memberikan feedback kepada kami',
          null,
          { positionClass: CommonConstant.TOAST_BOTTOM_RIGHT }
      );
      this.activeModal.dismiss('0');
      this.openSigningRequestCompleteModal();
  }

  private handleDismissAndOpenModal() {
      this.activeModal.dismiss('0');
      this.openSigningRequestCompleteModal();
  }

  private openSigningRequestCompleteModal() {
      const modal = this.modalService.open(SigningRequestCompleteComponent, {
          backdrop: 'static',
          keyboard: false,
          size: 'l'
      });

      if (this.msg) {
        console.log("embed");
        modal.componentInstance.msg = this.msg;         
        modal.componentInstance.tenantCode = this.tenantCode;
        modal.componentInstance.isRedirect = this.isRedirect;
        modal.componentInstance.redirectUrl = this.url;
        modal.componentInstance.redirectCountDown = this.redirectCountDown;
      }
  }


  valueChange(text){
    this.remainingText = text.length;
  }

}
