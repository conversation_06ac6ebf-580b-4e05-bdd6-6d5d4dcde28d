<div class="row" style="margin-top: 15px">
  <div class="col-6">
    <div class="content-header" style="margin-top: 0 !important;">Inquiry Document - Detail</div>
  </div>
  <div class="col-6 text-right">
    <button class="btn btn-danger" (click)="doBack()">Back</button>
  </div>
</div>


<div class="row match-height">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <div class="row" style="margin-bottom: 8px">
          <div class="col-6">
            <div class="row">
              <div class="col-5">
                Reference No
              </div>
              <div class="col-7">
                : {{inquiry.refNo}}
              </div>
            </div>
          </div>

          <div class="col-6">
            <div class="row">
              <div class="col-5">
                Document Type
              </div>
              <div class="col-7">
                : {{inquiry.docType}}
              </div>
            </div>
          </div>
        </div>
        <div class="row p-10">
          <div class="col-6">
            <div class="row">
              <div class="col-5">
                Customer Name
              </div>
              <div class="col-7">
                : {{inquiry.custName}}
              </div>
            </div>
          </div>

          <div class="col-6">
            <div class="row">
              <div class="col-5">
                Request Date
              </div>
              <div class="col-7">
                : {{inquiry.reqDate}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<section id="inquiry-list">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-content">
          <div class="card-body">
            <div class="row">
              <div class="col-6">
                <div class="form-group row">
                  <div class="col-2">
                    <label for="status" class="col-form-label">Status</label>
                  </div>
                  <div class="col-7">
                    <ng-select id="status" [items]="status" bindLabel="name" [ngModel]="selectedStatus" placeholder="Select Status" (change)="onChangeStatus($event)"></ng-select>
                  </div>
                </div>
              </div>
              <div class="col-6 text-right">
                <div class="action-group" style="margin-bottom: 10px">
                  <button *ngIf="selectedStatus.name === 'Complete' && chkBoxSelected.length > 0" class="btn btn-info" style="margin-right: 8px"><i class="ft-folder"></i> .zip {{chkBoxSelected?.length}} Document</button>
                  <button *ngIf="selectedStatus.name !== 'Complete' && chkBoxSelected.length > 1" class="btn btn-danger" style="margin-right: 8px" (click)="bulkSign()"><i class="ft-edit"></i> Sign {{chkBoxSelected?.length}} Document</button>
                </div>
              </div>
            </div>
            <ngx-datatable [rows]="filteredDocuments" [rowHeight]="50" class="bootstrap core-bootstrap" [limit]="10"
                           [columnMode]="ColumnMode.force" [headerHeight]="50" [footerHeight]="50" rowHeight="auto"
                           [selected]="chkBoxSelected" [selectionType]="SelectionType.checkbox"
                           (select)="customChkboxOnSelect($event)" [scrollbarH]="true">
              <ngx-datatable-column [width]="50" [sortable]="false" [canAutoResize]="false" [draggable]="false"
                                    [resizeable]="false" *ngIf="canSign()">
                <ng-template ngx-datatable-header-template let-value="value" let-allRowsSelected="allRowsSelected"
                             let-selectFn="selectFn">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" [checked]="allRowsSelected" (change)="selectFn(!allRowsSelected)" />
                  </div>
                </ng-template>
                <ng-template ngx-datatable-cell-template let-row="row" let-value="value" let-isSelected="isSelected"
                             let-onCheckboxChangeFn="onCheckboxChangeFn">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox"  [checked]="isSelected" (change)="onCheckboxChangeFn($event)" />
                  </div>
                </ng-template>
              </ngx-datatable-column>
              <ngx-datatable-column name="Document Name" prop="name" [width]="200"></ngx-datatable-column>
              <ngx-datatable-column name="Status" prop="status" [width]="15"></ngx-datatable-column>
              <ngx-datatable-column name="Complete Date" prop="completeDate" [width]="75"></ngx-datatable-column>
              <ngx-datatable-column name="&nbsp;" [width]="75" *ngIf="canSign()">
                <ng-template let-row="row" let-name="value" ngx-datatable-cell-template>
                  <button *ngIf="row.status === 'Complete'" class="btn btn-sm btn-danger" disabled><i class="ft-edit"></i> Signing Document</button>
                  <button *ngIf="row.status !== 'Complete'" class="btn btn-sm btn-danger" (click)="signing(row)"><i class="ft-edit"></i> Signing Document</button>
                </ng-template>
              </ngx-datatable-column>
              <ngx-datatable-column name="Action" [width]="50">
                <ng-template let-row="row" let-name="value" ngx-datatable-cell-template>
                  <i class="ft-eye text-info cursor-pointer" style="margin-right: 10px" [routerLink]="['/inquiry/detail/document']" [queryParams]="row"></i>
                  <i class="ft-download text-info cursor-pointer" style="margin-right: 10px"></i>
                  <i class="ft-user text-info cursor-pointer" (click)="openModal()"></i>
                </ng-template>
              </ngx-datatable-column>
            </ngx-datatable>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
