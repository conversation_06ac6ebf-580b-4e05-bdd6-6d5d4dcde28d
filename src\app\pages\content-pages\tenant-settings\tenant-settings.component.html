<div class="row" style="margin-top: 15px;">
    <div class="col-{{formColSize}}">
        <div class="content-header" style="margin-top: 0 !important;" translate>
            Tenant Setting
        </div>
    </div>
</div>

<div class="row match-height">
    <div class="col-{{formColSize}}">
        <div class="card">
            <div class="card-content">
                <div class="card-body">
                    <form [formGroup]="tenantSettingsForm">
                        <!-- Ref number label field -->
                        <p class="content-header" translate>Ref Number Label</p>
                        <div class="form-group">
                            <input type="text" formControlName="refNumberLabel" id="refNumberLabel"
                                class="form-control mb-1 col-5">
                            <div *ngIf="tenantSettingsForm.get('refNumberLabel').invalid"
                                class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                {{validationMessages.refNumberLabel.message}}</div>
                        </div>

                        <!-- URL upload field -->
                        <div class="row" style="margin-top: 2%;">
                            <p class="content-header" style="margin-left: 3%;" translate>URL Upload</p>
                        </div>
                        <div class="row input-group mb-1" style="margin-left: 1px;">
                            <input class="form-control col-8" placeholder="Tulis URL Upload" formControlName="uploadUrl" id="uploadUrl">
                            <button class="input-group-addon btn btn-secondary col-{{buttonColSize}}" (click)="copyUploadUrl()"
                            style="padding-top: 0px; padding-bottom: 0px; border-top-left-radius: 0px; border-bottom-left-radius: 0px;" translate>Copy</button>
                        </div>
                        <div *ngIf="tenantSettingsForm.get('uploadUrl').invalid"
                                class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                {{validationMessages.uploadUrl.message}}
                        </div>

                        <!-- Balance threshold field -->
                        <p class="content-header" translate>Balance Threshold</p>
                        <div *ngFor="let balance of balances" class="form-group">
                            <div *ngIf="balanceShow[balance.code]">
                                <label for="{{balance.code}}" class="form-title" translate>{{balance.description}}</label>
                                <input [value]="balanceValue[balance.code]" type="number" min="0" formControlName="{{balance.code}}" id="{{balance.code}}" class="form-control mb-1 col-2">
                            </div>
                        </div>

                        <!-- Reminder receiver field -->
                        <p class="content-header" translate>Balance Reminder Email Receiver</p>
                        <div *ngIf="emailList.length === 0"
                                class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                {{validationMessages.balanceReminderReceiver.message}}</div>
                        <div *ngFor="let email of emailList; let i = index">
                            <div class="input-group mb-1">
                                <input [(ngModel)]="emailList[i].address" [ngModelOptions]="{standalone: true}"
                                    class="form-control col-5" placeholder="Tulis email">
                                <button class="input-group-addon btn btn-danger col-{{buttonColSize}}" (click)="deleteEmail(i)" style="padding-top: 0px; padding-bottom: 0px; 
                                    border-top-left-radius: 0px; border-bottom-left-radius: 0px;" translate>Delete</button>
                            </div>
                        </div>
                        <button class="btn btn-info" (click)="addEmail()" translate>Add</button>

                        <!-- Stamping otomatis field -->
                        <div class="row" style="margin-top: 2%;">
                            <p class="content-header" style="margin-left: 3%;" translate>Stamping Otomatis</p>
                        </div>
                        <label class="switch">
                            <input type="checkbox" formControlName="automaticSign" id="automaticSign">
                            <span class="slider round"></span>
                        </label>

                        <!-- Activation callback URL field -->
                        <!-- <div class="row" style="margin-top: 2%;">
                            <p class="content-header" style="margin-left: 3%;" translate>URL Callback Aktivasi</p>
                        </div>
                        <div class="row input-group mb-1" style="margin-left: 1px;">
                            <input class="form-control col-8" placeholder="Tulis URL Callback Aktivasi" formControlName="activationCallbackUrl" id="activationCallbackUrl">
                            <button class="input-group-addon btn btn-secondary col-{{buttonColSize}}" (click)="testCallbackUrl()"
                            style="padding-top: 0px; padding-bottom: 0px; border-top-left-radius: 0px; border-bottom-left-radius: 0px;" translate>Coba</button>
                        </div>
                        <div *ngIf="tenantSettingsForm.get('activationCallbackUrl').invalid"
                                class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                {{validationMessages.activationCallbackUrl.message}}
                        </div> -->

                        <!-- Pengiriman Notifikasi SMS / WA -->

                        <div class="row" style="margin-top: 2%;">
                            <p class="content-header" style="margin-left: 3%; margin-bottom: 2%;" translate>Pengiriman Notifikasi Tanpa Email</p>
                        </div>
                        <div class="form-check form-check-inline" >
                            <input formControlName="useWaMessage" class="form-check-input radio-pengiriman-notifikasi" type="radio" 
                                name="useWaMessage" id="gridRadios1" value="0">
                            <label class="form-check-label label-pengiriman-notifikasi" for="gridRadios1" translate>
                              SMS
                            </label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input formControlName="useWaMessage" class="form-check-input radio-pengiriman-notifikasi" type="radio" 
                                name="useWaMessage" id="gridRadios2" value="1">
                            <label class="form-check-label label-pengiriman-notifikasi" for="gridRadios2" translate>
                              Whatsapp
                            </label>
                        </div>

                        <!-- URL Callback -->

                        <div class="row" style="margin-top: 1%;">
                            <p class="content-header" style="margin-left: 3%;" translate>URL Callback</p>
                        </div>
                        <div class="row input-group mb-1" style="margin-left: 1px;">
                            <input class="form-control col-8" placeholder="Tulis URL Callback" formControlName="clientCallbackUrl" id="clientCallbackUrl">
                            <button class="input-group-addon btn btn-secondary col-{{buttonColSize}}" (click)="tryClientCallbackUrl()"
                            style="padding-top: 0px; padding-bottom: 0px; border-top-left-radius: 0px; border-bottom-left-radius: 0px;" translate>Coba</button>
                        </div>
                        <div *ngIf="tenantSettingsForm.get('clientCallbackUrl').invalid"
                                class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                {{validationMessages.clientCallbackUrl.message}}
                        </div>

                        <!-- URL Redirect Aktivasi -->

                        <div class="row" style="margin-top: 2%;">
                            <p class="content-header" style="margin-left: 3%;" translate>URL Redirect Aktivasi</p>
                        </div>
                        <div class="row input-group mb-1" style="margin-left: 1px;">
                            <input class="form-control col-8" placeholder="Tulis URL Redirect Aktivasi" formControlName="clientActivationRedirectUrl" id="clientActivationRedirectUrl">
                        </div>
                        <div *ngIf="tenantSettingsForm.get('clientActivationRedirectUrl').invalid"
                                class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                {{validationMessages.clientActivationRedirectUrl.message}}
                        </div>

                        <!-- URL Redirect Signing -->

                        <div class="row" style="margin-top: 2%;">
                            <p class="content-header" style="margin-left: 3%;" translate>URL Redirect Tanda Tangan</p>
                        </div>
                        <div class="row input-group mb-1" style="margin-left: 1px;">
                            <input class="form-control col-8" placeholder="Tulis URL Redirect Tanda Tangan" formControlName="clientSigningRedirectUrl" id="clientSigningRedirectUrl">
                        </div>
                        <div *ngIf="tenantSettingsForm.get('clientSigningRedirectUrl').invalid"
                                class="help-block mt-1 text-danger"><i class="ft-alert-circle align-middle"></i>
                                {{validationMessages.clientSigningRedirectUrl.message}}
                        </div>
                        
                        <!-- Cancel / Save button -->
                        <div class="row" style="margin-top: 20px;">
                            <div class="col-12 text-center">
                                <button class="btn btn-light mr-2" (click)="goBack()" translate>Cancel</button>
                                <button class="btn btn-info" (click)="updateTenantSetting()"
                                    [disabled]="tenantSettingsForm.invalid || emailList.length === 0" translate>Save</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>