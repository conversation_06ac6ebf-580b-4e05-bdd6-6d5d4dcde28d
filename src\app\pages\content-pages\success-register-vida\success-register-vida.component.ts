import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { MsxAlertComponent } from 'app/shared/components/msx-alert/msx-alert.component';
import { GlobalService } from 'app/shared/data/global.service';
import { ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-success-register-vida',
  templateUrl: './success-register-vida.component.html',
  styleUrls: ['./success-register-vida.component.scss']
})
export class SuccessRegisterVidaComponent implements OnInit {

  state: any;
  user:any;
  date:any;
  time:any;
  messages  = [];

  timeLeft = 0;
  interval;
  modalRef: any;
  url: any;

  constructor(private global: GlobalService, private ngModal: NgbModal, private activatedRoute: ActivatedRoute, private cdr: ChangeDetectorRef,
    private router: Router) {
    this.activatedRoute.queryParams.subscribe(() => {
      this.state = this.router.getCurrentNavigation().extras.state;
      console.log('state', this.state);
    })
   }

  ngOnInit(): void {    
    this.user = 'User ('+ this.state.name + ' - ' + this.state.nik + ' - ' + this.state.phone + ' - ' + this.state.email + ')';
    this.timeLeft = this.state.redirectCountDown;

    var d = new Date(this.state.registeredDate);
    this.date = d.toLocaleDateString('id', { day:'2-digit', month:'long', year:'numeric'});
    this.time = d.toLocaleTimeString('it-IT');

    const modal = this.ngModal.open(MsxAlertComponent, { size: 'md', backdrop: 'static', keyboard: false });
    this.modalRef = modal;

    modal.componentInstance.image = './assets/img/img_success.svg';
    modal.componentInstance.title = 'Registrasi Berhasil';

    if (this.state.redirectCountDown === 0) {
      modal.componentInstance.message = `${this.user} Proses registrasi dan aktivasi Anda sudah selesai. Silahkan menunggu notifikasi permintaan tanda tangan untuk lanjut ke proses berikutnya.`;
    } else {
      this.updateModalMessage();
      this.startTimer();
      this.redirectUrl();
    }


    if (localStorage.getItem('sign')) {
      const data = JSON.parse(localStorage.getItem('sign'));
      if (this.global.user) {
        location.href = data['url'];
        localStorage.removeItem('sign');
      }
    }
    
  }

  ngOnDestroy(): void {
    if (this.interval) {
      clearInterval(this.interval); 
    }
  }

  updateModalMessage() {
    this.modalRef.componentInstance.message = 
    `${this.user} Proses registrasi dan aktivasi Anda sudah selesai. Silahkan menunggu notifikasi permintaan tanda tangan untuk lanjut ke proses berikutnya. <br><br> Halaman akan diredirect dalam ${this.timeLeft} detik.<br>Jika Anda tidak diarahkan ke halaman baru silahkan, <a href="${this.url}" rel="noreferrer">klik disini</a>`
  }

  redirectUrl() {
    if (!this.state.redirectUrl.startsWith('http://') && !this.state.redirectUrl.startsWith('https://')) {
      this.url = 'https://' + this.state.redirectUrl; // You can default to 'http://'
    } else {
      this.url = this.state.redirectUrl;
    }
    console.log(this.url);
  }

  startTimer() {
    this.interval = setInterval(() => {
      if (this.timeLeft > 0) {
        this.timeLeft--;
        this.updateModalMessage();
      } else {
        this.timeLeft = 0;
        window.location.href = this.url;
      }
    }, 1000);
  }
}


