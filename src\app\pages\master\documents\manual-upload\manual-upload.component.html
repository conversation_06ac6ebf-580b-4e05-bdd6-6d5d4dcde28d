<div class="row" style="margin-top: 15px">
  <div class="col-6">
    <div class="content-header" style="margin-top: 0 !important;" translate>Setting Document</div>
  </div>
</div>

<div class="row match-height">
  <div class="col-{{colsize}}" >
    <div class="card">
      <div class="card-content">
        <div class="card-body">
          <form [formGroup]="msxForm" (ngSubmit)="onSubmit()" id="msxForm">
            <ng-container *ngIf="formObj?.params && formObj.params?.length > 0">
              <input *ngFor="let param of formObj.params" type="hidden" [formControlName]="param.key"
                     [value]="param.value" [id]="param.key" />
            </ng-container>

            <!-- PSrE -->
            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('psreCode')" [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>
            </div>

            <!-- Ref Number / Nomor Dokumen -->
            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-text [form]="msxForm" [question]="getQuestion('referenceNo')" [direction]="formObj.direction"></app-text>
              </div>
            </div>

            <!-- Nama Dokumen -->
            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-text [form]="msxForm" [question]="getQuestion('documentName')" [direction]="formObj.direction"></app-text>
              </div>
            </div>

            <!-- Tanggal Dokumen -->
            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-date [form]="msxForm" [question]="getQuestion('documentDate')" [direction]="formObj.direction"></app-date>
              </div>
            </div>

            <!-- Payment sign type -->
            <div class="row" *ngIf="showPaymentTypeDropdown">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [options]="paymentTypeList" [question]="getQuestion('paymentType')"
                            [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>
            </div>

            <!-- Sign sequence -->
            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('isSequence')"
                            [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>
            </div>

            <!-- Use Sign QR -->
            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [options]="useSignQrOptionList" [question]="getQuestion('useSignQr')"
                            [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>
            </div>

            <!-- Business Line -->
            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('businessLineCode')"
                            [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>
            </div>

            <!-- Region -->
            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('regionCode')"
                            [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>
            </div>

            <!-- Office -->
            <div class="row">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [options]="officeList" [question]="getQuestion('officeCode')"
                            [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>
            </div>

            <!-- Upload document -->
            <!-- Case VIDA -->
            <div class="row" *ngIf="psreCode === 'VIDA'">
              <div class="col-{{formObj.colSize}}">
                <app-file [form]="msxForm" [direction]="formObj.direction" [question]="getQuestion('documentExampleVIDA')" (change)="onInput($event)" (rawFile)="onBase64Data($event)"></app-file>
              </div>
            </div>

            <!-- Case PRIVY -->
            <div class="row" *ngIf="psreCode === 'PRIVY'">
              <div class="col-{{formObj.colSize}}">
                <app-file [form]="msxForm" [direction]="formObj.direction" [question]="getQuestion('documentExamplePRIVY')" (change)="onInput($event)" (rawFile)="onBase64Data($event)"></app-file>
              </div>
            </div>

            <!-- other -->
            <div class="row" *ngIf="psreCode !== 'VIDA' && psreCode !== 'PRIVY'">
              <div class="col-{{formObj.colSize}}">
                <app-file [form]="msxForm" [direction]="formObj.direction" [question]="getQuestion('documentExample')" (change)="onInput($event)" (rawFile)="onBase64Data($event)"></app-file>
              </div>
            </div>


            <!-- Use emeterai checkbox -->
            <div class="row" style="margin-bottom: 8px">
              <div class="col-{{formObj.colSize}}">
                <div class="custom-control custom-checkbox" [formGroup]="msxForm">
                  <input type="checkbox" formControlName="isMeterei" class="custom-control-input" id="isMeterei" (change)="onChange($event)" />
                  <label class="custom-control-label" for="isMeterei" style="font-size: 11px">{{getQuestion('isMeterei').label}}</label>
                </div>
              </div>
            </div>

            <!-- Tipe dokumen Peruri -->
            <div class="row" *ngIf="hasEmeterei">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('documentTypePeruri')"
                            [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>
            </div>

            <!-- Automatic stamp -->
            <div class="row" *ngIf="hasEmeterei">
              <div class="col-{{formObj.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('isAutomaticStamp')"
                            [direction]="formObj.direction" (selected)="onSelect($event)"></app-select>
              </div>
            </div>

            <!-- Add Signer -->
            <div class="row" style="margin-top: 16px; margin-bottom: 16px">
              <div class="col-6">
                <i class="ft-users" style="font-size: 14px; margin-right: 8px"></i>
                <strong translate>Signer</strong>
              </div>
              <div class="col-6 text-right">
                <a href="javascript:" (click)="openNewSignerModal()">Tambah</a>
              </div>
              <div class="col-12">
                <table class="table table-striped table-hover">
                  <tr *ngIf="signers.length === 0">
                    <td><small class="text-info"><i class="ft-info"></i> Belum ada daftar penanda tangan!</small></td>
                  </tr>
                  <tr *ngFor="let data of signers">
                    <td>
                      <p>
                        <i class="ft-user"></i> <strong>{{data.name}}</strong><br/>
                        <i class="ft-phone"></i> {{data.phone}},
                        <i class="ft-mail" *ngIf="data.email"></i> {{data.email}}
                        <span style="
                          float: right;
                          margin-right: -16px;
                          cursor: pointer;
                      "><i class="ft-user-x text-danger" placement="top" ngbTooltip="Hapus Penanda Tangan!" (click)="deleteSigner(data)"></i></span>
                      </p>
                    </td>
                  </tr>
                </table>
              </div>
            </div>

            <div class="row">
              <div class="col-12 text-center">
                <button class="btn btn-light mr-2" type="button" (click)="onCancel($event)">{{'Cancel' | translate}}</button>
                <button class="btn btn-info" type="submit" [disabled]="msxForm.invalid">Next</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

