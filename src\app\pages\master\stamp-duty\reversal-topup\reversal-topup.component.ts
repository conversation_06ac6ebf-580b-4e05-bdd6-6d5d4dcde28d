import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ReversalTopup } from 'app/model/reversal-topup';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { ReversalTopupView } from '../view/reversal-topup-list.view';
import * as swalFunction from '../../../../shared/data/sweet-alerts';
import { HttpClient } from '@angular/common/http';
import { Location } from '@angular/common';
import { ReverseTopupResponse } from 'app/shared/dto/stamp-duty/reverse-topup.response';
import { ReverseTopupRequest } from 'app/shared/dto/stamp-duty/reverse-topup.request';

@Component({
  selector: 'app-reversal-topup',
  templateUrl: './reversal-topup.component.html',
  styleUrls: ['./reversal-topup.component.scss']
})
export class ReversalTopupComponent implements OnInit {

  view: MsxView;
  buttonList: Button[];
  serviceUrl = URLConstant.GetListReverseTopup;
  
  swal = swalFunction;

  constructor(private router: Router, private http: HttpClient, 
    private location: Location) { }

  ngOnInit(): void {
    this.view = ReversalTopupView;
    this.buttonList = [
      { name: 'Back', class: 'btn btn-secondary', hide: false, icon: '' }
    ]
  }

  onBtnClickListener(event) {
    switch (event['name']) {
      case this.buttonList[0].name:
        this.router.navigate([PathConstant.LIST_STAMP_DUTY]);
        break;
      default:
        break;
    }
  }

  onItemClickListener(event) {
    const data = event['data'];

    switch (event['act']['type']) {
      case Act.Cancel:
        return this.reverseTopup(data);

      default:
        return;
    }
  }

  reverseTopup(data: ReversalTopup) {
    this.swal.ConfirmWithInput('Apakah Anda yakin akan membatalkan saldo meterai ini?').then(
      (result) => {
        if (result.isConfirmed === true) {
          const request: ReverseTopupRequest = new ReverseTopupRequest();
          request.vendorCode = data.vendorCode;
          request.invoiceNo = data.invoiceNo;
          request.qty = data.availableQty;
          request.notes = result.value;
          this.http.post<ReverseTopupResponse>(URLConstant.ReverseTopup, request).subscribe(
            (response) => {
              if (response.status.code !== 0) {
                console.log('Reverse Topup Error', response);
              } else {
                this.swal.SuccessWithRedirect('Reverse Topup Sukses, ' + data.availableQty + ' meterai telah dihapus', this.router.url);
              }
            }
          );
        }
      }
    );
  }

}
