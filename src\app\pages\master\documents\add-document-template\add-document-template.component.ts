import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import {FormModel} from '../../../../shared/components/ms-form/models';
import {FormConstant} from '../../../../shared/components/ms-form/constants/form.constant';
import {ActivatedRoute, Router} from '@angular/router';
import {BehaviorSubject} from 'rxjs';
import {DocumentTemplate} from '../../../../model/template';
import {PathConstant} from '../../../../shared/constant/PathConstant';
import {CommonConstant} from '../../../../shared/constant/common.constant';
import {HttpClient} from '@angular/common/http';
import {BaseResponse} from '../../../../shared/dto/base.response';
import {URLConstant} from '../../../../shared/constant/URLConstant';
import {ToastrService} from 'ngx-toastr';
import {QuestionDropdown, QuestionTextbox} from '../../../../shared/components/ms-form/questions';
import {QuestionFile} from '../../../../shared/components/ms-form/questions/question-file';
import { Vendor } from 'app/shared/constant/vendor';
import { FormGroup } from '@angular/forms';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { Location } from '@angular/common';
import { ConfirmationModel } from 'app/shared/components/pre-formsubmit-prompt/confirmation.model';
import { PreFormsubmitPromptComponent } from 'app/shared/components/pre-formsubmit-prompt/pre-formsubmit-prompt.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-add-document-template',
  templateUrl: './add-document-template.component.html',
  styleUrls: ['./add-document-template.component.scss']
})
export class AddDocumentTemplateComponent implements OnInit {

  mForm: FormModel<any>;
  msxForm: FormGroup;
  mode = 'Add';
  submitLabel = 'Next';

  state: any;
  routeData: any;

  dataSubj: BehaviorSubject<DocumentTemplate> = new BehaviorSubject<DocumentTemplate>(null);

  useSignQrOptionList: {key: string; value: string}[] = [];

  private documentTemplate: string;
  rawFile: string;

  constructor(private activatedRoute: ActivatedRoute, private router: Router, private http: HttpClient,
              private toastrService: ToastrService, private fcs: MsxFormControlService, private cdr: ChangeDetectorRef,
              private readonly location: Location, private modalService: NgbModal) {

    this.routeData = this.activatedRoute.snapshot.data;
    this.mode = this.routeData['mode'];

    this.activatedRoute.queryParams.subscribe(() => {
      this.state = this.router.getCurrentNavigation().extras.state;
      this.dataSubj.next(this.state);
      console.log('state', this.state);
    });
  }

  ngOnInit(): void {
    if (this.mode === CommonConstant.MODE_EDIT) {
      this.submitLabel = 'Save';
    }

    this.initView();
    const controls = [...this.mForm.params, ...this.mForm.components];
    this.msxForm = this.fcs.toFormGroup(controls);

    if (this.dataSubj) {
      this.dataSubj.asObservable().subscribe(next => {
        if (!next) {
          return;
        }

        this.msxForm.patchValue(next);
      });
    }
  }

  openPrompt(data) {
    const datas: ConfirmationModel[] = [];

    const modal = this.modalService.open(PreFormsubmitPromptComponent);
    modal.result.then((result) => {
      if (result === 'submit') {
        this.onNext();
      }
    });
  }

  onNext() {
    const data = this.msxForm.value;

    if (this.mode === CommonConstant.MODE_EDIT && !data['documentExample']) {
      const request = {...data, ...{isSignLocOnly: '0'}};
      this.http.post<BaseResponse>(URLConstant.UpdateTemplate, request).subscribe(response => {
        if (response.status.code !== 0) {
          console.log('error', response);
          return;
        }
        
        this.toastrService.info(`Template ${data['documentTemplateName']} berhasil di update.`, null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        this.router.navigate([PathConstant.LIST_DOCUMENT_TEMPLATE]);
      });
      return;
    }

    this.http.post(URLConstant.CheckIsDocumentExist, {documentTemplateCode: data['documentTemplateCode']}).subscribe(response => {
      if (response['documentTemplateExist'] && this.mode !== CommonConstant.MODE_EDIT) {
        this.toastrService.error(`Template Code: ${data['documentTemplateCode']} sudah digunakan.`, null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
      } else {
        this.router.navigate([PathConstant.SETTING_DOCUMENT_TEMPLATE], {
          state: {...data, ...{rawTemplate: this.documentTemplate}},
          queryParams: {mode: this.mode}
        });
      }
    });
  }

  onSelect($event) {
    if ($event.prop === 'vendorCode') {
      this.changeSignQrOptions($event.data.key);
    }
  }

  getQuestion(key: string) {
    return this.mForm.components.find(q => q.key === key);
  }

  onCancel($event) {
    this.location.back();
  }

  onBase64Data($event) {
    this.documentTemplate = $event;
  }

  onInput($event) {
    // Intentional empty method
  }

  changeSignQrOptions(vendorCode: string) {

    if (Vendor.PRIVY !== vendorCode) {
      const question = this.mForm.components.find(c => c.key === 'useSignQr');
      const options = [
        { key: '1', value: 'Ya' },
        { key: '0', value: 'Tidak' }
      ];

      question.options = options;
      this.useSignQrOptionList = options;
      this.msxForm.get('useSignQr').setValue('0');
      return;
    }

    // Set options to Yes only
    const question = this.mForm.components.find(c => c.key === 'useSignQr');
    const options = [{ key: '1', value: 'Ya' }];
    question.options = options;
    this.useSignQrOptionList = options;
    this.msxForm.get('useSignQr').setValue('1');
    this.cdr.detectChanges();
  }

  onTemplateFile(data) {
    this.documentTemplate = data;
  }

  initView() {
    this.mForm = {
      mode: this.mode,
      name: 'addEditDocumentTemplate',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionTextbox(
          {
            key: 'documentTemplateCode',
            label: 'Document Template Code',
            placeholder: 'Type document template code here',
            readonly: this.mode === CommonConstant.MODE_EDIT,
            maxLength: 20,
            required: true,
            validations: [
              {type: 'required', message: 'Document Template Code harus di isi.'},
              {type: 'maxlength', message: 'Maksimal jumlah karakter Document Template Code adalah 20'}
            ]
        }),
        new QuestionTextbox(
        {
          key: 'documentTemplateName',
          label: 'Document Template Name',
          placeholder: 'Type document template name here',
          maxLength: 100,
          required: true,
          validations: [
            {type: 'required', message: 'Document Template Name harus di isi.'},
            {type: 'maxlength', message: 'Maksimal jumlah karakter Document Template Name adalah 100'}
          ]
        }),
        new QuestionTextbox({
          key: 'documentTemplateDescription',
          label: 'Description',
          placeholder: 'Type description here',
          required: true,
          maxLength: 200,
          validations: [
            {type: 'required', message: 'Description harus di isi.'},
            {type: 'maxlength', message: 'Maksimal jumlah karakter Description adalah 200'}
          ]
        }),
        new QuestionDropdown({
          key: 'paymentSignTypeCode',
          label: 'Payment Sign Type',
          placeholder: 'Select payment sign type',
          serviceUrl: URLConstant.GetPaymentSignType,
          options: [
            { key: '', value: 'Select payment sign type' }
          ],
          args: {
            list: 'paymentSignTypeList',
            key: 'code',
            value: 'description'
          },
          required: true,
          validations: [
            {type: 'required', message: 'Payment Sign Type harus di isi.'}
          ]
        }),
        new QuestionDropdown({
          key: 'vendorCode',
          label: 'PSrE',
          placeholder: 'Select PSrE',
          serviceUrl: URLConstant.GetVendorList,
          options: [
            { key: '', value: 'Select PSrE' }
          ],
          params: {
            vendorTypeCode: CommonConstant.VENDOR_TYPE_PSRE
          },
          args: {
            list: 'vendorList',
            key: 'code',
            value: 'name'
          },
        }),
        new QuestionFile({
          key: 'documentExample',
          label: 'Document',
          placeholder: 'Choose File',
          accept: 'application/pdf',
          required: this.mode === CommonConstant.MODE_ADD,
        }),
        {
          key: 'isSequence',
          label: 'Is it Sequential Signing ?',
          controlType: FormConstant.TYPE_DROPDOWN,
          placeholder: 'Select One',
          required: true,
          options: [
            {key: '1', value: 'Iya'},
            {key: '0', value: 'Tidak'}
          ],
          value: '0'
        },
        new QuestionDropdown({
          key: 'useSignQr',
          label: 'Use QR for Sign Tracking',
          placeholder: 'Use QR for Sign Tracking',
          options: [
            { key: '1', value: 'Ya' },
            { key: '0', value: 'Tidak' }
          ],
          value: '0',
          required: true
        }),
        {
          key: 'isActive',
          label: 'Status',
          controlType: FormConstant.TYPE_DROPDOWN,
          placeholder: 'Select One',
          required: true,
          options: [
            {key: '1', value: 'Active'},
            {key: '0', value: 'Inactive'}
          ],
          value: '1'
        },
        {
          key: 'prioritySequence',
          label: 'Priority Sequence',
          placeholder: '0',
          controlType: FormConstant.TYPE_CURRENCY,
          required: false
        }
      ],
      params: [
      ]
    }
  }
}
