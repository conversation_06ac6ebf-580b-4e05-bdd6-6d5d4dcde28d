import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { PsreSetting } from "app/model/psre-setting";
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { ActionModel } from "app/shared/components/ms-form/models/action.model";
import { Act } from "app/shared/components/msx-datatable/enums/act";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import { PathConstant } from "app/shared/constant/PathConstant";
import { URLConstant } from "app/shared/constant/URLConstant";

@Component({
  selector: "app-list-psre-setting",
  templateUrl: "./list-psre-setting.component.html",
  styleUrls: ["./list-psre-setting.component.scss"],
})
export class ListPsreSettingComponent implements OnInit {
  view: MsxView;
  serviceUrl = URLConstant.GetListPSrESetting;

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.initiateView();
  }

  initiateView() {
    const searchFilter = {
      name: "ListPsreSettingFilter",
      colSize: 6,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        {
          key: "vendorName",
          label: "Vendor Name",
          placeholder: "Type vendor name here",
          controlType: FormConstant.TYPE_TEXT,
        },
        {
          key: "vendorCode",
          label: "Vendor Code",
          placeholder: "Type vendor code here",
          controlType: FormConstant.TYPE_TEXT,
        },
        {
          key: "status",
          label: "Status",
          placeholder: "Pilih Status",
          controlType: FormConstant.TYPE_DROPDOWN,
          options: [
            { key: "", value: "All" },
            { key: "1", value: "Aktif" },
            { key: "0", value: "Tidak Aktif" },
          ],
          value: "",
        },
        {
          key: "statusOperating",
          label: "Operational Status",
          placeholder: "Pilih Status Operasional",
          controlType: FormConstant.TYPE_DROPDOWN,
          options: [
            { key: "", value: "All" },
            { key: "1", value: "Aktif" },
            { key: "0", value: "Tidak Aktif" },
          ],
          value: "",
        },
      ],
      params: [
        {
          key: "page",
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1,
        },
      ],
    };

    const psreSettingTable: Table<PsreSetting> = {
      name: "getListPSrESetting",
      list: [],
      columns: [
        {
          type: ColumnType.Text,
          prop: "vendorName",
          label: "Vendor Name",
          width: 100,
        },
        {
          type: ColumnType.Text,
          prop: "vendorCode",
          label: "Vendor Code",
          width: 100,
        },
        {
          type: ColumnType.IsActive,
          prop: "status",
          label: "Status",
          width: 50,
        },
        {
          type: ColumnType.IsActive,
          prop: "statusOperating",
          label: "Operational Status",
          width: 50,
        },
        {
          type: ColumnType.Text,
          prop: "paymentSignType",
          label: "Payment Sign Type",
          width: 100,
        },
        {
          type: ColumnType.Action,
          label: "Action",
          width: 100,
          action: [
            {
              class: "text-primary",
              icon: "ft-edit",
              type: Act.Edit,
              descr: "Edit",
            },
          ],
        },
      ],
    };

    this.view = {
      title: "List PSrE Setting",
      components: [
        {
          type: WidgetType.SearchFilter,
          component: searchFilter,
        },
        {
          type: WidgetType.Datatable,
          component: psreSettingTable,
        },
      ],
    };
  }

  onItemClick(event: { act: ActionModel; data: any }) {
    switch (event.act.type) {
      case Act.Edit:
        this.router.navigate([PathConstant.EDIT_SETTING_PSRE], {
          state: event.data,
        });
        break;
    }
  }
}
