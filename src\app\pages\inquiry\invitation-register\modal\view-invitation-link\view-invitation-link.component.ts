import {Component, Input, OnInit} from '@angular/core';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-view-invitation-link',
  templateUrl: './view-invitation-link.component.html',
  styleUrls: ['./view-invitation-link.component.scss']
})
export class ViewInvitationLinkComponent implements OnInit {

  @Input()
  link: string;

  constructor(public activeModal: NgbActiveModal) { }

  ngOnInit(): void {
  }

  clipboard(inputElement: HTMLInputElement) {
    inputElement.select();
    document.execCommand('copy');
    inputElement.setSelectionRange(0, 0);
  }
}
