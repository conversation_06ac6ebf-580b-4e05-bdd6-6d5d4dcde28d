import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { PathConstant } from "app/shared/constant/PathConstant";
import { AddTenantComponent } from "./add-tenant/add-tenant.component";
import { ServiceBalanceComponent } from "./service-balance/service-balance.component";
import { TenantComponent } from "./tenant.component";
import { TenantSettingsListComponent } from "./tenant-settings/tenant-settings-list/tenant-settings-list.component";

const routes: Routes = [
    {
      path: '',
      children: [
        {
          path: '',
          component: TenantComponent,
          data: {
            title: 'Tenant'
          }
        },
        {
          path: PathConstant.NEW,
          component: AddTenantComponent,
          data: {
            title: 'Add Tenant',
            mode: 'Add'
          }
        },
        {
          path: PathConstant.EDIT,
          component: AddTenantComponent,
          data: {
            title: 'Edit Tenant',
            mode: 'Edit'
          }
        },
        {
          path: 'serviceBalance',
          component: ServiceBalanceComponent,
          data: {
            title: 'Tenant - Service Balance'
          }
        },
        {
          path: 'settings',
          component: TenantSettingsListComponent,
          data: {
            title: 'Tenant - Settings List'
          }
        }
      ]
    }
  ];
  
  @NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
  })
  export class TenantRoutingModule { }
  