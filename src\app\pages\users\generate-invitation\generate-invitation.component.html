<div class="row" style="margin-top: 15px">
  <div class="col-6">
    <div class="content-header" style="margin-top: 0 !important;" translate>Generate Invitation</div>
  </div>
  <div class="col-6 text-right">
    <button class="btn btn-light mr-2" translate>Kembali</button>
  </div>
</div>

<div class="row">
  <div class="col-6">
    <div class="card">
      <div class="card-content">
        <div class="card-body">
          <form [formGroup]="registerForm" #formElement>
            <div class="form-group">
              <label for="roleCode" class="form-title" translate>Role</label>
              <ng-select formControlName="roleCode" [items]="roleList" bindLabel="description" bindValue="code" [placeholder]="'Select Role' | translate" 
                        [ngClass]="{ 'is-invalid': registerForm.get('roleCode').dirty && rf.roleCode.invalid, 'is-valid': registerForm.get('roleCode').dirty && !rf.roleCode.invalid }"
                        class="form-input" (change)="onChange('roleCode')">
              </ng-select>
              <div *ngFor="let validation of validationMessages.roleCode">
                <div *ngIf="registerForm.get('roleCode').dirty && registerForm.get('roleCode').hasError(validation.type)"
                    class="help-block mt-1 text-danger">
                  <i class="ft-alert-circle align-middle"></i> {{validation.message}}
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-6">
                <div class="form-group">
                  <label for="nik" class="form-title">NIK</label>
                  <input type="text" formControlName="nik" id="nik" class="form-control mb-2" placeholder="NIK"
                         [ngClass]="{ 'is-invalid': registerForm.get('nik').dirty && rf.nik.invalid, 'is-valid': registerForm.get('nik').dirty && !rf.nik.invalid }"
                  >
                  <div *ngFor="let validation of validationMessages.nik">
                    <div *ngIf="registerForm.get('nik').dirty && registerForm.get('nik').hasError(validation.type)"
                         class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                  </div>
                </div>
              </div>

              <div class="col-6">
                <div class="form-group">
                  <label for="name" class="form-title" translate>Full Name</label>
                  <input type="text" formControlName="name" id="name" class="form-control mb-2" placeholder="Nama Lengkap"
                         [ngClass]="{ 'is-invalid': rf.name.invalid && registerForm.get('name').dirty, 'is-valid': !rf.name.invalid && registerForm.get('name').dirty }"
                  >
                  <div *ngFor="let validation of validationMessages.name">
                    <div *ngIf="registerForm.get('name').dirty && registerForm.get('name').hasError(validation.type)"
                         class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-6">
                <div class="form-group">
                  <label for="pob" class="form-title" translate>Place of Birth</label>
                  <input type="text" formControlName="pob" id="pob" class="form-control mb-2" placeholder="Tempat Lahir"
                         [ngClass]="{ 'is-invalid': registerForm.get('pob').dirty && rf.pob.invalid, 'is-valid': registerForm.get('pob').dirty && !rf.pob.invalid }"
                  >
                  <div *ngFor="let validation of validationMessages.pob">
                    <div *ngIf="registerForm.get('pob').dirty && registerForm.get('pob').hasError(validation.type)"
                         class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                  </div>
                </div>
              </div>

              <div class="col-6">
                <div class="form-group">
                  <label for="dob" class="form-title" translate>Date of Birth</label>
                  <input type="date" formControlName="dob" id="dob" class="form-control mb-2" placeholder="Tanggal Lahir"
                         [ngClass]="{ 'is-invalid': registerForm.get('dob').dirty && registerForm.get('dob').invalid, 'is-valid': registerForm.get('dob').dirty && !rf.dob.invalid }">
                  <div *ngFor="let validation of validationMessages.dob">
                    <div *ngIf="registerForm.get('dob').dirty && registerForm.get('dob').hasError(validation.type)"
                         class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="gender" class="form-title" translate>Gender</label>
              <div class="row" id="gender">
                <div class="col-6">
                        <span class="border rounded border-primary d-block">
                          <div class="form-check form-check-inline align-middle p-3">
                            <input class="form-check-input" type="radio" id="female" formControlName="gender" value="F">
                            <label class="form-check-label label-radio-button" for="female" translate>Female</label>
                          </div>
                        </span>
                </div>
                <div class="col-6">
                        <span class="border rounded border-primary d-block">
                          <div class="form-check form-check-inline align-middle p-3">
                            <input class="form-check-input" type="radio" id="male" formControlName="gender" value="M">
                            <label class="form-check-label label-radio-button" for="male" translate>Male</label>
                          </div>
                        </span>
                </div>
              </div>
              <div *ngFor="let validation of validationMessages.gender">
                <div *ngIf="registerForm.get('gender').dirty && registerForm.get('gender').hasError(validation.type)"
                     class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
              </div>
            </div>

            <div class="row">
              <div class="col-6">
                <div class="form-group">
                  <label for="phone" class="form-title" translate>Phone Number</label>
                  <input type="tel" formControlName="phone" id="phone" class="form-control mb-2" placeholder="Contoh : 081234567890"
                         [ngClass]="{ 'is-invalid': registerForm.get('phone').dirty && rf.phone.invalid, 'is-valid': registerForm.get('phone').dirty && !rf.phone.invalid }">
                  <div *ngFor="let validation of validationMessages.phone">
                    <div *ngIf="registerForm.get('phone').dirty && registerForm.get('phone').hasError(validation.type)"
                         class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                  </div>
                </div>
              </div>

              <div class="col-6">
                <div class="form-group">
                  <label for="email" class="form-title">Email</label>
                  <input type="email" formControlName="email" id="email" class="form-control mb-2" placeholder="Email"
                         [ngClass]="{ 'is-invalid': registerForm.get('email').dirty && rf.email.invalid, 'is-valid': registerForm.get('email').dirty && !rf.email.invalid }"
                  >
                  <div *ngFor="let validation of validationMessages.email">
                    <div *ngIf="registerForm.get('email').dirty && registerForm.get('email').hasError(validation.type)"
                         class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="address" class="form-title" translate>Full Address</label>
              <textarea rows="3" formControlName="address" id="address" class="form-control mb-2" placeholder="Tulis alamat lengkap"
                        [ngClass]="{ 'is-invalid': registerForm.get('address').dirty && rf.address.invalid, 'is-valid': registerForm.get('address').dirty && !rf.address.invalid }"
              >
                    </textarea>
              <div *ngFor="let validation of validationMessages.address">
                <div *ngIf="registerForm.get('address').dirty && registerForm.get('address').hasError(validation.type)"
                     class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
              </div>
            </div>

            <div class="row">
              <div class="col-6">
                <div class="form-group">
                  <label for="pob" class="form-title" translate>Province</label>
                  <input type="text" formControlName="province" id="province" class="form-control mb-2" placeholder="Provinsi"
                         [ngClass]="{ 'is-invalid': registerForm.get('province').dirty && rf.province.invalid, 'is-valid': registerForm.get('province').dirty && !rf.province.invalid }"
                  >
                  <div *ngFor="let validation of validationMessages.province">
                    <div *ngIf="registerForm.get('province').dirty && registerForm.get('province').hasError(validation.type)"
                         class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                  </div>
                </div>
              </div>

              <div class="col-6">
                <div class="form-group">
                  <label for="pob" class="form-title" translate>City</label>
                  <input type="text" formControlName="city" id="city" class="form-control mb-2" placeholder="Kota"
                         [ngClass]="{ 'is-invalid': registerForm.get('city').dirty && rf.city.invalid, 'is-valid': registerForm.get('city').dirty && !rf.city.invalid }"
                  >
                  <div *ngFor="let validation of validationMessages.city">
                    <div *ngIf="registerForm.get('city').dirty && registerForm.get('city').hasError(validation.type)"
                         class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-6">
                <div class="form-group">
                  <label for="district" class="form-title" translate>District</label>
                  <input type="text" formControlName="district" id="district" class="form-control mb-2" placeholder="Kecamatan"
                         [ngClass]="{ 'is-invalid': registerForm.get('district').dirty && rf.district.invalid, 'is-valid': registerForm.get('district').dirty && !rf.district.invalid }"
                  >
                  <div *ngFor="let validation of validationMessages.district">
                    <div *ngIf="registerForm.get('district').dirty && registerForm.get('district').hasError(validation.type)"
                         class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                  </div>
                </div>
              </div>

              <div class="col-6">
                <div class="form-group">
                  <label for="subDistrict" class="form-title" translate>Ward</label>
                  <input type="text" formControlName="subDistrict" id="subDistrict" class="form-control mb-2" placeholder="Kelurahan"
                         [ngClass]="{ 'is-invalid': registerForm.get('subDistrict').dirty && rf.district.invalid, 'is-valid': registerForm.get('subDistrict').dirty && !rf.district.invalid }"
                  >
                  <div *ngFor="let validation of validationMessages.subDistrict">
                    <div *ngIf="registerForm.get('subDistrict').dirty && registerForm.get('subDistrict').hasError(validation.type)"
                         class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-6">
                <div class="form-group">
                  <label for="zip" class="form-title" translate>Zip Code</label>
                  <input type="text" formControlName="zip" id="zip" class="form-control mb-2" placeholder="Kode Pos"
                         [ngClass]="{ 'is-invalid': registerForm.get('zip').dirty && rf.zip.invalid, 'is-valid': registerForm.get('zip').dirty && !rf.zip.invalid }"
                  >
                  <div *ngFor="let validation of validationMessages.zip">
                    <div *ngIf="registerForm.get('zip').dirty && registerForm.get('zip').hasError(validation.type)"
                         class="help-block mt-1 text-danger"> <i class="ft-alert-circle align-middle"></i> {{validation.message}}</div>
                  </div>
                </div>
              </div>

              <div class="col-6">
                <div class="form-group">
                  <label for="zip" class="form-title" translate>Business Line</label>
                  <ng-select formControlName="businessLine" [items]="businessLineList" bindLabel="businessLineName" bindValue="businessLineCode" [placeholder]="'Select Business Line' | translate"
                    [(ngModel)]="selectedBusinessLineCode" class="form-input" (change)="onChange('businessLine')">
                  </ng-select>
                </div>
              </div>

            </div>

            <div class="row">

              <div class="col-6">
                <div class="form-group">
                  <label for="zip" class="form-title" translate>Region</label>
                  <ng-select formControlName="region" [items]="regionList" bindLabel="regionName" bindValue="regionCode" [placeholder]="'Select Region' | translate"
                    [(ngModel)]="selectedRegionCode" class="form-input" (change)="onChange('region')">
                  </ng-select>
                </div>
              </div>

              <div class="col-6">
                <div class="form-group">
                  <label for="zip" class="form-title" translate>Office</label>
                  <ng-select formControlName="office" [items]="officeList" bindLabel="officeName" bindValue="officeCode" [placeholder]="'Select Office' | translate"
                    [(ngModel)]="selectedOfficeCode" class="form-input" (change)="onChange('office')">
                  </ng-select>
                </div>
              </div>

            </div>

            <div class="row">
              
              <div class="col-6">
                <div class="form-group">
                  <label for="vendor">PSRE</label>
                  <ng-select [items]="psre" formControlName="vendorCode" id="vendor" bindLabel="name" placeholder="Pilih PSRe" [(ngModel)]="selectedPsre"
                             [ngClass]="{ 'is-invalid': registerForm.get('vendorCode').dirty && rf.vendorCode.invalid, 'is-valid': registerForm.get('vendorCode').dirty && !rf.vendorCode.invalid }">
                  </ng-select>
                </div>
              </div>

              <div class="col-6">
                <div class="form-group">
                  <label for="zip" class="form-title" translate>Nomor Referensi</label>
                  <input type="text" formControlName="refNumber" id="refNumber" class="form-control mb-2" placeholder="Nomor Referensi"
                         [ngClass]="{ 'is-invalid': registerForm.get('refNumber').dirty && rf.refNumber.invalid, 'is-valid': registerForm.get('refNumber').dirty && !rf.refNumber.invalid }">
                </div>
              </div>

            </div>
            <div class="text-right">
              <button class="btn btn-light" style="margin-right: 8px" (click)="onCancel()">Batal</button>
              <button class="btn btn-info" appPreventDoubleClick (throttledClick)="save()" [throttleTime]="1000" translate>Simpan</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

<!--  <div class="col-6" *ngIf="result">-->
<!--    <div class="card">-->
<!--      <div class="card-content">-->
<!--        <div class="card-body">-->
<!--          <p class="text-success">Undangan berhasil di buat, simpan link undangan berikut</p>-->
<!--          <input type="text" [value]="result.links[0]" class="form-control" />-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
</div>
