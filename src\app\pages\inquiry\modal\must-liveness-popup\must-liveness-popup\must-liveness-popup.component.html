<div class="modal-body">
    <p *ngIf="message != null">Foto selfie tidak ditemukan di sistem</p>
    <p *ngIf="message != null">Silahkan mencoba kembali verifikasi dengan OTP</p>
    <p *ngIf="message == null">Percobaan verifikasi wajah sudah melewati batas harian</p>
    <p *ngIf="message == null">Silahkan mencoba kembali lagi besok atau </p>
    <p *ngIf="message == null">lanjutkan verifikasi dengan OTP</p>

    <div class="modal-footer justify-content-center text-center">
      <button class="btn btn-light" (click)="cancel()" translate>Cancel</button>
      <button class="btn btn-success" (click)="goToOTP()" [disabled]="sendMediaForm.invalid" translate>Lanjut dengan OTP</button> 
    </div>

    <form [formGroup]="sendMediaForm">
      <!-- otp media option -->
      <div class="form-group">
          <label for="gender" class="form-title" translate>OTP Delivery Medium<span class="mandatory text-danger">*</span></label>
          <div class="row">
            <div class="col-12" *ngIf="isSms" [ngStyle]="{ 'margin-top.px': '0' }">
              <span class="border rounded border-primary d-block">
                <div class="form-check form-check-inline align-middle p-3">
                  <input class="form-check-input" type="radio" id="sms" formControlName="sendMedia" value="SMS">
                  <label class="form-check-label label-radio-button" for="sms" translate>SMS</label>
                </div>
              </span>
            </div>
            <div class="col-12" *ngIf="isWa" [ngStyle]="{ 'margin-top.px': '10' }">
              <span class="border rounded border-primary d-block">
                <div class="form-check form-check-inline align-middle p-3">
                  <input class="form-check-input" type="radio" id="wa" formControlName="sendMedia" value="WA">
                  <label class="form-check-label label-radio-button" for="wa" translate>WhatsApp</label>
                </div>
              </span>
            </div>
            <div class="col-12" *ngIf="isEmail" [ngStyle]="{ 'margin-top.px': '10' }">
              <span class="border rounded border-primary d-block">
                <div class="form-check form-check-inline align-middle p-3">
                  <input class="form-check-input" type="radio" id="email" formControlName="sendMedia" value="EMAIL">
                  <label class="form-check-label label-radio-button" for="email" translate>Email</label>
                </div>
              </span>
            </div>
          </div>
      </div>
  </form>
</div>

    

