import { Component, OnInit } from '@angular/core';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import {TemplateService} from '../../../../../services/api/template.service';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {AddTemplateRequest} from '../../../../../model/api/add.template.request';
import {ToastrService} from 'ngx-toastr';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-create-document',
  templateUrl: './create-document.component.html',
  styleUrls: ['./create-document.component.scss']
})
export class CreateDocumentComponent implements OnInit {
  templateForm: FormGroup;
  tmpFile: string;
  waiting: boolean;

  constructor(public activeModal: NgbActiveModal, private service: TemplateService,
              private formBuilder: FormBuilder, private toastrService: ToastrService) { }

  ngOnInit(): void {
    this.waiting = false;
    this.templateForm = this.formBuilder.group({
      documentTemplateCode: ['', [Validators.required, Validators.maxLength(10)]],
      documentTemplateName: ['', Validators.required],
      documentTemplateDescription: ['', Validators.required],
      documentExample: ['', Validators.required],
      numOfPage: ['99'],
      isActive: ['1'],
    })
  }

  get f() {
    return this.templateForm.controls;
  }

  onSubmit() {
    if (this.templateForm.invalid) {
      return;
    }

    const formDat = this.templateForm.value;
    const request: AddTemplateRequest = new AddTemplateRequest();
    request.documentTemplateCode = formDat.documentTemplateCode;
    request.documentTemplateName = formDat.documentTemplateName;
    request.documentTemplateDescription = formDat.documentTemplateDescription;
    request.numberOfPage = formDat.numOfPage;
    request.isActive = formDat.isActive;
    request.documentExample = this.tmpFile;

    // send data to api service
    this.waiting = true;
    setTimeout(() => {
      this.service.create(request).subscribe(response => {
        this.waiting = false;
        if (response.status.code !== 0) {
          this.toastrService.error(response.status.message, null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          });
          return;
        }

        this.activeModal.dismiss({action: 'refresh'});
        this.toastrService.info(`Template dokumen ${request.documentTemplateName} berhasil di simpan.`, null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
      });
    }, 1000);
  }

  onInput(file: any) {
    if (!file.target.files) {
      return;
    }

    const reader  = new FileReader();
    reader.onload = (e: any) => {
      this.tmpFile = e.target.result.replace(CommonConstant.DATA_APPLICATION_PDF_BASE64, '');
      console.log('File Data', this.tmpFile);
    };

    reader.readAsDataURL(file.target.files[0]);
  }

}
