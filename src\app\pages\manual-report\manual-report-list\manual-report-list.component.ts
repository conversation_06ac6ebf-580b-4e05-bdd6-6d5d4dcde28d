import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { DownloadReportRequest } from 'app/model/api/download.report.request';
import { ListReportDownload } from 'app/model/list-report-download';
import { ManualReport } from 'app/model/manual-report';
import { ManualReportService } from 'app/services/api/manual-report.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDate, QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Router } from '@angular/router';
import { DeleteReportRequest } from 'app/model/api/delete-report.request';

@Component({
  selector: 'app-manual-report-list',
  templateUrl: './manual-report-list.component.html',
  styleUrls: ['./manual-report-list.component.scss']
})
export class ManualReportListComponent implements OnInit {

  view: MsxView;
  searchFormObj: FormModel<any> = new FormModel<any>();
  form: FormGroup;
  serviceUrl= URLConstant.getManualReportList;
  swal = swalFunction;
  buttonList: Button[];

  constructor(private global: GlobalService, private manualReportService: ManualReportService, private router: Router) { }

  ngOnInit(): void {
    this.initView();
    this.buttonList = [
      {name: 'New', class: 'btn btn-primary', hide: false, icon: 'ft-plus'}
    ]
  }

  initView() {
    this.searchFormObj = {
      name: 'SearchForm',
      colSize: 6,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      autoload: true,
      components: [
        new QuestionDropdown({
          key: 'tenantCode',
          label: 'Tenant',
          placeholder: 'Select Tenant',
          serviceUrl: URLConstant.GetTenantList,
          value: '',
          params: {
            status: '1'
          },
          options: [
            { key: '', value: CommonConstant.CONST_SELECT_TENANT}
          ],
          args: {
            list: 'tenantList',
            key: 'tenantCode',
            value: 'tenantName'
          }
        }),
        new QuestionDropdown({
          key: 'reportType',
          label: 'Report Type',
          placeholder: 'Select Report Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'REPORT_TYPE'
          }
        }),
        new QuestionDate({
          key: 'reportDateStart',
          label: 'Report Date From',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDate({
          key: 'reportDateEnd',
          label: 'Report Date To',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        {
          key: 'fileName',
          label: 'File Name',
          placeholder: 'Type file name here',
          controlType: FormConstant.TYPE_TEXT
        }
      ],
      params: [
        {
          key: 'audit',
          controlType: FormConstant.TYPE_HIDDEN,
          value: {
            callerId: this.global.user.loginId
          }
        },
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        }
      ]
    }

    const ManualReportTable: Table<ManualReport> = {
      name: 'reports',
      list: [],
      columns: [
        {
          type: ColumnType.Number,
          prop: 'no',
          label: 'No.',
          width: 30
        },
        {
          type: ColumnType.Text,
          prop: 'tenantName',
          label: 'Tenant Name',
          width: 50
        },
        {
          type: ColumnType.Text,
          prop: 'reportType',
          label: 'Report Type',
          width: 50
        },
        {
          type: ColumnType.Text,
          prop: 'fileName',
          label: 'File Name',
          width: 50
        },
        {
          type: ColumnType.Text,
          prop: 'reportDate',
          label: 'Report Date',
          width: 50
        },
        {
          type: ColumnType.Action,
          label: 'Action',
          width: 85,
          action: [
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: CommonConstant.ICON_FT_DOWNLOAD,
              type: Act.Download,
              descr: 'Download'
            },
            {
              class: CommonConstant.TEXT_DANGER,
              icon: 'ft-trash-2',
              descr: 'Delete',
              type: Act.Delete
            }
          ]
        }
      ]
    };

    this.view = {
      title: 'Manual Report',
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: ManualReportTable
        }
      ]
    }
  }

  onItemClickListener(event) {
    const data = event['data'];

    switch(event.act.type) {
      case Act.Download:
        return this.gotoDownload(data, true);
      case Act.Delete:
        return this.deleteReport(data);
    }
  }

  gotoDownload(data: ManualReport, download: boolean) {
    console.log('Data', data);
    const request: DownloadReportRequest = new DownloadReportRequest();
    request.idManualReport = data.manualReportId;

    this.manualReportService.downloadReport(request).toPromise().then(response => {
      if (response.status.code === 0) {
          const downloadLink = document.createElement('a');
          const fileName = data.fileName;

          downloadLink.href = `data:application/xlsx;base64, ${response.xlBase64}`;
          downloadLink.download = fileName;
          downloadLink.click();
        }
      }
    );
  }

  deleteReport(data: ManualReport) {
    const confirmationText = 'Apakah Anda yakin ingin menghapus Report dengan file ' + data.fileName + '?';
    this.swal.Confirm(confirmationText).then(
      (result) => {
        if (result.isConfirmed === true) {
          const request: DeleteReportRequest = new DeleteReportRequest()
          request.tenantCode = data.tenantCode;
          request.fileName = data.fileName;
          this.manualReportService.deleteReport(request).toPromise().then(response => {
            if (response.status.code === 0) {
              this.swal.Success('Report berhasil dihapus');
              window.location.reload();
            } else {
              this.swal.Error(response.status.message);
            }
          });
        }
      }
    );
  }

  onBtnClickListener(event) {
    switch (event['name']) {
      case this.buttonList[0].name:
        this.router.navigate([PathConstant.UPLOAD_MANUAL_REPORT]);
        break;
      default:
        break;
    }
  }
}
