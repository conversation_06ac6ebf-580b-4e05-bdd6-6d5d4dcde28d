import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { BalanceService } from 'app/services/api/balance.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { AddBalanceRequest } from 'app/shared/dto/balance/add-balance.request';
import { TopupBalanceRequest } from 'app/shared/dto/balance/topup-balance.request';
import { DeviceDetectorService } from 'ngx-device-detector';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-add-balance',
  templateUrl: './add-balance.component.html',
  styleUrls: ['./add-balance.component.scss']
})
export class AddBalanceComponent implements OnInit {

  addForm: FormModel<any>;
  xForm: FormGroup;
  col = 6;

  constructor(private deviceService: DeviceDetectorService, 
    private toastService: ToastrService, private balanceService: BalanceService,
    private router: Router) {
    if (deviceService.isMobile()) {
      this.col = 12;
    }
  }

  ngOnInit(): void {
    this.addForm = {
      name: 'addBalance',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        {
          key: 'balanceTypeCode',
          label: 'Balance Type Code',
          placeholder: 'Type balance type code here',
          controlType: FormConstant.TYPE_TEXT,
          maxLength: 80,
          required: true
        },
        {
          key: 'balanceTypeName',
          label: 'Balance Type Name',
          placeholder: 'Type balance type name here',
          controlType: FormConstant.TYPE_TEXT,
          maxLength: 200,
          required: true
        }
      ],
      params: [
      ]
    }
  }

  onForm(form: FormGroup) {
    this.xForm = form;
  }

  onNext(data: any) {
    if (!data['balanceTypeCode']) {
      return;
    }
    if (!data['balanceTypeName']) {
      return;
    }

    const request = new AddBalanceRequest();
    request.balanceTypeCode = data['balanceTypeCode'];
    request.balanceTypeName = data['balanceTypeName'];
    this.balanceService.addBalanceType(request).subscribe(
      (response) => {
        if (response.status.code === 0) {
          this.toastService.success('Add balance success', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          });
          setTimeout(() => {
            window.location.reload();
          }, 750);
        }
      }
    )
  }

}
