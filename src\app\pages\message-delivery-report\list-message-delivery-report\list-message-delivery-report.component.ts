import { Component, OnInit } from '@angular/core';
import { MessageDeliveryReport } from 'app/model/message-delivery-report';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { QuestionDate, QuestionDropdown, QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-list-message-delivery-report',
  templateUrl: './list-message-delivery-report.component.html',
  styleUrls: ['./list-message-delivery-report.component.scss']
})
export class ListMessageDeliveryReportComponent implements OnInit {
  view: MsxView;
  serviceUrl = URLConstant.listMessageDelivery;

  constructor() { }

  ngOnInit(): void {
    this.initiateView();
  }

  initiateView() {
    const searchFilter = {
      name: "ListMessageDeliveryReportFilter",
      colSize: 6,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        new QuestionDropdown({
          key: "vendorCode",
          label: "Vendor",
          placeholder: "Select Vendor",
          serviceUrl: URLConstant.GetVendorList,
          options: [
            { key: '', value: 'All'}
          ],
          params: {
            vendorTypeCode: CommonConstant.VENDOR_TYPE_MESSAGE_GATEWAY
          },
          args: {
            list: 'vendorList',
            key: 'code',
            value: 'name'
          },
          value: ''
        }),
        new QuestionDropdown({
          key: "messageMedia",
          label: "Message Media",
          placeholder: "Select Message Media",
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All'}
          ],
          params: {
            lovGroup: 'MESSAGE_MEDIA'
          },
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          value: ''
        }),
        new QuestionDate({
          key: 'reportTimeStart',
          label: 'Report Time Start',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDate({
          key: 'reportTimeEnd',
          label: 'Report Time End',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDate({
          key: 'requestTimeStart',
          label: 'Request Message Time Start',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDate({
          key: 'requestTimeEnd',
          label: 'Request Message Time End',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDropdown({
          key: 'deliveryStatus',
          label: 'Delivery Status',
          placeholder: 'Select Delivery Status',
          options: [
              {key: '', value: 'All'},
              {key: '0', value: 'Not Started'},
              {key: '1', value: 'Waiting'},
              {key: '2', value: 'Failed'},
              {key: '3', value: 'Delivered'},
              {key: '4', value: 'Read'}
          ],
          value: ''
        }),
        new QuestionTextbox({
          key: 'recipient',
          label: 'Recipient',
          placeholder: 'Type recipient here'
        })
      ],
      params: [
        {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
        }
      ]
    };

    const messageDeliveryReportTable: Table<MessageDeliveryReport> = {
      name: "listMessageDeliveryReport",
      list: [],
      columns: [
        {
          type: ColumnType.Text,
          prop: "vendorName",
          label: "Vendor",
          width: 100,
        },
        {
          type: ColumnType.Date,
          format: CommonConstant.FORMAT_DATE_WITHTIME_SECOND,
          prop: 'requestTime',
          label: "Request Message Time",
          width: 150
        },
        {
          type: ColumnType.Date,
          format: CommonConstant.FORMAT_DATE_WITHTIME_SECOND,
          prop: 'reportTime',
          label: 'Report Time',
          width: 150
        },
        {
          type: ColumnType.Text,
          prop: "recipient",
          label: "Recipient",
          width: 100,
        },
        {
          type: ColumnType.Text,
          prop: 'trxNo',
          label: 'Trx No',
          width: 70
        },
        {
          type: ColumnType.Text,
          prop: "messageMedia",
          label: "Message Media",
          width: 100,
        },
        {
          type: ColumnType.Text,
          prop: "deliveryStatusInformation",
          label: "Delivery Status",
          width: 100,
        }
      ]
    };

    this.view = {
      title: "Message Delivery Report",
      components: [
        {
          type: WidgetType.SearchFilter,
          component: searchFilter,
        },
        {
          type: WidgetType.Datatable,
          component: messageDeliveryReportTable,
        },
      ],
    };
  }

}
