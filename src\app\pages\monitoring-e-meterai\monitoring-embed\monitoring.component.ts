import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { MonitoringService } from 'app/services/api/monitoring.service';
import { QuestionDate } from 'app/shared/components/ms-form/questions/question-date';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { UserProfile } from 'app/model/user-profile';
import { GlobalService } from 'app/shared/data/global.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDropdown, QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Monitoring } from 'app/model/monitoring';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { StampdutyErrorComponent } from './stampduty-error/stampduty-error.component';
import { RetryStampingRequest } from 'app/model/api/retry-stamping.request';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { DownloadExcelTaxReportRequest } from 'app/shared/dto/Monitoring-e-materai/download-excel-tax-report.request';
import { saveAs } from 'file-saver';
import { RetryLatestStampingFromUploadEmbedRequest } from 'app/shared/dto/Monitoring-e-materai/retry-stamping-from-upload-embed.request';
import { ConfirmationModalComponent } from 'app/pages/users/inquiry-user/modal/confirmation-modal/confirmation-modal.component';
import { ViewDocumentStampedRequest } from 'app/model/api/view.document.stamped.request';
import { ViewDocumentStampedResponse } from 'app/model/api/view.document.stamped.response';
import { HttpClient } from '@angular/common/http';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { DeviceDetectorService } from 'ngx-device-detector';

@Component({
  selector: 'app-monitoring',
  templateUrl: './monitoring.component.html',
  styleUrls: ['./monitoring.component.scss']
})

export class MonitoringComponent implements OnInit {
  view: MsxView;
  routeData: any;
  serviceUrl = URLConstant.ListMonitoringEmbed;
  public params;
  private pathSrc: string;
  msg: string;
  isReload = false;
  static get: string;
  swal = swalFunction;

  @Input()
  question: QuestionDate;
  searchFormObj: FormModel<any> = new FormModel<any>();

  constructor(private activatedRoute: ActivatedRoute, private router: Router, private modalService: NgbModal, 
    private global: GlobalService, private monitoringService: MonitoringService, private http: HttpClient, private modal: NgbModal) {
    this.routeData = this.activatedRoute.snapshot.data;

    const snapshot = this.activatedRoute.snapshot.data;
    this.pathSrc = snapshot.path;
    this.activatedRoute.queryParams.subscribe(params => {
      if (params['msg']) {
        this.msg = params['msg'];
      }
    }); 
  }


  ngOnInit(): void {
    localStorage.clear();
    this.initView();
    
    localStorage.removeItem('msg');
    if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      localStorage.removeItem('token');
      localStorage.clear();
    }

    if (this.isReload) {
      this.isReload = null;
      window.top.location.href = window.location.pathname;
    }

    if (this.msg) {
      const user: UserProfile = new UserProfile();
      user.pathSrc = this.pathSrc;
      user.loginId = 'CONFINS';
      user.role = {
        tenantCode: '',
        tenantName: '',
        roleCode: '',
        roleName: ''
      };
      user.roles = [{
        tenantCode: '',
        tenantName: '',
        roleCode: '',
        roleName: ''
      }];
      this.global.msg = this.msg;
      this.global.user = user;
    }
  }
  


  private dateToString(dateObject: any) {
    return (dateObject['year']) ? dateObject['year'] + '-' + dateObject['month'] + '-' + dateObject['day'] : dateObject;
  }

  initView() {
    this.searchFormObj = {
     name: 'MonitoringSearchForm',
     direction: FormConstant.DIRECTION_HORIZONTAL,
     colSize: 6,
     exportExcel: true,
     exportExcelLabel: 'Export Tax Excel',
     components: [
      new QuestionTextbox({
        key: 'nomorDokumen',
        label: CommonConstant.LABEL_DOCUMENT_NUMBER,
        placeholder: 'Type nomor document here',
        value: ''
      }),
      new QuestionDropdown({
          key: 'jenisDokumen',
          label: 'Document Peruri Type',
          placeholder: 'Select document kind',
          serviceUrl: URLConstant.getListDocumentEMateraiType,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'documentEMateraiList',
            key: 'documentName',
            value: 'documentName'
          },
           params: {
            msg: this.msg
           }
      }),
      new QuestionDropdown({
          key: 'tipeDokumen',
          label: 'Document Type',
          placeholder: 'Select Document Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'description',
            value: 'description'
          },
          params: {
            lovGroup: 'DOC_TYPE'
          }
      }),
      new QuestionDropdown({
          key: 'templateDokumen',
          label: 'Document template',
          placeholder: 'Select document template',
          serviceUrl: URLConstant.getListDocumentTemplateEmbed,
          options: [
            { key: '', value: 'All' },
            { key: 'MANUAL', value: 'MANUAL'}
          ],
          value: '',
          args: {
            list: 'listDocumentTemplate',
            key: 'documentTemplateName',
            value: 'documentTemplateName'
          },
          params: {
            msg: this.msg
            }
      }),
      new QuestionDate({
          key: 'tanggalDokumenMulai',
          label: 'Document Date From',
          placeholder: CommonConstant.FORMAT_DATE,
          isDateStart: true
        }),
      new QuestionDate({
          key: 'tanggalDokumenSampai',
          label: 'Document Date To',
          placeholder: CommonConstant.FORMAT_DATE,
          isDateEnd: true
        }),
      new QuestionDropdown({
        key: 'hasilStamping',
        label: 'Stamping Result',
        placeholder: 'Select stamping result',
        options: [
          {key: '', value: 'All'},
          {key: 'Not Started', value: 'Not Started'},
          {key: 'Failed', value: 'Failed'},
          {key: 'In Progress', value: 'In Progress'},
          {key: 'Success', value: 'Success'},
        ],
      }),
      new QuestionTextbox({
          key: 'noSN',
          label: 'Serial Number',
          placeholder: 'Type serial number here',
          value: ''
        }),
      ],
      params: [
        {
          key: 'msg',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.msg
        },
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        }
      ]
    }

    const MonitoringTable: Table<Monitoring> = {
      name: 'listMonitoring',
      list: [],
      columns: [
          {
              type: ColumnType.Text,
              prop: 'nomorDokumen',
              label: CommonConstant.LABEL_DOCUMENT_NUMBER,
              width: 80
          },
          {
              type: ColumnType.Date,
              format: 'DD-MMM-YYYY',
              prop: 'tanggalDokumen',
              label: 'Document Date',
              width: 70
          },
          {
              type: ColumnType.Text,
              prop: 'namaDokumen',
              label: 'Document Name',
              width: 80
          },
          {
              type: ColumnType.Text,
              prop: 'jenisDokumen',
              label: 'Document Peruri Type',
              width: 80
          },
          {
              type: ColumnType.Text,
              prop: 'tipeDokumen',
              label: 'Document Type',
              width: 80
          },
          {
              type: ColumnType.Currency,
              prop: 'nominalDokumen',
              label: 'Document Nominal',
              width: 80
          },
          {
              type: ColumnType.Text,
              prop: 'templateDokumen',
              label: 'Document template',
              width: 80
          },
          {
              type: ColumnType.Text,
              prop: 'hasilStamping',
              label: 'Stamping Result',
              width: 80
          },
          {
              type: ColumnType.Text,
              prop: 'noSN',
              label: 'Serial Number',
              width: 80
          },
          {
              type: ColumnType.Text,
              prop: 'prosesMaterai',
              label: 'Materai Process',
              width: 80
          },
          {
              type: ColumnType.Text,
              prop: 'noIdentitas',
              label: 'Identity Number',
              width: 80
          },
          {
              type: ColumnType.Text,
              prop: 'namaIdentitas',
              label: 'Identity Name',
              width: 80
          },
          {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-eye',
                descr: 'View Error Message',
                type: Act.View,
                condition: true,
                conditionVariable: 'errorMessage',
                conditionExpected: '',
                conditionedClass: 'd-none'
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-play-circle',
                descr: 'Retry Stamping',
                type: Act.Resend,
                condition: true,
                conditionVariable: 'errorMessage',
                conditionExpected: '',
                conditionedClass: 'd-none'
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-rotate-cw',
                descr: 'Retry Stamping From Upload',
                type: Act.Resend,
                condition: true,
                conditionVariable: 'errorMessage',
                conditionExpected: '',
                conditionedClass: 'd-none'
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-eye',
                descr: 'View',
                type: Act.View,
                condition: true,
                conditionVariable: 'isSuccess',
                conditionExpected: '0',
                conditionedClass: 'd-none'
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: CommonConstant.ICON_FT_DOWNLOAD,
                descr: 'Download',
                type: Act.Download,
                condition: true,
                conditionVariable: 'isSuccess',
                conditionExpected: '0',
                conditionedClass: 'd-none'
              }
            ]
        }
      ]
    };
    
    this.view = {
      title: 'List Monitoring E-Meterai',
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: MonitoringTable
        }
      ]
    }
  }

  onItemClickListener(result: any) {
    const data = result['data'];

    switch (result['act']['descr']) {
      case 'View Error Message':
       return this.openErrorMessageModal(data);
      case 'Retry Stamping':
        return this.retryStamping(data);
      case 'Retry Stamping From Upload':
        return this.retryStampingFromUpload(data);
      case 'Download':
        return this.gotoViewOrDownload(data, true);
      case 'View':
        return this.gotoViewOrDownload(data, false);
    }
  }

  gotoViewOrDownload(data: Monitoring, download: boolean) {
    console.log('Data', data);
    const request: ViewDocumentStampedRequest = new ViewDocumentStampedRequest();
    request.documentId = data.encryptedDocId;
    if (this.msg && this.router.url.includes('/embed/')) {
      request.msg = this.msg;
    }

    const url = (this.msg) ? URLConstant.ViewDocumentStampedEmbed : URLConstant.ViewDocumentStamped;
    this.http.post<ViewDocumentStampedResponse>(url, request).subscribe(
      (response) => {
        if (response.status.code === 0) {
          if (download) {
            const downloadLink = document.createElement('a');
            const fileName = data.templateDokumen + '.pdf';

            downloadLink.href = `data:application/pdf;base64,${response.file}`;
            downloadLink.download = fileName;
            downloadLink.click();
          } else {
            const extras = { pdfBase64: response.file, refNumber: data.nomorDokumen };
            data = { ...data, ...extras };

            if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
              this.router.navigate([PathConstant.EMBED_VIEW_DOCUMENT_INQUIRY], { state: data });
            } 
            else {
              this.router.navigate([PathConstant.VIEW_DOCUMENT_INQUIRY], { state: data });
            }
          }
        }
      }
    );
  }

  openErrorMessageModal(data) {
    const modal = this.modalService.open(StampdutyErrorComponent, { backdrop: 'static', keyboard: false, size: 'l' });
        modal.componentInstance.errorMessage = data['errorMessage'];
  }

  retryStamping(data :Monitoring) {
    
    this.swal.Confirm('Apakah Anda yakin ingin melakukan proses retry stamping?').then(
      (result) => {
        if (!result.isConfirmed) {
          return;
        }

        const request: RetryStampingRequest = new RetryStampingRequest();
        request.msg = this.msg;
        request.refNumber = data.nomorDokumenEncrypt;
        request.no = data.noSNEncrypt;

        this.monitoringService.getRetryStampingEmbed(request).subscribe(
          (response) => {
            if (response.status.code === 0) {
              this.swal.Success(response.message);
            }
            else {
              return Error(response.message);
            }
          }
        )
      }
    )
  }

  retryStampingFromUpload(data: any) {

    this.swal.Confirm('Apakah Anda yakin ingin memproses stamping dengan upload ulang?').then(
      (result) => {
        if (!result.isConfirmed) {
          return;
        }

        const request = new RetryLatestStampingFromUploadEmbedRequest();
        request.msg = this.msg;
        request.encryptedRefNumber = data['nomorDokumenEncrypt'];
        
        this.monitoringService.retryLatestStampFromUploadEmbed(request).subscribe(
          (response) => {
            if (response.status.code === 0) {
              this.swal.Success(response.status.message);
            }
          }
        )
      }
    )
  }

  downloadExcelTaxReport(params) {
    console.log('Download tax report', params);
    const request = new DownloadExcelTaxReportRequest();
    request.nomorDokumen = params['nomorDokumen'];
    request.tipeDokumen = params['tipeDokumen'];
    request.tanggalDokumenMulai = this.dateToString(params['tanggalDokumenMulai']);
    request.tanggalDokumenSampai = this.dateToString(params['tanggalDokumenSampai']);
    request.jenisDokumen = params['jenisDokumen'];
    request.templateDokumen = params['templateDokumen'];
    request.hasilStamping = params['hasilStamping'];
    request.noSN = params['noSN'];
    request.msg = params['msg'];

    this.monitoringService.downloadExcelTaxReport(request).subscribe(
      response => {
        if (response.status.code === 0) {
          const blob = this.b64toBlob(response.base64ExcelReport);
          saveAs(blob, response.filename);
        }
      }
    )
  }

  b64toBlob(b64Data, contentType = '', sliceSize = 512) {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }
  
}


