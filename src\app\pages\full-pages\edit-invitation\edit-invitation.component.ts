import { Component, OnInit } from '@angular/core';
import {Location} from '@angular/common';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {InquiryInvitation} from '../../../model/inquiry-invitation';
import * as moment from 'moment';
import {Error, Success} from '../../../shared/data/sweet-alerts';
import {UpdateInvitationRequest} from '../../../model/api/update.invitation.request';
import {InvitationService} from '../../../services/api/invitation.service';
import {CommonConstant} from '../../../shared/constant/common.constant';
import { invitationBy } from 'app/model/invitationBy';
import { IpService } from 'app/services/api/ip.service';

@Component({
  selector: 'app-edit-invitation',
  templateUrl: './edit-invitation.component.html',
  styleUrls: ['./edit-invitation.component.scss']
})
export class EditInvitationComponent implements OnInit {

  registerForm: FormGroup;
  fields: any;
  validationMessages: any;
  template: any;
  state: InquiryInvitation;
  selectedItem: any = '';
  InviteBy: invitationBy[];
  ipAddress: string;

  constructor(private formBuilder: FormBuilder, private location: Location, private router: Router,
              private readonly service: InvitationService, private ipService: IpService) {
    this.state = this.router.getCurrentNavigation().extras.state['param'];
    console.log('state', this.state);
  }

  async ngOnInit() {
    this.initView();
    await this.getIdAddress();
  }

  async getIdAddress(){
    await this.ipService.getIpAddress().toPromise().then(ipResponse =>{
      this.ipAddress = ipResponse.ip;
    })
  }

  save() {
    const formData = this.registerForm.getRawValue();
    console.log('formData', formData);
    if (this.selectedItem === CommonConstant.SRC_SMS && !this.checkPhoneNumber(formData['receiver'], formData['phone'])) {
      return Error('Nomor Handphone tidak sama dengan Penerima Undangan, Silahkan cek kembali.');
    }

    // Prepare data for updates
    const request: UpdateInvitationRequest = new UpdateInvitationRequest();
    request.oldRecieverDetail = this.state.recieverDetail;
    request.invitationBy = this.selectedItem;
    request.address = formData['address'];
    request.dateOfBirth = formData['dob'];
    request.email = formData['email'];
    request.fullName = formData['name'];
    request.gender = formData['gender'];
    request.idNo = formData['nik'];
    request.kecamatan = formData['district'];
    request.kelurahan = formData['subDistrict'];
    request.kota = formData['city'];
    request.phone = formData['phone'];
    request.placeOfBirth = formData['pob'];
    request.provinsi = formData['province'];
    request.receiverDetail = formData['receiver'];
    request.zipCode = formData['zip'];
    request.vendorCode = formData['vendorCode'];
    request.ipAddress = this.ipAddress;

    console.log('Request', request);
    this.service.updateInvitationData(request).subscribe(res => {
      if (res.status.code === 0) {
        return Success(res.status.message).then(() => {
          this.doBack();
        });
      } else {
        return Error(res.status.message);
      }
    })
  }

  checkPhoneNumber(phone1: string, phone2: string) {
    return phone1 === phone2;
  }

  doBack() {
    this.location.back();
  }

  initView() {
    this.setProperties();
    this.registerForm = this.formBuilder.group({
      invBy: [this.state.invBy],
      receiver: [this.state.recieverDetail, Validators.required],
      name: [this.state.nama, Validators.compose([Validators.maxLength(50)])],
      nik: [this.state.idKtp, Validators.compose([Validators.maxLength(16), Validators.pattern('\\d*')])],
      pob: [this.state.tmpLahir, Validators.compose([Validators.maxLength(50)])],
      dob: [this.parseDate(this.state.tglLahir), Validators.compose([Validators.maxLength(10)])],
      gender: [this.state.jenisKelamin],
      phone: [this.state.tlp, Validators.compose([Validators.required, Validators.pattern('^\\d+$'), Validators.maxLength(15)])],
      address: [this.state.alamat],
      subDistrict: [this.state.kelurahan, Validators.compose([Validators.maxLength(50)])],
      district: [this.state.kecamatan, Validators.compose([Validators.maxLength(50)])],
      city: [this.state.kota, Validators.compose([Validators.maxLength(50)])],
      province: [this.state.provinsi, Validators.compose([Validators.maxLength(50)])],
      zip: [this.state.kodePos, Validators.compose([Validators.maxLength(5), Validators.pattern('\\d*')])],
      email: [this.state.email, Validators.compose([
        Validators.pattern('[\\w\\-\\._]+@[\\w\\-\\._]+\\.\\w{2,10}'),
        Validators.maxLength(64)]
      )],
      vendorCode: [this.state.vendorCode],
      vendorName: [this.state.vendorName]
    });

    //Invitation By
    this.selectedItem = this.state.invBy;
    this.InviteBy = [
      { invBy: 'Email'},
      { invBy: 'SMS'},
    ];
    // Check input fields
    this.checkData();
  }

  onChange($event) {
    this.selectedItem = $event.invBy;
    if($event.invBy === CommonConstant.SRC_SMS){
      this.registerForm.get('email').disable();
      this.registerForm.get('phone').disable();
      this.registerForm.patchValue({
        'receiver': this.registerForm.get('phone').value,
        'email': null
      });
    } else if($event.invBy === 'Email'){
      this.registerForm.get('phone').enable();
      this.registerForm.patchValue({
        'email': this.state.email,
      });
      this.registerForm.patchValue({
        'receiver': this.registerForm.get('email').value,
      });
    }
  }
  updatePhone($event) {
    console.log('on blur', $event);
    if (this.selectedItem === CommonConstant.SRC_SMS) {
      this.registerForm.patchValue({
        'phone': this.registerForm.get('receiver').value
      });
    } else {
      this.registerForm.patchValue({
        'email': this.registerForm.get('receiver').value
      });
      console.log('email', this.registerForm.get('email').value);
    }
  }

  checkData() {
    if (this.state.isRegistered === '1') {
      this.registerForm.get('name').disable();
      this.registerForm.get('nik').disable();
      this.registerForm.get('pob').disable();
      this.registerForm.get('dob').disable();
      this.registerForm.get('gender').disable();
      this.registerForm.get('address').disable();
      this.registerForm.get('subDistrict').disable();
      this.registerForm.get('district').disable();
      this.registerForm.get('city').disable();
      this.registerForm.get('province').disable();
      this.registerForm.get('zip').disable();
      this.registerForm.get('email').disable();
      // this.registerForm.get('receiver').disable();
      this.registerForm.get('invBy').disable();
    }

    if (this.selectedItem === CommonConstant.SRC_SMS) {
      this.registerForm.get('phone').setValue(this.state.recieverDetail);
      this.registerForm.get('phone').setValidators([Validators.required, Validators.maxLength(15)]);
      this.registerForm.get('phone').updateValueAndValidity();
      this.registerForm.get('phone').disable();
      this.registerForm.get('email').disable();
      
    }

    if (this.selectedItem === 'Email') {
      this.registerForm.get('email').setValue(this.state.recieverDetail);
      this.registerForm.get('email').setValidators([Validators.required, Validators.pattern('[\\w\\-\\._]+@[\\w\\-\\._]+\\.\\w{2,10}'), Validators.maxLength(64)]);
      this.registerForm.get('email').updateValueAndValidity();
    }
  }

  get rf() {
    return this.registerForm.controls;
  }

  private setProperties() {
    // Message Template
    this.template = {
      required: '{x} harus diisi',
      maxlength: 'Maksimal jumlah karakter {x} adalah {v}',
      minlength: 'Minimal jumlah karakter {x} adalah {v}',
      pattern: 'Silahkan isi {x} dengan format yang benar.',
    };

    this.fields = {
      name: {
        label: 'Nama Lengkap',
        prop: {
          maxlength: 50
        }
      },
      nik: {
        label: 'NIK',
        prop: {
          maxlength: 16
        }
      },
      pob: {
        label: 'Tempat Lahir',
        prop: {
          maxlength: 50
        }
      },
      dob: {
        label: 'Tanggal Lahir'
      },
      gender: {
        label: 'Jenis Kelamin'
      },
      phone: {
        label: 'No. Telp',
        prop: {
          maxlength: 15
        }
      },
      address: {
        label: 'Alamat',
        prop: {
          maxlength: 150
        }
      },
      subDistrict: {
        label: 'Kelurahan',
        prop: {
          maxlength: 50
        }
      },
      district: {
        label: 'Kecamatan',
        prop: {
          maxlength: 50
        }
      },
      city: {
        label: 'Kota',
        prop: {
          maxlength: 50
        }
      },
      province: {
        label: 'Provinsi',
        prop: {
          maxlength: 50
        }
      },
      zip: {
        label: 'Kode Pos',
        prop: {
          maxlength: 5
        }
      },
      fotoSelfie: {
        label: 'Foto Diri'
      },
      fotoKtp: {
        label: 'Foto KTP'
      },
      email: {
        label: 'Email',
        prop: {
          maxlength: 64
        }
      },
    };

    // Validation Custom Error Messages
    this.validationMessages = {
      name: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.name.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.name.label)
            .replace('{v}', this.fields.name.prop.maxlength)
        },
      ],
      nik: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.nik.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.nik.label)
            .replace('{v}', this.fields.nik.prop.maxlength)
        },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.nik.label) },
      ],
      pob: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.pob.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.pob.label)
            .replace('{v}', this.fields.pob.prop.maxlength)
        },
      ],
      dob: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.dob.label) }
      ],
      gender: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.gender.label) }
      ],
      phone: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.phone.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.phone.label)
            .replace('{v}', this.fields.phone.prop.maxlength)
        },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.phone.label) },
      ],
      address: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.address.label) }
      ],
      subDistrict: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.subDistrict.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.subDistrict.label)
            .replace('{v}', this.fields.subDistrict.prop.maxlength)
        },
      ],
      district: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.district.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.district.label)
            .replace('{v}', this.fields.district.prop.maxlength)
        },
      ],
      city: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.city.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.city.label)
            .replace('{v}', this.fields.city.prop.maxlength)
        },
      ],
      province: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.province.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.province.label)
            .replace('{v}', this.fields.province.prop.maxlength)
        },
      ],
      zip: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.zip.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.zip.label)
            .replace('{v}', this.fields.zip.prop.maxlength)
        },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.zip.label) },
      ],
      psre: [
        { type: 'required', message: this.template.required.replace('{x}', 'PSRe') }
      ],
      fotoSelfie: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.fotoSelfie.label) }
      ],
      fotoKtp: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.fotoKtp.label) }
      ],
      email: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.email.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.email.label)
            .replace('{v}', this.fields.email.prop.maxlength)
        },
      ],
    };
  }

  parseDate(strDate: any) {
    // previous code -> moment(strDate, 'DD/MM/YYYY').format('YYYY-MM-DD');
    if (!strDate) {
      return null;
    }
    const date = moment(strDate).format('YYYY-MM-DD');
    console.log('Formatted', date);
    return date;
  }

}
