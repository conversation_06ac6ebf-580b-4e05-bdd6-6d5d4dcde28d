import {Component, OnInit, ViewEncapsulation} from '@angular/core';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {TemplateService} from '../../../services/api/template.service';
import {Router} from '@angular/router';
import {MsxView} from '../../../shared/components/msx-view/models/MsxView';
import {DocumentTemplateListView} from './view/document-template-list.view';
import {URLConstant} from '../../../shared/constant/URLConstant';
import {PathConstant} from '../../../shared/constant/PathConstant';
import {Act} from '../../../shared/components/msx-datatable/enums/act';
import {DocumentTemplate} from '../../../model/template';
import {CommonConstant} from '../../../shared/constant/common.constant';
import {GetDocumentTemplateResponse} from '../../../shared/dto/document-template/get-document-template.response';
import {HttpClient} from '@angular/common/http';
import { Button } from 'app/shared/components/msx-view/models/Button';

@Component({
  selector: 'app-documents',
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.scss', '/assets/sass/libs/datatables.scss'],
  encapsulation: ViewEncapsulation.None
})
export class DocumentsComponent implements OnInit {
  view: MsxView;
  buttonList: Button[];
  serviceUrl = URLConstant.ListTemplate;

  constructor(private service: TemplateService, private modalService: NgbModal, private http: HttpClient, private router: Router) {
  }

  ngOnInit(): void {
    this.view = DocumentTemplateListView;
    this.buttonList = [
      {name: 'New', class: 'btn btn-primary', hide: false, icon: 'ft-plus'}
    ]
  }

  onBtnClickListener(event) {
    switch (event['name']) {
      case this.buttonList[0].name:
        this.router.navigate([PathConstant.ADD_DOCUMENT_TEMPLATE]);
        break;
      default:
        break;
    }
  }

  onItemClickListener(event) {
    const data = event['data'];

    switch (event['act']['type']) {
      case Act.Edit:
        return this.gotoEditDocumentTemplate(data);

      case Act.Setting:
        return this.gotoSetupSigner(data);

      default:
        return this.gotoView(data);
    }

  }

  gotoEditDocumentTemplate(data: DocumentTemplate) {
    this.router.navigate([PathConstant.EDIT_DOCUMENT_TEMPLATE], {state: data});
  }

  gotoSetupSigner(data: DocumentTemplate) {
    this.getTemplateDetail(CommonConstant.MODE_SIGNER, data);
  }

  gotoView(data: DocumentTemplate) {
    this.getTemplateDetail(CommonConstant.MODE_VIEW, data);
  }

  private getTemplateDetail(mode: string, data: DocumentTemplate) {
    this.http.post<GetDocumentTemplateResponse>(URLConstant.GetTemplate, {documentTemplateCode: data.documentTemplateCode})
      .subscribe(response => {
        const extras = {rawTemplate: `data:application/pdf;base64,${response.documentFile}`, signer: response.signer};
        data = {...data, ...extras};

        if (CommonConstant.MODE_SIGNER === mode) {
          this.router.navigate([PathConstant.SETTING_DOCUMENT_TEMPLATE], {state: data,
            queryParams: {mode: CommonConstant.MODE_SIGNER}});
        } else {
          this.router.navigate([PathConstant.VIEW_DOCUMENT_TEMPLATE], {state: data,
            queryParams: {mode: CommonConstant.MODE_VIEW}});
        }
      });
  }

}
