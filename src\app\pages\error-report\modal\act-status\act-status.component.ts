import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, Input, NgZone, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ErrHistActivationStatusRequest } from 'app/model/api/ErrHistActivationStatus.request';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { BehaviorSubject } from 'rxjs';
import { ActStatusTable } from './view/act-status.view';

@Component({
  selector: 'app-act-status',
  templateUrl: './act-status.component.html',
  styleUrls: ['./act-status.component.scss']
})
export class ActStatusComponent implements OnInit {
  @Input() idErrorHistory: any;

  actStatusTable = ActStatusTable;

  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);

  constructor(public activeModal: NgbActiveModal, public http: HttpClient,
     private ngZone: NgZone, private cdr: ChangeDetectorRef) { }

  async ngOnInit() {
    await this.getActStatus().then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  async getActStatus() {
    const url = URLConstant.ActivationStatus;
    const request : ErrHistActivationStatusRequest = new ErrHistActivationStatusRequest();
    request.idErrorHistory = this.idErrorHistory;

    await this.http.post(url, request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
          this.datasource.next(response);
        }
      }
    )
  }

}
