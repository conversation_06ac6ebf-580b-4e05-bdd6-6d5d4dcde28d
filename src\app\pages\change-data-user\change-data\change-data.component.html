<div class="container">
    <div class="row">
        <div class="col-12 d-flex align-items-center justify-content-center">
            <div class="card" style="width: 480px;">
                <div class="card-content">
                    <div class="card-body">
                        <p class="title" translate>Change Profile</p>
                        <label class="form-title" translate>Data yang akan diganti</label>
                        <div class="row">
                            <div class="col-6" (click)="onClickEmail()">
                                <span class="border rounded border-primary d-block">
                                    <div class="form-check form-check-inline align-middle p-3">
                                        <input class="form-check-input" type="radio" id="email"
                                            [checked]="emailChecked">
                                        <label class="form-check-label label-radio-button" translate>Email</label>
                                    </div>
                                </span>
                            </div>
                            <div class="col-6" (click)="onClickNoHp()">
                                <span class="border rounded border-primary d-block">
                                    <div class="form-check form-check-inline align-middle p-3">
                                        <input class="form-check-input" type="radio" id="noHp" [checked]="noHpChecked">
                                        <label class="form-check-label label-radio-button" translate>No
                                            Handphone</label>
                                    </div>
                                </span>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between flex-sm-row flex-column" style="margin-top: 20px;">
                            <button class="btn btn-info" [ngClass]="{disabled: !emailChecked && !noHpChecked}"
                                (click)="openOtpModal()" style="width: 100%; height: 55px; font-size: 16px;"
                                translate>Kirim OTP</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>