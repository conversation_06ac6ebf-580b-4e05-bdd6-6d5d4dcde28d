<div class="row" style="margin-top: 15px;">
    <div class="col-6">
        <div class="content-header" style="margin-top: 0 !important;" translate>
            Profile
        </div>
    </div>
</div>

<div class="row match-height">
    <div class="col-12">
        <div class="card">
            <div class="card-content">
                <div class="card-body">
                        <div style="display: flex; align-self: flex-start;">
                        <div>
                            <p translate>Name</p>
                            <div *ngFor="let item of label">
                                <br>
                                <p translate>{{item.emaillabel}}</p>
                                <p translate>{{item.phonelabel}}</p>
                                <p translate>{{item.vendorlabel}}</p>
                            </div>
                        </div>
                            <div style="margin-right: 8px; margin-left: 8px;">
                                <div>
                                    <p>:</p>
                                    <div *ngFor="let item of label">
                                          <br>
                                          <p>:</p>
                                          <p>:</p>
                                          <p>:</p>
                                    </div>
                                </div>
                            </div>
                        <div>
                            <p>{{name}}</p>
                            <div *ngFor="let respon of beanobj">
                                <br>
                                <p translate>{{respon.email}}</p>
                                <p translate>{{respon.phoneNumber}}</p>
                                <p translate>{{respon.vendor}}</p>
                            </div>
                         </div>
                        </div>
                </div>
            </div>
        </div>
    </div>
</div>