<div class="row" style="margin-top: 15px">
  <div class="col-6">
    <div class="content-header" style="margin-top: 0 !important;" translate>Dashboard</div>
  </div>
</div>
<div class="row" style="margin-top: 2px; color: black !important; height: 25px;">
  <div class="col-12">
    <label class="card-title" translate><span class="font-weight-bold">{{totalResult | mask: 'separator':'.'}}</span> Total</label> 
  </div>
</div>
<div class="row">
  <div class="col-6">
    <div *ngIf="documents.length > 0" class="content-header" style="margin-top: 0 !important;">
      <button class="btn btn-primary" (click)="goToBulkSign($event)">{{'Sign' | translate}} {{documents.length}} {{'Document' | translate}}</button>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-12">
    <div [ngClass]="{ 'not-card': isMobile, 'card': !isMobile }">
      <div *ngIf="!isMobile" class="card-header">
        <label class="card-title pl-4 font-medium-5 font-weight-bold" translate>Inbox</label>
      </div>
      <div class="card-content">
        <div [ngClass]="{ 'body': isMobile, 'card-body': !isMobile }">  
           <app-msx-card-datatable [tableObj]="dashboardTable" *ngIf="dashboardTable" [datasource]="datasource" (getPage)="getPage($event)" 
           (itemClickListener)="onItemClickListener($event)" (selected)="onSelected($event)" [usingMsxPaging]="false"></app-msx-card-datatable>
        </div>
      </div>
    </div>
  </div>
</div>

