<div class="row" style="margin-top: 15px">
  <div class="col-6">
    <div class="content-header" style="margin-top: 0 !important;" translate>Setting Document</div>
  </div>
  <div class="col-6 text-right">
    <button class="btn btn-light mr-2" (click)="doBack()" translate>Cancel</button>
    
    <button *ngIf="sequence === '1'"  class="btn btn-info" (click)="onSubmit()" [disabled]="isSubmitting" translate>Next</button>
    <button *ngIf="sequence === '0'"  class="btn btn-info" (click)="onSubmit()" [disabled]="isSubmitting" translate>Process</button>
    <button *ngIf="sequence == null"  class="btn btn-info" (click)="onSubmit()" [disabled]="isSubmitting" translate>Save</button>
  </div>
</div>

<app-form *ngIf="mode === 'Signer' || mode === 'ManualSigner' || sdtOnly === true" [formObj]="mForm" col="12" [hideButton]="true"></app-form>

<div class="row" style="overflow: auto;">
  <div class="col-12 align-items-center justify-content-center">
    <app-document-anotate [document]="state['rawTemplate']" [signer]="signer" (annotations)="onAnnotations($event)"
                          (numOfPage)="onNumOfPage($event)" [sdtOnly]="sdtOnly" [users]="users" [isMeterai]="state['isMeterei']"></app-document-anotate>
  </div>
</div>
