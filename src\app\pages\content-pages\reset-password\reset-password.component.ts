import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ChangePasswordModalComponent } from './../change-password/change-password-modal/change-password-modal.component';
import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { UserService } from 'app/services/api/user.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ResetPasswordRequest } from 'app/shared/dto/password/reset-password.request';
import { ResetPasswordResponse } from 'app/shared/dto/password/reset-password.response';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import {TranslateService} from '@ngx-translate/core';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent implements OnInit {

  formObj: FormModel<any>;
  xForm: FormGroup;
  state: any;
  loginId: string;
  resetCode: string;
  currentDate = new Date();
  swal = swalFunction;

  constructor(private router: Router, private http: HttpClient,
    private toastService: ToastrService, private userService: UserService,
    private global: GlobalService, private translate: TranslateService,
    private modalService: NgbModal) {
    this.state = this.router.getCurrentNavigation().extras.state;
    this.loginId = this.state?.loginId;
    this.resetCode = this.state?.resetCode;
  }

  ngOnInit(): void {
    this.initView();
  }

  initView() {
    if (!this.loginId && !this.resetCode) {
      this.router.navigate([PathConstant.FORGOT_PASSWORD_PAGE]);
    }

    this.formObj = {
      name: 'resetPassword',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      mode: 'Edit',
      components: [
        {
          key: 'newPassword',
          label: 'New Access Code',
          placeholder: 'Type your new access code here',
          controlType: FormConstant.TYPE_PASSWORD,
          icon: 'ft-lock',
          required: true
        },
        {
          key: 'confirmNewPassword',
          label: 'Confirm New Access Code',
          placeholder: 'Confirm New Access Code',
          controlType: FormConstant.TYPE_PASSWORD,
          icon: 'ft-lock',
          required: true
        }
      ],
      params: [
      ]
    }
  }

  onForm(form: FormGroup) {
    this.xForm = form;
  }

  onNext(data: any) {
    if (data['newPassword']) {
      if (data['newPassword'] !== data['confirmNewPassword']) {
        this.toastService.error('Input kode akses berbeda', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }

      const request = new ResetPasswordRequest();
      request.audit = {
        callerId: this.loginId
      };
      request.loginId = this.loginId;
      request.resetCode = this.resetCode;
      request.newPassword = data['newPassword'];

      this.http.post<ResetPasswordResponse>(URLConstant.ResetPassword, request).subscribe(
        (response) => {
          if (response.status.code === 0) {
            this.swal.Success(this.translate.instant('Your access code has been successfully changed'));
            this.router.navigate([PathConstant.LOGIN]);
          } else {
            this.swal.Error(response.status.message);
          }
        }
      );

    }
  }

  openModal() {
    const modal = this.modalService.open(ChangePasswordModalComponent, { backdrop: 'static', keyboard: false });
  }
}
