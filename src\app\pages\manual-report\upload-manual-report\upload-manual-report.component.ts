import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { uploadManualReportRequest } from 'app/model/api/uploadManualReportRequest';
import { AuditContext } from 'app/model/audit.context';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { QuestionDate, QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { QuestionFile } from 'app/shared/components/ms-form/questions/question-file';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-upload-manual-report',
  templateUrl: './upload-manual-report.component.html',
  styleUrls: ['./upload-manual-report.component.scss']
})
export class UploadManualReportComponent implements OnInit {

  constructor(private toastrService: ToastrService, private http: HttpClient, private router: Router, private fcs: MsxFormControlService,  private global: GlobalService) { }

  rawFile: string;
  formObj: FormModel<any>;
  msxForm: FormGroup;
  formData: any;
  colsize = 6;
  fileName: string;
  change: string;
  base64: string;

  ngOnInit(): void {
    this.setupQuestion();
    const controls = [...this.formObj.params, ...this.formObj.components];
    this.msxForm = this.fcs.toFormGroup(controls);
    console.log('form value', this.msxForm.getRawValue())
  }

  setupQuestion() {
    const time = Date.now();
    const nowday = new Date(time);
    this.formObj = {
      name: 'insertExcelAutosignBm',
      mode: CommonConstant.MODE_ADD,
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionDropdown({
          key: 'tenantCode',
          label: 'Tenant',
          placeholder: 'Select Tenant',
          serviceUrl: URLConstant.GetTenantList,
          value: '',
          params: {
            status: '1'
          },
          options: [
            { key: '', value: CommonConstant.CONST_SELECT_TENANT}
          ],
          required: true,
          validations: [
            {type: 'required', message: 'Payment Sign Type harus di isi.'}
          ],
          args: {
            list: 'tenantList',
            key: 'tenantCode',
            value: 'tenantName'
          }
        }),
        new QuestionDropdown({
          key: 'reportType',
          label: 'Report Type',
          placeholder: 'Select Report Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          required: true,
          validations: [
            {type: 'required', message: 'Report Type harus di isi.'}
          ],
          params: {
            lovGroup: 'REPORT_TYPE'
          }
        }),
        new QuestionDate({
          key: 'reportDate',
          label: 'Report Date',
          placeholder: CommonConstant.FORMAT_DATE,
          required: true,
        }),
        new QuestionFile({
          key: 'documentExample',
          label: 'Document',
          placeholder: 'Choose File',
          accept:  ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv', 'application/vnd.ms-excel'],
          required: true
        }),
      ],
      params: []
    }

    const quest = this.getQuestionkey('documentName');
    console.log('documentName', quest);
    console.log('document date', {year: nowday.getFullYear(), month: nowday.getMonth(), day: nowday.getUTCDay} )
  }

  getQuestionkey(key:string) {
    return this.formObj.components.find(x => x.key === key);
  }

  doBack() {
    this.router.navigate([PathConstant.MANUAL_REPORT_LIST]);
  }

  onInput(event) {
    console.log('input', event);
    if (event.target && event.target['files']) {
      const files = event.target.files;
      const file = files[0];
  
      // Get the filename and file type
      this.fileName = file.name;
      const fileType = file.type;
  
      // Check for valid file types
      const validFileTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv', 'application/vnd.ms-excel'];
      if (!validFileTypes.includes(fileType)) {
        this.msxForm.get('documentExample').reset();
        this.toastrService.warning('Silahkan pilih dokumen dengan format xlsx, csv, atau xls!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }
  
      // Convert file to base64
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.base64 = e.target.result.split(',')[1]; // Extract base64 string
      };
      reader.readAsDataURL(file);
    }
  }
  
  onBase64Data($event) {
    this.rawFile = $event;
  }

  onSubmit() {
    this.formData = this.msxForm.getRawValue();
    const reportDate = this.formData['reportDate'];

    const request  = new uploadManualReportRequest();
    request.tenantCode = this.formData['tenantCode'];
    request.reportType = this.formData['reportType'];
    if (reportDate) {
      request.reportDate = `${reportDate.year}-${String(reportDate.month).padStart(2, '0')}-${String(reportDate.day).padStart(2, '0')}`;
    }    
    request.audit = new AuditContext();
    request.audit.callerId = this.global.user.loginId;
    request.base64File = this.base64;
    request.fileName = this.fileName;

    this.http.post<any>(URLConstant.uploadManualReport, request).subscribe(response => {
      if (response['status']['code'] === 0) {
        this.toastrService.success('Manual report berhasil diupload.', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        this.router.navigate([PathConstant.MANUAL_REPORT_LIST])
      }
      })
  }

}
