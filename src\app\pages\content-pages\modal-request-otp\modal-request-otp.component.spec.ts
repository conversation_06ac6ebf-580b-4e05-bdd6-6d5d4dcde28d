import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalRequestOtpComponent } from './modal-request-otp.component';

describe('ModalRequestOtpComponent', () => {
  let component: ModalRequestOtpComponent;
  let fixture: ComponentFixture<ModalRequestOtpComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalRequestOtpComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalRequestOtpComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
