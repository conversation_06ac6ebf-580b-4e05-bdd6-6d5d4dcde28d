import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { ResetPasswordVendorRequest } from 'app/shared/dto/password/reset-password-vendor.request';
import { ResetPasswordVendorResponse } from 'app/shared/dto/password/reset-password-vendor.response';
import * as swalFunction from '../../../shared/data/sweet-alerts';

@Component({
  selector: 'app-reset-password-vendor',
  templateUrl: './reset-password-vendor.component.html',
  styleUrls: ['./reset-password-vendor.component.scss']
})
export class ResetPasswordVendorComponent implements OnInit {

  vendorResetCode: string;

  swal = swalFunction;

  constructor(private activeRoute: ActivatedRoute, private http: HttpClient, private router: Router) {
    this.activeRoute.queryParams.subscribe(params => {
      this.vendorResetCode = params?.vrc;
    });
  }

  ngOnInit(): void {
    if (this.vendorResetCode) {
      const request = new ResetPasswordVendorRequest();
      request.audit.callerId = 'SYSTEM';
      request.resetPasswordCode = this.vendorResetCode;
      this.http.post<ResetPasswordVendorResponse>(URLConstant.GetResetPasswordLink, request).subscribe(
        (response) => {
          if (response.status.code === CommonConstant.STATUS_CODE_SUCCESS) {
            location.href = response.resetPasswordLink;
          } else if (response.status.code === CommonConstant.STATUS_CODE_RESET_PASSWORD_CODE_INVALID) {
            this.swal.ErrorWithoutButton("Harap melakukan request link reset password kembali dan menunggu sms reset password berikutnya","Link sudah tidak berlaku!");
          }
        }
      );
    } else {
      this.router.navigate([PathConstant.LOGIN]);
    }
  }

}
