import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { HttpClient } from '@angular/common/http';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { ViewDocumentRequest } from 'app/model/api/view.document.request';
import { ViewDocumentResponse } from 'app/model/api/view.document.response';
import { BulkSignDocumentResponse } from 'app/model/api/bulk.sign.document.response';
import { BulkSignDocumentRequest } from 'app/model/api/bulk.sign.document.request';
import { GlobalService } from 'app/shared/data/global.service';
import { Document } from 'app/model/document';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { DeviceDetectorService } from 'ngx-device-detector';
import {NgxSpinnerService} from 'ngx-spinner';
import { CheckStatusDocumentService } from 'app/services/check-status-document.service';
import { Vendor } from 'app/shared/constant/vendor';
import { CheckDocumentBeforeSigningRequest } from 'app/model/api/checkDocumentBeforeSigning.request';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import { CountlyService } from 'app/services/api/countly.service';
import { Signer } from 'app/model/signer';
import { CertificateExpiredRegenerateInvLinkComponent } from 'app/pages/inquiry/modal/certificate-expired-regenerate-inv-link/certificate-expired-regenerate-inv-link.component';
import { PDFDocumentProxy } from 'ng2-pdf-viewer';
@Component({
  selector: 'app-bulk-sign',
  templateUrl: './bulk-sign.component.html',
  styleUrls: ['./bulk-sign.component.scss']
})
export class BulkSignComponent implements OnInit {
  @ViewChild('pdfViewerOnDemand') pdfViewerOnDemand;
  swal = swalFunction;

  active = 1;
  documents: Document[];
  pdfBase64: string;
  vendorCode: string;
  pdfSrc: Uint8Array = new Uint8Array();;
  checkDocStatus: any;
  isMobile = false;
  isFromView:boolean = false;
  url;
  signProcess:any[];
  documentsSign:any[];
  isAllSignValid = true;
  msg: string;
  tenantCode: string;
  private pathSrc: string;
  public doc: string;
  public state: any;
  public signer: Signer[];
  pdfBuffer: ArrayBuffer;
  private pdfLastPageNumber = 0;
  pdfPageNumber = 1;
  canSign = true;
  currentDocument: string;
  scrolledThroughDocs: string[] = [];
  documentIds: string[] = [];
  
  constructor(private router: Router, private activeRoute: ActivatedRoute, private modalService: NgbModal, private toastService: ToastrService,
              private http: HttpClient, private cdr: ChangeDetectorRef, private readonly global: GlobalService, private deviceService: DeviceDetectorService,
              private spinner: NgxSpinnerService, private checkStatusDocument: CheckStatusDocumentService, private location: Location, private countlyService: CountlyService) {
    
    this.isMobile = deviceService.isMobile();

    if (this.router.getCurrentNavigation().extras.state) {
      const state = this.router.getCurrentNavigation().extras.state;
      this.documents = state.data;
      this.vendorCode = state.vendorCode;
      this.isFromView = state.isFromView ?? false; 

      if(state.msg){
        this.msg = state.msg;
      }
      if(state.tenantCode){
        this.tenantCode = state.tenantCode;
      } else {
        this.tenantCode =this.global.user.role.tenantCode
      }

      console.log(this.documents);
    }
    const snapshot = this.activeRoute.snapshot.data;
    this.pathSrc = snapshot.path;

  }

  async ngOnInit(): Promise<void> {
    if (this.global.msg && this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      localStorage.clear();
    }

    this.documentsSign = [];
    
      //check Document Before Signing
      await this.checkDocBeforeSign();
      //penjagaan untuk jika semua doc yang dipilih signing processnya 1 = tidak bisa ttd
      await this.checkIfAllInvalid();

    if (this.isAllSignValid) {
      await this.viewPdf(this.documents[0].documentId);
    }
    this.countlyService.initiate();
  }

  async checkDocBeforeSign(){
    const req = new CheckDocumentBeforeSigningRequest();
    let url;
    if(this.msg && this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
      req.msg = this.msg;
      req.tenantCode = this.tenantCode;
      url = URLConstant.checkDocumentBeforeSigningEmbedV2;
    } 
    else{
      req.loginId = this.global.user.loginId;
      url = URLConstant.checkDocumentBeforeSigning;
    }

    for (let i = 0; i < this.documents.length; i++) {
      req.listDocumentId.push(this.documents[i].documentId);
      this.documentIds.push(this.documents[i].documentId);
      this.documents[i].read = false;
    }
    await this.http.post(url, req).toPromise().then(
      (response) => {
        if (this.router.url.includes('/embed/') && this.router.url.includes('/V2/') &&response["status"]["code"] === 0 && response["listCheckDocumentBeforeSigning"]["0"]["signingProcess"] === "2"){
          swalFunction.Error("Dokumen harus ditandatangani oleh user lain terlebih dahulu.");
          const extras: NavigationExtras = {
            queryParams: {
              msg: this.msg,
              tenantCode: this.global.user.role.tenantCode
            }
          };
          this.router.navigate([PathConstant.EMBED_V2_DASHBOARD], extras);
        }else if (this.router.url.includes('/embed/') && this.router.url.includes('/V2/') && response["status"]["code"] === 0 && response["certificateActiveStatus"] === "0"){
          const extras: NavigationExtras = {
            queryParams: {
              msg: this.msg,
              tenantCode: this.global.user.role.tenantCode
            }
          };
          const modal = this.modalService.open(CertificateExpiredRegenerateInvLinkComponent, { backdrop: 'static', keyboard: false });
          modal.componentInstance.msg = this.msg;
          modal.componentInstance.documentId = this.documents[0].documentId;
          modal.componentInstance.isEmbed = true;
          this.router.navigate([PathConstant.EMBED_V2_DASHBOARD], extras);
        }
        else if (response["status"]["code"] === 0 && response["listCheckDocumentBeforeSigning"]["0"]["signingProcess"] === "2") {
          swalFunction.Error("Dokumen harus ditandatangani oleh user lain terlebih dahulu.");
          this.router.navigate([PathConstant.DASHBOARD])
        } else if (response["status"]["code"] === 0 && response["certificateActiveStatus"] === "0") {
          const modal = this.modalService.open(CertificateExpiredRegenerateInvLinkComponent, { backdrop: 'static', keyboard: false });
          modal.componentInstance.loginId = this.global.user.loginId;
          modal.componentInstance.documentId = this.documents[0].documentId;
          modal.componentInstance.isEmbed = false;
          this.router.navigate([PathConstant.DASHBOARD])
        }
        if (response["status"]["code"] === 0) {
          if (response["mustScrollToSignStatus"] === "1") {
            this.canSign = false;
          }

          this.signProcess = response["listCheckDocumentBeforeSigning"];
          return;
        }
      }
    );
  }

  async checkIfAllInvalid(){
    let total = 0;
    for(let j = 0; j < this.signProcess.length; j++) {
      if(this.signProcess[j].signingProcess === '1'){
        total ++;
      }
    }
    if(total === this.signProcess.length){
      this.isAllSignValid = false;
      this.swal.Error('Semua dokumen yang anda pilih sedang dalam proses tanda tangan! Silahkan pilih dokumen lain.');
      this.goToDashboard();
    }
  }

  signAll() {
    this.swal.Confirm('Telah membaca dan akan menandatangani dokumen yang Anda pilih!', 'Apakah Anda yakin?').then(
      async (result) => {
        if (result.isConfirmed === true) {
          await this.checkBulkSignVendor();
        }
      }
    )
  }

  async checkBulkSignVendor(){
    let totalDigi = 0;
    let totalVida = 0;
    let totalPrivy = 0;
    let totalTknaj = 0;
    for(let j = 0; j < this.documents.length; j++) {
      if(this.documents[j].vendorCode === Vendor.DigiSign){
        totalDigi ++;
      } else if(this.documents[j].vendorCode === Vendor.VIDA){
        totalVida ++;
      } else if(this.documents[j].vendorCode === Vendor.PRIVY){
        totalPrivy ++;
      } else if (this.documents[j].vendorCode === Vendor.TekenAja){
        totalTknaj++
      }
    }
  
    if(totalDigi == this.documents.length || (this.vendorCode ===Vendor.DigiSign && this.vendorCode != null)){
        await this.getUrlBulkSignDigi();
    } else if(totalVida == this.documents.length || (this.vendorCode === Vendor.VIDA && this.vendorCode != null)){
       this.goToBulkSignEsign();
    } else if(totalPrivy == this.documents.length || (this.vendorCode ===Vendor.PRIVY && this.vendorCode != null)){
      this.goToBulkSignEsign();
   } else if(totalTknaj == this.documents.length || (this.vendorCode ===Vendor.TekenAja && this.vendorCode != null)){
    this.getUrlBulkSignDigi();
   }
  }

  goToBulkSignEsign() {
    for (let i = 0; i < this.documents.length; i++) {
       for(let j = 0; j < this.signProcess.length; j++) {
        if(this.documents[i].documentId === this.signProcess[j].documentId && this.signProcess[j].signingProcess === '0'){
          this.documentsSign.push(this.documents[i]);
        }
      }
    }
    const extras: NavigationExtras = {
      state: {
        data: this.documentsSign,
        msg: this.msg,
        tenantCode: this.tenantCode,
      }
    };

    if(this.msg && this.router.url.includes('/embed/')  && this.router.url.includes('/V2/')){
      this.router.navigate([PathConstant.EMBED_V2_BULK_SIGN_ESIGN], extras);
    } 
    else{
      this.router.navigate([PathConstant.BULK_SIGN_ESIGN], extras);
    }
    
  }

  async getUrlBulkSignDigi() {
    const request: BulkSignDocumentRequest = new BulkSignDocumentRequest();
    request.documentIds = [];
    let bulkSignUrl;
    if (this.msg && this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
      request.msg = this.msg;
      request.tenantCode = this.global.user.role.tenantCode;
      bulkSignUrl = URLConstant.bulkSignDocumentEmbedV2;
    }
    else if (this.msg && this.router.url.includes('/embed/')) {
      request.msg = this.msg;
      bulkSignUrl = URLConstant.BulkSignDocumentEmbed;
    } else {
      request.loginId = this.global.user.loginId;
      bulkSignUrl = URLConstant.BulkSignDocument;
    }
    for (let i = 0; i < this.documents.length; i++) {
       for(let j = 0; j < this.signProcess.length; j++) {
        if(this.documents[i].documentId === this.signProcess[j].documentId && this.signProcess[j].signingProcess === '0'){
          request.documentIds.push(this.documents[i].documentId);
        }
      }
    }

    await this.http.post<BulkSignDocumentResponse>(bulkSignUrl, request).toPromise().then(
      async (response) => {
        if (response.status.code === 0 && response.vendorCode === Vendor.DigiSign) {
          if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED || this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
            this.router.navigate([PathConstant.EMBED_BULK_SIGN_DIGI], { queryParams: { url: response.signLink } });
          } else  {
            this.router.navigate([PathConstant.INQUIRY_BULK_SIGN_DIGI], { queryParams: { url: response.signLink } });
          }
        } else if (response.status.code === 5 && response.status.message === 'user tidak ditemukan') {
          // get tenant code and vendor code, lempar ke register
          const extras: NavigationExtras = {
            state: {
              data: {
                tenantCode: response.tenantCode,
                vendorCode: response.vendorCode
              }
            }
          }
          this.router.navigate([PathConstant.REGISTER], extras);
        } else if (response.status.code === 0 && response.vendorCode === Vendor.TekenAja){
            window.open(response.signLink, '_blank');

        }
      }
    );
  }

  async viewPdf(documentId) {
    this.currentDocument = documentId;
    this.pdfPageNumber = 1;
    this.pdfLastPageNumber = 0;

    const request: ViewDocumentRequest = new ViewDocumentRequest();
    let URL: string;

    request.documentId = documentId;
    if (this.msg && this.router.url.includes('/embed/') && !this.router.url.includes('/V2/')) {
      request.msg = this.msg;
      URL = URLConstant.ViewDocumentEmbed;
    } else if (this.msg && this.router.url.includes('/embed/')  && this.router.url.includes('/V2/')) {
      request.msg = this.msg;
      request.tenantCode = this.tenantCode;
      URL =URLConstant.ViewDocumentEmbedV2;
    } else {
      URL = URLConstant.ViewDocument;
    }

    this.http.post<ViewDocumentResponse>(URL, request).subscribe((response) => {
      this.spinner.show();
      if (response.status.code !== 0) {
        return;
      }
      
      this.pdfBase64 = response.pdfBase64;
      this.base64ToArrayBuffer(response.pdfBase64);
      this.cdr.detectChanges();
    });

    this.cdr.detectChanges();
  }

  b64toBlob(b64Data, contentType= '', sliceSize= 512) {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, {type: contentType});
    return blob;
  }

  base64ToArrayBuffer(base64) {
    const binary_string =  window.atob(base64);
    const len = binary_string.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++)        {
        bytes[i] = binary_string.charCodeAt(i);
    }

    this.pdfBuffer = bytes.buffer;
    console.log('Pdf Buffer empty', !this.pdfBuffer);
    this.cdr.detectChanges();
    this.spinner.hide();
  }

  onClickBack(){
    if(this.isFromView){
      this.goBack();
    } else {
      this.goToDashboard();
    }
  }

  goBack() {
    this.location.back();
  }

  goToDashboard() {
    const extras: NavigationExtras = {
      queryParams: {
        msg: this.msg,
        tenantCode: this.tenantCode
      }
    };

    if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      this.router.navigate([PathConstant.EMBED_DASHBOARD], extras);
    } else if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
      this.router.navigate([PathConstant.EMBED_V2_DASHBOARD], extras);
    }
    else {
      this.router.navigate([PathConstant.DASHBOARD]);
    }
  }

  onLoadComplete(pdf: PDFDocumentProxy) {
    this.pdfLastPageNumber = pdf._pdfInfo.numPages;
    if (this.pdfPageNumber == this.pdfLastPageNumber) {
      this.scrolledThroughDocs.push(this.currentDocument);
      const i = this.documentIds.indexOf(this.currentDocument);
      this.documents[i].read = true;
      this.cdr.detectChanges();
    }

    if (this.scrolledThroughDocs.length == this.documents.length) {
      this.canSign = true;
      this.cdr.detectChanges();
    }
  }

  pageChange($event) {
    console.log('Current Page: ' + this.pdfPageNumber);
    if (this.scrolledThroughDocs.includes(this.currentDocument)) {
      return;
    }

    if (this.pdfPageNumber == this.pdfLastPageNumber) {
      this.scrolledThroughDocs.push(this.currentDocument);
      console.log('Scrolled to the bottom of the page');
      const i = this.documentIds.indexOf(this.currentDocument);
      this.documents[i].read = true;
      console.log('Scrolled to the bottom of the page');
      this.cdr.detectChanges();
    }

    if (this.scrolledThroughDocs.length == this.documents.length) {
      this.canSign = true;
      this.cdr.detectChanges();
    }

  }
}
function checkBulkSignVendor() {
  throw new Error('Function not implemented.');
}

