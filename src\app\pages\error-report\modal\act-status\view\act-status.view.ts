import { ActStatus } from "app/model/api/act-status";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";

export const ActStatusTable: Table<ActStatus> = {
    name: 'listActStatus',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'signerType',
            label: 'Signer Type',
            width: 150
        },
        {
            type: ColumnType.Text,
            prop: 'name',
            label: 'Name',
            width: 150
        },
        {
            type: ColumnType.Text,
            prop: 'idNo',
            label: 'Id. No.',
            width: 150
        },
        {
            type: ColumnType.Text,
            prop: 'activationStatus',
            label: 'Activation Status',
            width: 200
        }
    ]
}