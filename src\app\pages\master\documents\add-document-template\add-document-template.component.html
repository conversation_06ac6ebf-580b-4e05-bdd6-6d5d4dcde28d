<div class="row" style="margin-top: 15px">
  <div class="col-6">
    <div class="content-header" style="margin-top: 0 !important;">{{mode | translate}} {{'Document Template Page' | translate}}</div>
  </div>
</div>

<div class="row match-height">
  <div class="col-6">
    <div class="card">
      <div class="card-content">
        <div class="card-body">
          <form [formGroup]="msxForm" (ngSubmit)="openPrompt($event)" id="xForm" #formElement>
            <ng-container *ngIf="mForm?.params && mForm.params?.length > 0">
              <input *ngFor="let param of mForm.params" type="hidden" [formControlName]="param.key"
                     [value]="param.value" [id]="param.key" />
            </ng-container>

            <div class="row">

              <!-- Document Template Code -->
              <div class="col-{{mForm.colSize}}">
                <app-text [form]="msxForm" [question]="getQuestion('documentTemplateCode')" [direction]="mForm.direction" (selected)="onSelect($event)"></app-text>
              </div>

              <!-- Document Template Name -->
              <div class="col-{{mForm.colSize}}">
                <app-text [form]="msxForm" [question]="getQuestion('documentTemplateName')" [direction]="mForm.direction" (selected)="onSelect($event)"></app-text>
              </div>

              <!-- Description -->
              <div class="col-{{mForm.colSize}}">
                <app-text [form]="msxForm" [question]="getQuestion('documentTemplateDescription')" [direction]="mForm.direction" (selected)="onSelect($event)"></app-text>
              </div>

              <!-- Payment Sign Type -->
              <div class="col-{{mForm.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('paymentSignTypeCode')" [direction]="mForm.direction" (selected)="onSelect($event)"></app-select>
              </div>

              <!-- Vendor -->
              <div class="col-{{mForm.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('vendorCode')" [direction]="mForm.direction" (selected)="onSelect($event)"></app-select>
              </div>

              <!-- Upload document -->
              <div class="col-{{mForm.colSize}}">
                <app-file [form]="msxForm" [question]="getQuestion('documentExample')" [direction]="mForm.direction" (change)="onInput($event)" (rawFile)="onBase64Data($event)"></app-file>
              </div>

              <!-- Sequence -->
              <div class="col-{{mForm.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('isSequence')" [direction]="mForm.direction" (selected)="onSelect($event)"></app-select>
              </div>

              <!-- Use Sign QR -->
              <div class="col-{{mForm.colSize}}">
                <app-select [form]="msxForm" [options]="useSignQrOptionList" [question]="getQuestion('useSignQr')" [direction]="mForm.direction" (selected)="onSelect($event)"></app-select>
              </div>

              <!-- Is Active -->
              <div class="col-{{mForm.colSize}}">
                <app-select [form]="msxForm" [question]="getQuestion('isActive')" [direction]="mForm.direction" (selected)="onSelect($event)"></app-select>
              </div>

              <!-- Priority Sequence -->
              <div class="col-{{mForm.colSize}}">
                <app-currency [form]="msxForm" [question]="getQuestion('prioritySequence')" [direction]="mForm.direction" (selected)="onSelect($event)"></app-currency>
              </div>

              <!-- Save & Cancel Button -->
              <div class="col-12 text-center">
                <button class="btn btn-light mr-2" type="button" (click)="onCancel($event)"><i class=""></i> {{'Cancel' | translate}}</button>
                <button class="btn btn-info" type="submit" [disabled]="msxForm.invalid"><i class=""></i> {{submitLabel | translate}}</button>
              </div>

            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>