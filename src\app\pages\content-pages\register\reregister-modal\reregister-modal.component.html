
  <div class="modal-header">
    <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="mt-4">
      <div class="row justify-content-center align-items-center">
        <div class="row align-items-center">
          <p class="font-large-1 text-center text-bold-700 px-5" translate>Anda sudah terdaftar.</p>
        </div>
      </div>
    </div>

    <div class="mt-4">
      <div class="row justify-content-center align-items-center">
        <p class="font-large-1 text-center text-bold-300" translate>Apakah Anda ingin mendaftar ulang?</p>
        <p class="font-large-1 text-center text-bold-300" translate>Jika Ya, Silakan isi form di bawah ini.</p>
      </div>
    </div>

    <form [formGroup]="reregisForm" >
    <!-- (ngSubmit)="updateReregistration()" > -->
      <div class="form-group">
        <label for="email" class="form-title">Email <span class="mandatory text-danger">*</span></label>
        <input type="email" formControlName="email" id="email" class="form-control mb-2" placeholder="Email"
               [ngClass]="{ 'is-invalid': reregisForm.get('email').dirty && rf.email.invalid, 'is-valid': reregisForm.get('email').dirty && !rf.email.invalid }"
               required>
      </div>

      <div class="form-group">
        <label for="phone" class="form-title" translate>Phone Number <span class="mandatory text-danger">*</span></label>
        <input type="tel" formControlName="phone" id="phone" class="form-control mb-2" placeholder="Contoh : 081234567890"
              [ngClass]="{ 'is-invalid': reregisForm.get('phone').dirty && rf.phone.invalid, 'is-valid': reregisForm.get('phone').dirty && !rf.phone.invalid }"
              required>
      </div>

      <div class="row my-4 px-4">
        <div class="col-6 text-center">
          <button *ngIf="!waiting" type="button" class="btn btn-secondary btn-block" (click)="activeModal.dismiss('Cross click')" translate>Maybe Later!</button>
        </div>
        <div class="col-6 text-center">
          <button *ngIf="!waiting" type="button" class="btn btn-primary btn-block" [disabled]="enableSubmit" (click)="updateReregistration()" translate>Update and Re-register</button>
        </div>
        <div *ngIf="waiting" class="d-flex justify-content-center">
          <div class="spinner-border text-secondary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
        </div>
      </div>
    </form>
  </div>
  
  