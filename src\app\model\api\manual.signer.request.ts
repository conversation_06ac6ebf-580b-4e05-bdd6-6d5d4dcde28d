import {BaseRequest} from './base.request';
import {Signer} from '../../shared/components/document-anotate/model/signer';
import {StampAnnotation} from '../stamp-annotation';

export class ManualSignerRequest extends BaseRequest {
  referenceNo: string;
  psreCode: string;
  documentName: string;
  documentDate: string;
  peruriDocType: string;
  isAutomaticStamp: string;
  paymentType: string;
  stampingLocations: StampAnnotation[];
  signers: Signer[];
  documentFile: string;
  isSequence: string;
  officeCode: string;
  regionCode: string;
  businessLineCode: string;
  useSignQr: string;
}
