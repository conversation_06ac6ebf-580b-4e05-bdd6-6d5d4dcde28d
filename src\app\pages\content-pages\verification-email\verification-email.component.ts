import {Component, Input, OnInit, Output, ViewEncapsulation} from '@angular/core';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, Validators} from '@angular/forms';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import {Router} from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { ForgotPasswordRequest } from 'app/shared/dto/password/forgot-password.request';
import { VerifyResetCodeRequest } from 'app/shared/dto/password/verify-reset-code.request';
import { ForgotPasswordResponse } from 'app/shared/dto/password/forgot-password.response';
import { VerifyResetCodeResponse } from 'app/shared/dto/password/verify-reset-code.response';
import {SendOtpByEmailRequest} from '../../../shared/dto/otp/send-otp-by-email.request';
import {GlobalService} from '../../../shared/data/global.service';
import {CheckOtpByEmailRequest} from '../../../shared/dto/otp/check-otp-by-email.request';
import {PathConstant} from '../../../shared/constant/PathConstant';
import {CommonConstant} from '../../../shared/constant/common.constant';
import {environment} from '../../../../environments/environment';
import { DeviceDetectorService } from 'ngx-device-detector';
import { THIS_EXPR } from '@angular/compiler/src/output/output_ast';

@Component({
  selector: 'app-verification-email',
  templateUrl: './verification-email.component.html',
  styleUrls: ['./verification-email.component.scss']
  
})
export class VerificationEmailComponent implements OnInit {
  @Input() email: string;
  @Input() phone: string;
  @Input() verificationType: string;
  @Input() fullname: string;
  @Input() msg: string;
  @Input() vendorCode: string;
  @Input() defaultOtpSending: string;
  @Input() notifType: string;
  @Input() defaultOtpSendingOptions: string;
  @Input() otpSendingOptions: string[];
  templateForm: any;
  recepient: string;

  
  isMobile = false;
  isSMS = false;
  isWA = false;
  isEmail = false;
  isOTPSelect = true;
  isResendOtpSelect = false;
  sendingMediaOtp = '';
  default:any;
  oldEmail: string;

  timeLeft = -2;
  interval;

  swal = swalFunction;

  constructor(public activeModal: NgbActiveModal, private formBuilder: FormBuilder, private toastrService: ToastrService,
              private router: Router, private http: HttpClient, private global: GlobalService, private deviceService: DeviceDetectorService) {
  }

  ngOnInit(): void {
    if (this.global.msg) {
      this.msg = this.global.msg;
      var timeLeft = localStorage.getItem('timeLeft');
      var oldEmail = localStorage.getItem('oldEmail');
      localStorage.clear();

      console.log(oldEmail);

      localStorage.setItem('timeLeft', timeLeft);
      localStorage.setItem('oldEmail', oldEmail);
      console.log(localStorage.getItem('oldEmail'))
    }

    this.templateForm = this.formBuilder.group({
      otp: ['', Validators.required]
    });
    if (localStorage.getItem('timeLeft') != null) {
      this.timeLeft = Number(localStorage.getItem('timeLeft'));
      this.startTimer();
    }
    if (localStorage.getItem('oldEmail') != null) {
      oldEmail = localStorage.getItem('oldEmail');
      this.recepient = oldEmail;
      // Compare string case insensitive, 0 means equals
      if ((this.phone?.toUpperCase() ?? "") === oldEmail.toUpperCase() || (this.email?.toUpperCase() ?? "") === oldEmail.toUpperCase()) {
        this.checkUserActivationForgetPassword();
        this.isOTPSelect = false;
      } else {
        if (this.notifType == 'EMAIL') {
          if(this.isResendOtpSelect == false) {
            this.isOTPSelect = false;
            this.sendOtp();
          }
        } else{
          if (this.defaultOtpSendingOptions === 'WA') {
            console.log('WA CHECK');
            this.default = 'WA'
          } else if (this.defaultOtpSendingOptions === 'SMS') {
            console.log('SMS CHECK');
            this.default = 'SMS'
          } else if (this.defaultOtpSendingOptions === 'EMAIL') {
            console.log('EMAIL CHECK');
            this.default = 'EMAIL';
          }
          
          this.isWA = this.otpSendingOptions.includes('WA');
          this.isSMS = this.otpSendingOptions.includes('SMS');
          this.isEmail = this.otpSendingOptions.includes('EMAIL');
          
          if (!this.otpSendingOptions.includes(this.default)) {
            this.default = 'SMS';
          } 

          this.timeLeft = null;
        }
      }
    }  else {
      this.checkUserActivationForgetPassword();
    }
    
    console.log(this.timeLeft);

  }

  checkUserActivationForgetPassword() {
      
    if (this.notifType == 'EMAIL') {
      if(this.isResendOtpSelect == false) {
        this.isOTPSelect = false;
        this.sendOtp();
      }
    } else {
      if (this.defaultOtpSendingOptions === 'WA') {
        console.log('WA CHECK');
        this.default = 'WA'
      } else if (this.defaultOtpSendingOptions === 'SMS') {
        console.log('SMS CHECK');
        this.default = 'SMS'
      } else if (this.defaultOtpSendingOptions === 'EMAIL') {
        console.log('EMAIL CHECK');
        this.default = 'EMAIL';
      }
      
      this.isWA = this.otpSendingOptions.includes('WA');
      this.isSMS = this.otpSendingOptions.includes('SMS');
      this.isEmail = this.otpSendingOptions.includes('EMAIL');
      
      this.logOptions();
  
      if (!this.otpSendingOptions.includes(this.default)) {
        this.default = 'SMS';
      }
    }
  }

  logOptions() {
    if (this.isWA) {
      console.log('WA OPTION');
    }
    if (this.isSMS) {
      console.log('SMS OPTION');
    }
    if (this.isEmail) {
      console.log('EMAIL OPTION');
    }
  }

  dismiss() {
    this.activeModal.dismiss('0');
    clearInterval(this.interval);
    localStorage.setItem('timeLeft', this.timeLeft.toString());
    if (this.default === 'SMS' || this.default === 'WA') {
      localStorage.setItem('oldEmail', this.phone);
    } else {
      localStorage.setItem('oldEmail', this.email);
    }
  }

  openSuccessPopup() {
    this.toastrService.success(`Verifikasi OTP Email berhasil`, null, {
      positionClass: 'toast-top-right'
    });
  }

  openWrongCodePopup() {
    this.swal.Error('Kode OTP tidak sesuai!');
  }

  changeModalForgotPassword() {
    this.isOTPSelect = false;
    this.sendingMediaOtp = this.default;
    this.sendOtp();
    console.log("aaa" + this.sendingMediaOtp);
  }

  onClickSMS() {
    this.default = 'SMS';
    console.log(this.default);
  }
  
  onClickWA() {
    this.default = 'WA';
    console.log(this.default);
  }

  onClickEMAIL() {
    this.default = 'EMAIL';
    console.log(this.default);
  }

  onSubmit() {
    if (this.verificationType && this.verificationType === 'resetCode') {
      const request = new VerifyResetCodeRequest();
      request.loginId = this.recepient;
      request.resetCode = this.templateForm.controls.otp.value;
      request.audit = {
        callerId: this.recepient
      };

      this.http.post<VerifyResetCodeResponse>(URLConstant.VerifyResetCode, request).subscribe(
        (response) => {
          if (response.status.code !== 0) {
            this.swal.Error(response.status.message);
            return;
          }
          this.openSuccessPopup();
          this.activeModal.close(request.resetCode);

        }
      );
    } else if (this.verificationType && this.verificationType === CommonConstant.VERIF_TYPE_INV) {
      const request = new CheckOtpByEmailRequest();
      request.audit = { callerId: this.email };
      request.loginId = this.email;
      request.otpCode = this.templateForm.controls.otp.value;
      request.msg = this.msg;
      this.recepient = this.email;

      this.http.post(URLConstant.CheckOtpByEmailInvitation, request).subscribe(
        (response) => {
          if (response['status']['code'] !== 0) {
            this.openWrongCodePopup();
            console.log('Error', response);
            return;
          }

          this.openSuccessPopup();
          this.activeModal.close(response);
        }
      );

    } else {
      let urlCheclOtpByEmail;
      const request = new CheckOtpByEmailRequest();
      request.audit = { callerId: this.email };
      request.loginId = this.email;
      request.otpCode = this.templateForm.controls.otp.value;
      this.recepient = this.email;

      if (this.global.msg) {
        request.msg = this.global.msg;
        urlCheclOtpByEmail = URLConstant.CheckOtpByEmailEmbed;
      } else {
        urlCheclOtpByEmail = URLConstant.CheckOtpByEmail;
      }

      this.http.post(urlCheclOtpByEmail, request).subscribe(
        (response) => {
          if (response['status']['code'] !== 0) {
            this.openWrongCodePopup();
            console.log('Error', response);
            return;
          }

          this.openSuccessPopup();
          this.activeModal.close(response);
        }
      );
    }
  }

  sendOtp() {
    this.timeLeft = -2;

    if (this.verificationType && this.verificationType === 'resetCode') {
      const request = new ForgotPasswordRequest();
      if (this.default === 'SMS' || this.default === 'WA') {
        request.loginId = this.phone;
        console.log(this.phone);
      } else {
        request.loginId = this.email;
        console.log(this.email);
      }
      localStorage.setItem('oldEmail', request.loginId);
      this.recepient =  localStorage.getItem('oldEmail');
      request.audit = {
        callerId: this.recepient
      };
      request.sendingPointOption = this.default;

      this.http.post<ForgotPasswordResponse>(URLConstant.ForgotPassword, request).subscribe(
        (response) => {
          if (response.status.code !== 0) {
            if (response.status.code === 1051) {
              this.swal.ErrorWithRedirect('Tidak dapat request reset kode akses, silahkan menunggu permintaan tanda tangan dikirim.', PathConstant.LOGIN);
              localStorage.clear();
            }else{
              this.swal.Error(response.status.message);
              this.activeModal.dismiss('0');
              localStorage.clear();
            }
            return;
          }
          console.log("timeleft" + this.timeLeft);
          this.email = response.recipient;
          this.timeLeft = response.durationResendOTP;
          console.log("timeleft" + this.timeLeft);
          this.startTimer();
        }
      );
    } else if (this.verificationType && this.verificationType === CommonConstant.VERIF_TYPE_INV) {
      const request = new SendOtpByEmailRequest();
      request.audit = { callerId: this.email };
      request.loginId = this.email;
      request.msg = this.msg;
      this.recepient = this.email;
      this.http.post(URLConstant.SendOtpByEmailInvitation, request).subscribe(
        (response) => {
          if (response['status']['code'] !== 0) {
            console.log('Error', response);
            return;
          }
          console.log("timeleft" + this.timeLeft);
          this.timeLeft = response['durationResendOTP'];
          console.log("timeleft" + this.timeLeft);
          this.startTimer();
        }
      );

    } else if (!this.verificationType || this.verificationType !== 'resetCode') {
      let urlSendOtpByEmail;
      const request = new SendOtpByEmailRequest();
      request.audit = { callerId: this.email };
      request.loginId = this.email;
      this.recepient = this.email;
      if (this.global.msg) {
        request.msg = this.global.msg;
        request.fullname = this.fullname;
        request.vendorCode = this.vendorCode;
        urlSendOtpByEmail = URLConstant.SendOtpByEmailEmbed;
      } else {
        urlSendOtpByEmail = URLConstant.SendOtpByEmail;
      }

      this.http.post(urlSendOtpByEmail, request).subscribe(
        (response) => {
          if (response['status']['code'] !== 0) {
            console.log('Error', response);
            return;
          }
        }
      );
    }
    clearInterval(this.interval);
    this.isResendOtpSelect = false;
  }

  startTimer() {
    this.interval = setInterval(() => {
      if (this.timeLeft > 1) {
        this.timeLeft--;
      } else {
        this.timeLeft = -1;
        this.isResendOtpSelect = true;
        console.log("resendotpsecet ")
      }
    }, 1000);
  }
}
