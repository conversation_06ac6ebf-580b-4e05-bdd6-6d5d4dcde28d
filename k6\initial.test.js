import http from "k6/http";
import { check, sleep } from "k6";

const SERVICE_URL = "http://gdkwebsvr.ad-ins.com:7021/adimobile/esign/services/";
const SLEEP_DURATION = 0.25;
const USERNAME   = "<EMAIL>";
const PASSWORD   = "supersupeR4!";
const USERNAME_2 = "<EMAIL>";

// Test setup
export let options = {
  vus: 1,
  duration: '120s'
};

// Test scenario
export default function () {
  const params = {
    headers: {
      'Content-Type': 'application/json'
    }
  };

  // Random e-mail suffix
  let randomInt = Math.floor(Math.random() * 1000000);

  // Login request as BM
  const login_response = http.post('http://gdkwebsvr.ad-ins.com:7021/adimobile/esign/oauth/token?client_id=frontend&grant_type=password&username=<EMAIL>&password=supersupeR4!', {}, params);
  check(login_response, {
    'is status 200': (r) => r.status === 200,
    'is api key present': (r) => r.json().hasOwnProperty('access_token')
  });
  params.headers['Authorization'] = `Bearer ${login_response.json()['access_token']}`;
  sleep(SLEEP_DURATION);

  // Get User Profile (user/s/profiles)
  const profile_request = JSON.stringify({
    username: USERNAME,
    audit: { callerId: USERNAME }
  });

  const profile_response = http.post(`${SERVICE_URL}user/s/profiles`, profile_request, params);
  check(profile_response, {
    'get user profile status code is 200': (r) => r.status === 200,
    'is user present': (r) => r.json().hasOwnProperty('user')
  });
  sleep(SLEEP_DURATION);

  // Inquiry Document
  const inquiry_request = JSON.stringify({
    inquiryType: 'list',
    page: 1,
    tenantCode: 'WOMF',
    customerName: '',
    refNumber: '',
    requestedDateStart: '',
    requestedDateEnd: '',
    completedDateStart: '',
    completedDateEnd: '',
    docType: '',
    transactionStatus: '',
    audit: { callerId: USERNAME }
  });

  const inquiry_response = http.post(`${SERVICE_URL}document/s/inquiry`, inquiry_request, params);
  check(inquiry_response, {
    'inquiry documents status code is 200': (r) => r.status === 200,
    'inquiry is data found': (r) => r.json().hasOwnProperty('totalResult') && r.json()['totalResult'] > 0
  });
  sleep(SLEEP_DURATION);

  // Login request as LEGAL
  const login_asLegal_response = http.post('http://gdkwebsvr.ad-ins.com:7021/adimobile/esign/oauth/token?client_id=frontend&grant_type=password&username=<EMAIL>&password=password', {}, params);
  params.headers['Authorization'] = `Bearer ${login_asLegal_response.json()['access_token']}`;
  sleep(SLEEP_DURATION);

  // Get Document Template
  const list_template = JSON.stringify({
    documentTemplateCode: '',
    documentTemplateName: '',
    isActive: '',
    page: 1,
    tenantCode: 'WOMF',
    audit: { callerId: USERNAME_2 }
  });

  const list_template_response = http.post(`${SERVICE_URL}document/s/listDocumentTemplate`, list_template, params);
  check(list_template_response, {
    'list templates status code is 200': (r) => r.status === 200,
    'list templates is data found': (r) => r.json().hasOwnProperty('totalResult') && r.json()['totalResult'] > 0
  });
  sleep(SLEEP_DURATION);

  // Add Document Template
  const add_template = JSON.stringify({
    documentTemplateCode: `K6${randomInt}`,
    documentTemplateDescription: `Template Document K6 version ${randomInt}`,
    documentTemplateName: `Document Template ${randomInt}`,
    isActive: 1,
    isSequence: 0,
    isSignLocOnly: 0,
    numberOfPage: 1,
    paymentSignTypeCode: 'DOC',
    signer: [
      {
        signTypeCode: 'TTD',
        signerTypeCode: 'CUST',
        transform: 'translate3d(30px, 578px, 0px) translate3d(24px, 24px, 0px)',
        signPage: 1,
        signLocation: {
          llx: 55,
          lly: 173.89,
          urx: 185,
          ury: 238.89
        }
      }
    ],
    documentExample: '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',
    tenantCode: 'WOMF',
    audit: {callerId: USERNAME_2}
  });

  const add_template_response = http.post(`${SERVICE_URL}document/s/addDocumentTemplate`, add_template, params);
  console.log('Add Template', JSON.stringify(add_template_response.json()));
  check(add_template_response, {
    'add document template status code is 200': (r) => r.status === 200,
    'is insert new document template success': (r) => r.json()['status']['code'] === 0
  });
  sleep(SLEEP_DURATION);

  // Update Document Template
  const update_template = JSON.stringify({
    documentTemplateCode: `K600001`,
    documentTemplateDescription: `Template Document K6 version ${randomInt}`,
    documentTemplateName: `Document Template ${randomInt}`,
    isActive: 1,
    isSignLocOnly: 0,
    paymentSignTypeCode: 'DOC',
    documentExample: '',
    tenantCode: 'WOMF',
    audit: {callerId: USERNAME_2}
  });

  const update_template_response = http.post(`${SERVICE_URL}document/s/updateDocumentTemplate`, update_template, params);
  check(update_template_response, {
    'update document template status code is 200': (r) => r.status === 200,
    'is update document template success': (r) => r.json()['status']['code'] === 0
  });
  sleep(SLEEP_DURATION);
}
