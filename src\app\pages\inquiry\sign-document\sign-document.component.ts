import { AfterViewInit, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { SignDocumentResponse } from 'app/model/api/sign.document.response';
import { SignDocumentRequest } from 'app/model/api/sign.document.request';
import { ToastrService } from 'ngx-toastr';
import { GlobalService } from 'app/shared/data/global.service';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GetTenantLivenessRequest } from 'app/shared/dto/liveness/get-tenant-liveness.request';
import { GetTenantLivenessResponse } from 'app/shared/dto/liveness/get-tenant-liveness.response';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {LivenessCheckComponent} from '../../../shared/components/liveness-check/liveness-check.component';
import {NgxSpinnerService} from 'ngx-spinner';
import {LivenessGuidesComponent} from '../../../shared/components/liveness-guides/liveness-guides.component';
import { CheckStatusDocumentService } from 'app/services/check-status-document.service';
import {Vendor} from '../../../shared/constant/vendor';
import { GetDocumentSignDetailsRequest } from 'app/model/api/getDocumentSignDetails.request';
import { GetDocumentSignDetailsResponse } from 'app/model/api/getDocumentSignDetails.response';
import { Document } from 'app/model/document';
import { CheckDocumentBeforeSigningRequest } from 'app/model/api/checkDocumentBeforeSigning.request';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { Location } from '@angular/common';


@Component({
  selector: 'app-sign-document',
  templateUrl: './sign-document.component.html',
  styleUrls: ['./sign-document.component.scss']
})
export class SignDocumentComponent implements OnInit, AfterViewInit {
  url: string;
  documentId: string;
  refNumber: string;
  documents: Document[] = [];
  // START: Workaround bulksign page kosong
  skipBulksignCheck: string;
  // END: Workaround bulksign page kosong

  signProcess:any[];
  isAllSignValid = true;


  isShow = false;

  constructor(private http: HttpClient, private activeRoute: ActivatedRoute, private router: Router,
              private cdr: ChangeDetectorRef, private toastrService: ToastrService, private readonly global: GlobalService,
              private modalService: NgbModal, private spinner: NgxSpinnerService, private checkStatusDocument: CheckStatusDocumentService, private location: Location) {
    this.activeRoute.queryParams.subscribe(
      (params) => {
        if (params['url'] != null) {
          this.url = params['url'];
        }
        if (params['id'] != null) {
          this.documentId = params['id'];
        }

        localStorage.setItem('sign', JSON.stringify({url: window.location['href'], docId: params['id'], email: this.global.user?.email}));
      }
    );
    if (this.router.getCurrentNavigation().extras.state) {
      const state = this.router.getCurrentNavigation().extras.state;
      this.refNumber = state.refNumber;

      // START: Workaround bulksign page kosong
      this.skipBulksignCheck = state.skipBulksignCheck;
      // END: Workaround bulksign page kosong

      console.log(this.refNumber);
    }
  }

  async ngOnInit() {
    if (this.global.user == null) {
      await this.router.navigate([PathConstant.LOGIN], { queryParams: { isReturnToSign: true, id: this.documentId } });
    } else if (this.url == null) {
      const request: SignDocumentRequest = new SignDocumentRequest();
      const signDocUrl = URLConstant.SignDocument;
      request.email = this.global.user.loginId;
      request.documentId = this.documentId;

      // START: Workaround bulksign page kosong
      if (this.skipBulksignCheck) {
        request.skipBulksignCheck = this.skipBulksignCheck;
      }
      // END: Workaround bulksign page kosong

       //check send document status
      const checkDocStatus = await this.checkStatusDocument.validate(this.documentId, this.global.msg, this.router.url, this.refNumber);

      if (checkDocStatus.documentSendStatus === '1') {
        //check Document Before Signing
        this.checkDocBeforeSign();
        //penjagaan untuk jika semua doc yang dipilih signing processnya 1 = tidak bisa ttd
        this.checkIfAllInvalid();
    
        await this.http.post<SignDocumentResponse>(signDocUrl, request).toPromise().then(
          async (response) => {
            if (response.status.code === 0 && response.vendorCode === Vendor.TekenAja && response.url != "") {
              window.open(response.url, '_blank');
            }

            if (response.status.code === 0) {
              if (response.docs != null && response.url != null) {
                const extras: NavigationExtras = {
                  state: {
                    data: response.docs,
                    vendorCode:response.vendorCode,
                  }
                };
                this.router.navigate([PathConstant.INQUIRY_BULK_SIGN], extras);
              } else if (response.status.code === 0 && response.vendorCode === Vendor.VIDA) {
                if (response.docs != null) {
                  const extras: NavigationExtras = {
                    state: {
                      data: response.docs,
                      vendorCode:response.vendorCode,
                    }
                  };
                  this.router.navigate([PathConstant.INQUIRY_BULK_SIGN], extras);
                } else{
                  return this.goToBulkSignVida(this.documentId);
                }
              } else if (response.status.code === 0 && response.vendorCode === Vendor.PRIVY) {
                if (response.docs != null) {
                  const extras: NavigationExtras = {
                    state: {
                      data: response.docs,
                      vendorCode:response.vendorCode,
                    }
                  };
                  this.router.navigate([PathConstant.INQUIRY_BULK_SIGN], extras);
                } else{
                  return this.goToBulkSignVida(this.documentId);
                }
              } else if (response.url != null) {
                const livenessRequest: GetTenantLivenessRequest = new GetTenantLivenessRequest;
                livenessRequest.tenantCode = this.global.user.role.tenantCode;
                const getTenantLivenessUrl = URLConstant.GetTenantLiveness;

                await this.http.post<GetTenantLivenessResponse>(getTenantLivenessUrl, livenessRequest).toPromise().then(
                  async (responseLiveness) => {
                    if (responseLiveness.status.code === 0) {
                      if (responseLiveness.statusFaceVerify === CommonConstant.STATUS_ACTIVE) {
                        this.modalService.open(LivenessGuidesComponent, { size: 'sm', backdrop: 'static', keyboard: false })
                          .dismissed.subscribe(() => {
                            const liveness = this.modalService.open(LivenessCheckComponent, { backdrop: 'static', keyboard: false });
                            liveness.componentInstance.refNumber = this.refNumber;
                            liveness.result.then(
                              () => {
                                this.setUrlSign(response.url);
                              }
                            );
                          });
                      } else {
                        this.setUrlSign(response.url);
                      }
                    }
                  }
                );
              }
            } else if (response.status.code === 20 && response.register === '1') {
              const extras: NavigationExtras = {
                state: {
                  data: {
                    tenantCode: response.tenantCode,
                    vendorCode: response.vendorCode
                  }
                }
              };

              this.toastrService.info('User belum melakukan registrasi, silahkan melakukan register terlebih dahulu', null, {
                positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
              });

              if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
                this.router.navigate([PathConstant.EMBED_REGISTER], extras);
              } else {
                this.router.navigate([PathConstant.REGISTER], extras);
              }
            } else if (response.status.code === 20) {
              const extras: NavigationExtras = {
                queryParams: {
                  uri: this.global.user.loginId,
                  tenantCode: response.tenantCode,
                  vendorCode: response.vendorCode
                }
              };

              this.toastrService.info('User belum melakukan aktivasi, silakan melakukan aktivasi terlebih dahulu', null, {
                positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
              });
              if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
                this.router.navigate([PathConstant.EMBED_ACTIVATION], extras);
              } else {
                this.router.navigate([PathConstant.ACTIVATION], extras);
              }
            } else if (response.status.code === 10) {
              this.toastrService.info('Dokumen sudah ditandatangani oleh user', null, {
                positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
              });
              const extras: NavigationExtras = {
                queryParams: {
                  msg: this.global.msg
                }
              };
              if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
                this.router.navigate([PathConstant.EMBED_DASHBOARD], extras);
              } else {
                this.router.navigate([PathConstant.DASHBOARD]);
              }
            } else if (response.status.code === 30) {
              this.toastrService.info('Dokumen sudah tidak aktif lagi', null, {
                positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
              });
              const extras: NavigationExtras = {
                queryParams: {
                  msg: this.global.msg
                }
              };
              if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
                this.router.navigate([PathConstant.EMBED_DASHBOARD], extras);
              } else {
                this.router.navigate([PathConstant.DASHBOARD]);
              }
            }

            if (this.global.user.pathSrc !== CommonConstant.PATH_SRC_EMBED) {
            }
          }, (err) => {
            this.toastrService.error(err.message, null, { positionClass: 'toast-top-right' });
          }
        );
      }
    
    } else if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED && this.url != null) {
      window.location.href = this.url;
      this.isShow = true;
    } else if (this.url !== null) {
      const livenessRequest: GetTenantLivenessRequest = new GetTenantLivenessRequest;
      const getTenantLivenessUrl = URLConstant.GetTenantLiveness;
      livenessRequest.tenantCode = this.global.user.role.tenantCode;

      await this.http.post<GetTenantLivenessResponse>(getTenantLivenessUrl, livenessRequest).toPromise().then(
        async (responseLiveness) => {
          if (responseLiveness.status.code === 0) {
            if (responseLiveness.statusFaceVerify === CommonConstant.STATUS_ACTIVE) {
              this.modalService.open(LivenessGuidesComponent, { size: 'sm', backdrop: 'static', keyboard: false })
                .dismissed.subscribe(() => {
                const liveness = this.modalService.open(LivenessCheckComponent, { backdrop: 'static', keyboard: false });
                liveness.componentInstance.refNumber = this.refNumber;
                liveness.result.then(
                  () => {
                    this.setUrlSign(this.url);
                  }
                );
              });
            } else {
              this.setUrlSign(this.url);
            }
          }
        }
      );
    }
    
  }

  goToDashboard() {
    this.router.navigate([PathConstant.DASHBOARD]);
  }

  setUrlSign(url) {
    this.url = url;
    if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      window.location.href = this.url;
    }
    this.spinner.hide();
    this.isShow = true;
  }

  ngAfterViewInit(): void {

  }
  checkDocBeforeSign(){
    const req = new CheckDocumentBeforeSigningRequest();
    let url;
    if(this.global.msg && this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
      req.msg = this.global.msg;
      url = URLConstant.checkDocumentBeforeSigningEmbedV2;
    } 
    else{
      req.loginId = this.global.user.loginId;
      url = URLConstant.checkDocumentBeforeSigning;
    }

    req.listDocumentId.push(this.documentId);

    this.http.post(url, req).toPromise().then(
      (response) => {
        if (this.router.url.includes('/embed/') && this.router.url.includes('/V2/') &&response["status"]["code"] === 0 && response["listCheckDocumentBeforeSigning"]["0"]["signingProcess"] === "2"){
          swalFunction.Error("Dokumen harus ditandatangani oleh user lain terlebih dahulu.");
          return this.location.back();
        }
        if (response["status"]["code"] === 0 && response["listCheckDocumentBeforeSigning"]["0"]["signingProcess"] === "2") {
          swalFunction.Error("Dokumen harus ditandatangani oleh user lain terlebih dahulu.");
         this.router.navigate([PathConstant.DASHBOARD])
        }
        if (response["status"]["code"] === 0) {
          this.signProcess = response["listCheckDocumentBeforeSigning"];
          return;
        }
      }
    );
  }

  async checkIfAllInvalid(){
    let total = 0;
    for(let j = 0; j < this.signProcess.length; j++) {
      if(this.signProcess[j].signingProcess === '1'){
        total ++;
      }
    }
    if(total === this.signProcess.length){
      this.isAllSignValid = false;
      swalFunction.Error('Semua dokumen yang anda pilih sedang dalam proses tanda tangan! Silahkan pilih dokumen lain.');
      this.goToDashboard();
    }
  }

  async goToBulkSignVida(idDoc) {
    const detailsRequest: GetDocumentSignDetailsRequest = new GetDocumentSignDetailsRequest();
    detailsRequest.documentID = idDoc;
    detailsRequest.loginId = this.global.user.loginId;
    await this.http.post<GetDocumentSignDetailsResponse>(URLConstant.getDocumentSignDetails,  detailsRequest).toPromise().then(
      async (response) => {
        if (response.status.code === 0) {
          this.documents = response.listDocument;
        }
      }
    );
    
    const extras: NavigationExtras = {
      state: {
        data: this.documents
      }
    };

    if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      this.router.navigate([PathConstant.EMBED_BULK_SIGN], extras);
    } else {
      this.router.navigate([PathConstant.INQUIRY_BULK_SIGN], extras);
    }
  }
}
