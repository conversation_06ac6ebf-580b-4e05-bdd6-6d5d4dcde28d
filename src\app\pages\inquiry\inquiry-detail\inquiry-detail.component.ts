import {Component, OnInit, ViewEncapsulation} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {Document} from '../../../model/document';
import {ColumnMode, SelectionType} from '@swimlane/ngx-datatable';
import {ActivatedRoute, NavigationExtras, Router} from '@angular/router';
import {Inquiry} from '../../../model/inquiry';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {SignerComponent} from '../modal/signer/signer.component';
import {DOCUMENTS, INQUIRY_DOCUMENT} from '../inquiry.data';
import {GlobalService} from '../../../shared/data/global.service';
import {Const} from '../../../shared/data/consts';
import {Location} from '@angular/common';

@Component({
  selector: 'app-inquiry-detail',
  templateUrl: './inquiry-detail.component.html',
  styleUrls: ['./inquiry-detail.component.scss', '../../../../assets/sass/libs/datepicker.scss',
    '../../../../assets/sass/libs/select.scss', '../../../../assets/sass/libs/datatables.scss'],
  encapsulation: ViewEncapsulation.None
})
export class InquiryDetailComponent implements OnInit {

  searchForm: FormGroup;
  selectedStatus = {code: '0', name: 'Need Sign'};

  status = [
    {code: '1', name: 'Complete'},
    {code: '0', name: 'Need Sign'}
  ];

  inquiry: Inquiry;
  documents: Document[] = DOCUMENTS;
  inquiries: Inquiry[] = INQUIRY_DOCUMENT;
  filteredDocuments: Document[];

  public ColumnMode = ColumnMode;
  public chkBoxSelected = [];
  public SelectionType = SelectionType;
  public selectedAll = false;
  public params;

  private src: string;

  constructor(private router: Router, private modalService: NgbModal, private location: Location,
              private activeRoute: ActivatedRoute, private global: GlobalService) {
    this.activeRoute.queryParams.subscribe(params => {
      this.params = params;
      console.log('Params', params);
    });

    if (this.router.getCurrentNavigation().extras.state) {
      const state  = this.router.getCurrentNavigation().extras.state;
      this.inquiry = state.data;
      this.src     = state.src;
    }

    this.activeRoute.queryParams.subscribe(params => {
      if (params?.contractRef) {
        this.inquiry = this.inquiries.find(x => x.refNo === params.contractRef);
      }
    });
    console.log('Route Data', this.activeRoute.snapshot.data);
  }

  ngOnInit(): void {
    if (this.src === 'embed') {
      localStorage.clear();
    }
    this.filter();
  }

  filter() {
    //this.filteredDocuments = this.documents.filter(x => x.status === this.selectedStatus.name);
  }

  /**
   * customChkboxOnSelect
   *
   * @param { selected }
   */
  customChkboxOnSelect({ selected }) {
    this.chkBoxSelected.splice(0, this.chkBoxSelected.length);
    this.chkBoxSelected.push(...selected);
  }

  selectCheckboxheader() {
    // this.selectedAll = !this.selectedAll;
    // console.log('Select All', this.selectedAll);

    // if (this.selectedAll) {
    //   this.documents.forEach(select => {
    //     if (select.status !== 'Complete') {
    //       this.chkBoxSelected.push(select);
    //     }
    //   });

    //   console.log('ChkboxAll', this.chkBoxSelected);
    // } else {
    //   this.chkBoxSelected = [];
    // }
  }

  onChangeStatus($event) {
    this.chkBoxSelected = [];
    this.selectedStatus = $event;
    this.filter();
    console.log('Event', this.selectedStatus);
  }

  canSign() {
    if (this.params?.role === 'admin') {
      return false;
    }

    if (this.global.user && this.global.user.roles[0].roleCode === Const.role_admin) {
      return false;
    }

    return true;
  }

  signing(document: Document) {
    const extras: NavigationExtras = {
      state: {
        data: document
      }
    };

    this.router.navigate(['signature'], extras);
  }

  openModal() {
    this.modalService.open(SignerComponent);
  }

  bulkSign() {
    const extras: NavigationExtras = {
      state: {
        data: this.chkBoxSelected
      }
    };

    if (this.src === 'embed') {
      this.router.navigate(['embed', 'bulk-sign'], extras);
    } else {
      this.router.navigate(['inquiry', 'detail', 'bulkSign'], extras);
    }
  }

  doBack() {
    this.location.back();
  }

}
