import {CdkDragDrop, moveItemInArray} from '@angular/cdk/drag-drop';
import { Component, OnInit } from '@angular/core';
import { GetPSrEPriorityRequest } from 'app/model/api/get-psre-priority.request';
import { UpdatePSrEPriorityRequest } from 'app/model/api/update-psre-priority.request';
import { PSrEPriority } from 'app/model/psre-priority';
import { DataService } from 'app/services/api/data.service';
import { GlobalService } from 'app/shared/data/global.service';
import * as swalFunction from '../../shared/data/sweet-alerts';
import { NgxSpinnerService } from 'ngx-spinner';
import { PreFormsubmitPromptComponent } from 'app/shared/components/pre-formsubmit-prompt/pre-formsubmit-prompt.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-psre-priority',
  templateUrl: './psre-priority.component.html',
  styleUrls: ['./psre-priority.component.scss']
})
export class PsrePriorityComponent implements OnInit {
  swal = swalFunction;
  vendors : PSrEPriority[] = []
  dataLoaded = false;

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.vendors, event.previousIndex, 
    event.currentIndex);
  }

  constructor(private global: GlobalService, private dataService: DataService,private spinner: NgxSpinnerService, private ngModal: NgbModal) { }

  async ngOnInit() {
    this.getVendors();
  }

  async getVendors() {
    const request = new GetPSrEPriorityRequest();
    request.tenantCode = this.global.user.role.tenantCode;

    this.dataService.getPsrePriority(request).subscribe(
      (response) => {
        if(response.status.code === 0) {
          for (const item of response.listPsrePriority) {
            this.vendors.push({
              vendorCode: item.vendorCode,
              vendorName: item.vendorName,
              priority: item.priority
            });
          }
          
        }
        this.dataLoaded = false;
      }
    );
  }

  onSubmit() {

    
    const modal = this.ngModal.open(PreFormsubmitPromptComponent);
    modal.result.then((result) => {
      if (result === 'submit') {
    const request = new UpdatePSrEPriorityRequest();
    
    request.vendor = [];

    this.vendors.forEach((vendor, index) => {
      request.vendor.push({
        vendorCode: vendor.vendorCode,
        priority: (index + 1).toString() // Menambahkan 1 ke index karena index dimulai dari 0
      });
    });

    this.dataService.updatePsrePriority(request).subscribe(
      (response) => {
        if(response.status.code === 0) {
          this.swal.Success("Success");
        }
      }
    )

  }
});}

}
