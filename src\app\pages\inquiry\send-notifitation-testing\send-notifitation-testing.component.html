<div class="row" style="margin-top: 15px">
  <div class="col-6">
    <div class="content-header" style="margin-top: 0 !important;" [innerText]="title | translate"></div>
  </div>
</div>

<div class="row match-height">
    <div class="col-12 d-flex justify-content-center align-items-center">
      <div class="card">
        <div class="card-content">
          <div class="card-body">
            <form [formGroup]="xForm" (ngSubmit)="onNext()" id="xForm" #formElement>
              <ng-container *ngIf="notifForm?.params && notifForm.params?.length > 0">
                <input *ngFor="let param of notifForm.params" type="hidden" [formControlName]="param.key"
                       [value]="param.value" [id]="param.key" />
              </ng-container>
  
              <div class="row">
                <div class="col-{{notifForm.colSize}}">
                  <app-text [form]="xForm" [question]="getQuestion('phoneNo')" [direction]="notifForm.direction" (selected)="onSelect($event)"></app-text>
                </div>
  
                <div class="col-{{notifForm.colSize}}">
                  <app-select [form]="xForm" [question]="getQuestion('messageMedia')" [options]="getOptions('messageMedia')" [direction]="notifForm.direction" (selected)="onSelect($event)"></app-select>  
                </div>
  
                <div class="col-{{notifForm.colSize}}">
                  <app-select [form]="xForm" [question]="getQuestion('notifGateway')" [options]="getOptions('notifGateway')" [direction]="notifForm.direction" (selected)="onSelect($event)"></app-select>
                </div>
                <div class="col-{{notifForm.colSize}}">
                  <app-text [form]="xForm" [question]="getQuestion('message')" direction="vertical"></app-text>
                </div>
              </div>
  
              <div class="row">
                <div class="col-12 text-center">
                  <button class="btn btn-info" type="submit" [disabled]="xForm.invalid">Send</button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div style="margin-left: 10px;">
    <button class="btn btn-secondary" style="margin-right: 8px" (click)="getDeliveryReport()"> {{'Get Delivery Report' | translate}}</button>
  </div>
  
  <div class="modal-body">
    <app-msx-datatable [tableObj]="ListInquirySendNotificationMessageDelivery" [datasource]="datasource"></app-msx-datatable>
  </div>
  