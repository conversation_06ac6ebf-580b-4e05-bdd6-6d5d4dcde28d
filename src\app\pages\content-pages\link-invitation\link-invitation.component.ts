import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit, NgZone } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SmsDeliveryService } from 'app/services/api/sms-delivery.service';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { SmsDeliverySettingRequest } from 'app/shared/dto/sms-delivery-setting/sms-delivery-setting.request';
import { UpdateSmsDeliverySettingRequest } from 'app/shared/dto/sms-delivery-setting/update-sms-delivery-setting.request';
import * as swalFunction from '../../../shared/data/sweet-alerts';

@Component({
  selector: 'app-link-invitation',
  templateUrl: './link-invitation.component.html',
  styleUrls: ['./link-invitation.component.scss']
})
export class LinkInvitationComponent implements OnInit {
  swal = swalFunction;
  linkSettingsForm: FormGroup;
  template: any;

  constructor(private formBuilder: FormBuilder,private global: GlobalService, private location: Location, 
              private smsDeliveyService: SmsDeliveryService, private cdr: ChangeDetectorRef, private ngZone: NgZone) { }

  ngOnInit(): void {
    this.setupForm();
    this.setForm();
  }

  setupForm() {
    this.linkSettingsForm = this.formBuilder.group({
      addonInvLink: ['', Validators.required],
      addonSignLink: ['', Validators.required]
    }); 
  }

  setForm() {
    const request = new SmsDeliverySettingRequest();
    request.tenantCode = this.global.user.role.tenantCode;

    this.smsDeliveyService.getSmsDeliverySettings(request).subscribe(
      (response) => {
        console.log('response: ', response);
        if (response.status.code === 0) {      
          this.ngZone.run(() => {
            this.linkSettingsForm.patchValue({
              addonInvLink: response.addonInvLink,
              addonSignLink: response.addonSignLink
            });
  
            this.cdr.detectChanges();
          });   
        }
      }
    );
  }
  
  fieldsChangeInvite(values: any):void {
    this.linkSettingsForm.patchValue({addonInvLink: values.currentTarget.checked})
    console.log('addonInvLink Value:', this.linkSettingsForm.get('addonInvLink').value);
  }

  fieldsChangeSign(values:any):void {
    this.linkSettingsForm.patchValue({addonSignLink: values.currentTarget.checked})
    console.log('addonSignLink Value:', this.linkSettingsForm.get('addonSignLink').value);

  }

  updateLinkSetting() {
    const formData = this.linkSettingsForm.getRawValue();
    console.log('formData:', formData);

    const request = new UpdateSmsDeliverySettingRequest();
    request.addonInvLink = formData.addonInvLink
    request.addonSignLink = formData.addonSignLink

    this.smsDeliveyService.updateSmsDeliverySettings(request).subscribe(
      (response) => {
        if (response.status.code === 0) {
          this.swal.Success('Link invitation setting has been successfully changed');
        } else {
          return;
        }
      }
    );
  }

  goBack() {
    this.location.back();
  }

}