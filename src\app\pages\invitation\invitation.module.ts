import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { InvitationRoutingModule } from './invitation-routing.module';
import {EditInvitationComponent} from '../full-pages/edit-invitation/edit-invitation.component';
import {ContentPagesModule} from '../content-pages/content-pages.module';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {NgSelectModule} from '@ng-select/ng-select';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap';
import {SwiperModule} from 'ngx-swiper-wrapper';
import {PipeModule} from '../../shared/pipes/pipe.module';
import {HttpClient} from '@angular/common/http';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import {TranslateLoader, TranslateModule} from '@ngx-translate/core';
import { SharedModule } from 'app/shared/shared.module';

export function createTranslateLoader(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  declarations: [
    EditInvitationComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: createTranslateLoader,
        deps: [HttpClient]
      }
    }),
    InvitationRoutingModule,
    ContentPagesModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgbModule,
    SwiperModule,
    PipeModule,
    SharedModule,
  ]
})
export class InvitationModule { }
