<div *ngIf="!isMobile" class="row align-items-center my-3">
    <div class="col-4">
      <div class="content-header" style="margin-top: 0 !important;" translate>View Document</div>
    </div>
    <div class="col-4 text-center">
      <div class="content-header" style="margin-top: 0 !important;">{{'Ref Number' | translate}} {{refNumber}}</div>
    </div>
    <div class="col-4 text-right">
      <button class="btn btn-secondary" (click)="doBack()" translate>Back</button>
    </div>
</div>

<div *ngIf="isMobile">
  <div class="row">
    <div class="col-12">
      <h4 class="content-header" style="margin-top: 0 !important;" translate>View Document</h4>
    </div>
  </div> 
  <div class="row pt-2">
    <div class="col-12 mr-2">
      <label class="font-medium-3 mr-2" translate>Ref Number</label>
      <span class="card-title font-medium-4 font-weight-bold">{{state.refNumber}}</span>
    </div> 
   </div> 
</div>

<div *ngIf="!isMobile" class="wrapper">
  <iframe *ngIf="url" [src]="url | safe"></iframe>
</div> 

<div *ngIf="isMobile" class="row match-height mt-3">
  <div class="col-12 tab-style tab-text"> 
    <div>
      <pdf-viewer
      class="pdfViewer"
      id="pdfViewer"
      [src]="pdfBuffer"
      [rotation]="0"
      [original-size]="false"
      [show-all]="true"
      [fit-to-page]="false"
      [zoom]="1"
      [zoom-scale]="'page-width'"
      [stick-to-page]="false"
      [render-text]="true"
      [external-link-target]="'blank'"
      [autoresize]="true"
      [show-borders]="true" 
      [(page)]="pdfPageNumber" 
    ></pdf-viewer>
    </div> 
  </div>
</div>
 

<div *ngIf="isMobile">
  <div *ngIf="isSign" class="row pb-1">
    <div class="col-12 pl-4 pr-4 pb-2 pt-3">
      <button class="btn btn-primary btn-lg btn-block" (click)="onClickSign()" translate>{{signButtonLabel}}</button>
    </div>
  </div>
  <div class="card mobile-footer my-0" style="box-shadow: none;">
    <div *ngIf="roleCode === roleCust || roleCode === roleBranchManager || pathsrc.includes('embed')" class="card-body pb-2">
      <ng-container *ngFor="let act of ActionCustMobile">
        <a
           class="mr-2" 
           (click)="onClickAction(act)" 
           [title]="act.descr | translate" >
           <img [src]="act.iconSrc" class="icon-mobile" [alt]="act.descr" [title]="act.descr">
        </a>
      </ng-container>
    </div>
    <div class="row">
      <div class="col-12 pl-4 pr-4 pb-2 pt-2">
        <button class="btn btn-secondary btn-lg btn-block" (click)="doBack()" translate>Back</button>
      </div>
    </div>
  </div>
</div>