import {ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mponent, ElementRef, OnInit, ViewChild} from '@angular/core';
import {MsxView} from '../../shared/components/msx-view/models/MsxView';
import {FormModel} from '../../shared/components/ms-form/models';
import {URLConstant} from '../../shared/constant/URLConstant';
import {FormConstant} from '../../shared/components/ms-form/constants/form.constant';
import {QuestionDate, QuestionDropdown} from '../../shared/components/ms-form/questions';
import {FormGroup} from '@angular/forms';
import {CommonConstant} from '../../shared/constant/common.constant';
import {MsxFormControlService} from '../../shared/components/ms-form/msx-form-control.service';
import {TenantService} from '../../services/api/tenant.service';
import {DataRequest} from '../../model/api/data.request';
import {BalanceService} from '../../services/api/balance.service';
import * as moment from 'moment';
import {ToastrService} from 'ngx-toastr';
import {GlobalService} from '../../shared/data/global.service';
import {SubmitReconcileRequest} from '../../model/api/submit.reconcile.request';
import {JobResultService} from '../../services/api/job-result.service';


@Component({
  selector: 'app-reconsile',
  templateUrl: './reconsile.component.html',
  styleUrls: ['./reconsile.component.scss']
})
export class ReconsileComponent implements OnInit {

  view: MsxView;
  formObj: FormModel<any>;
  msxForm: FormGroup;
  serviceUrl = URLConstant.ListMonitoring;

  @ViewChild('formElement') formElement: ElementRef;

  constructor(private fcs: MsxFormControlService, private tenantService: TenantService, private global: GlobalService,
              private balanceService: BalanceService, private jobResult: JobResultService, private cdr: ChangeDetectorRef,
              private toastrService: ToastrService) {
  }

  ngOnInit(): void {
    this.initView();
    const controls = [...this.formObj.params, ...this.formObj.components];
    this.msxForm = this.fcs.toFormGroup(controls);
  }

  initView() {
    this.formObj = {
      mode: CommonConstant.MODE_ADD,
      name: 'f_reconcile_tool',
      direction: FormConstant.DIRECTION_VERTICAL,
      colSize: 6,
      components: [
        new QuestionDropdown({
          key: 'vendorCode',
          label: 'Vendor',
          placeholder: 'Select Vendor',
          serviceUrl: URLConstant.GetVendorListV2,
          options: [
            { key: '', value: 'Pilih Vendor' }
          ],
          value: '',
          args: {
            list: 'vendorList',
            key: 'code',
            value: 'name'
          },
          params: {
            vendorTypeCode: CommonConstant.VENDOR_TYPE_PSRE
          },
          required: true,
        }),
        new QuestionDropdown({
          key: 'tenantCode',
          label: 'Tenant',
          placeholder: CommonConstant.CONST_SELECT_TENANT,
          required: true,
          options: [
            { key: '', value: CommonConstant.CONST_SELECT_TENANT }
          ],
          value: ''
        }),
        new QuestionDropdown({
          key: 'balanceType',
          label: 'Balance Type',
          placeholder: CommonConstant.CONST_SELECT_SALDO_TYPE,
          required: true,
          options: [
            { key: '', value: CommonConstant.CONST_SELECT_SALDO_TYPE },
          ],
          value: ''
        }),
        new QuestionDate({
          key: 'startDate',
          label: 'Tanggal Mulai',
          placeholder: CommonConstant.FORMAT_DATE,
          required: true,
        }),
        new QuestionDate({
          key: 'endDate',
          label: 'Tanggal Akhir',
          placeholder: CommonConstant.FORMAT_DATE,
          required: true,
        }),
      ],
      params: [
        {
          key: 'loginId',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.user.loginId
        }
      ]
    };
  }

  getQuestion(key: string) {
    return this.formObj.components.find(q => q.key === key);
  }

  getOptions(key: string) {
    const question = this.getQuestion(key);
    return question.options;
  }

  onSubmit() {
    console.log('msxForm', this.msxForm.getRawValue());
    const startDate = this.parseDate(this.msxForm.get('startDate').value );
    const endDate = this.parseDate(this.msxForm.get('endDate').value);

    // Validate date input
    if (!moment(endDate).isSameOrAfter(startDate)) {
      this.toastrService.warning(
        'Input Tanggal Akhir harus lebih besar atau sama dengan dari Tanggal Mulai.',
      null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      this.msxForm.get('endDate').reset();
      this.setFocus('endDate');
      return;
    }

    const rawData = this.msxForm.getRawValue();
    const request: SubmitReconcileRequest = new SubmitReconcileRequest();
    request.vendorCode  = rawData.vendorCode;
    request.tenantCode  = rawData.tenantCode;
    request.balanceType = rawData.balanceType;
    request.startDate   = <string> this.parseDate(rawData.startDate, 0);
    request.endDate     = <string> this.parseDate(rawData.endDate, 0);
    request.loginId     = rawData.loginId;

    console.log('Submit Reconcile', request);
    this.jobResult.submitReconcile(request).subscribe(res => {
      console.log('Submit Reconcile Response', res);
      if (res.status.code === 0) {
        this.toastrService.success(res.status.message, null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }
    })
  }

  onSelect($event) {
    console.log('select event', $event);
    switch ($event.prop) {
      case 'vendorCode':
        this.fetchTenant($event.data.key);
        break;
      case 'tenantCode':
        this.getBalanceType();
        break;
      default:
        console.log('default action');
        break;
    }
  }

  onForm(form: FormGroup) {
    this.msxForm = form;
  }

  onInput($event) {
  }

  fetchTenant(vendorCode: string) {
    const request: DataRequest = new DataRequest();
    request.vendorCode = vendorCode;

    // Reset current payment type value
    this.msxForm.get('tenantCode').reset();
    this.msxForm.get('balanceType').reset();

    this.tenantService.getTenantRecon(request).subscribe(res => {
      console.log('listTenant Response', res);
      const question = this.formObj.components.find(c => c.key === 'tenantCode');
      const defaultAnswer = [{ key: '', value: CommonConstant.CONST_SELECT_TENANT }];
      let options = [];

      for (const tenant of res.listTenantRekon) {
        const option = {key: tenant.tenantCode, value: tenant.tenantName};
        options.push(option);
      }

      options = defaultAnswer.concat(options);
      question.options = options;
      // this.listTenant = question.options;
      console.log(question.key, question.options);

      if (res.listTenantRekon.length === 1) {
        this.msxForm.get('tenantCode').setValue(res.listTenantRekon[0].tenantCode);
        this.getBalanceType();
      }

      this.cdr.detectChanges();
    })
  }

  getBalanceType() {
    const vendorCode = this.msxForm.get('vendorCode').value;
    const tenantCode = this.msxForm.get('tenantCode').value;

    // Reset current balance type
    this.msxForm.get('balanceType').reset();

    const request: DataRequest = new DataRequest();
    request.vendorCode = vendorCode;
    request.tenantCode = tenantCode;

    console.log('Request Get BalanceType', request);

    this.balanceService.getBalanceType(request).subscribe(res => {
      console.log('listBalanceType Response', res);
      const question = this.formObj.components.find(c => c.key === 'balanceType');
      const defaultAnswer = [{ key: '', value: CommonConstant.CONST_SELECT_SALDO_TYPE }];
      let options = [];

      for (const balanceType of res.listBalanceType) {
        const option = {key: balanceType.balanceTypeCode, value: balanceType.balanceTypeName};
        options.push(option);
      }

      options = defaultAnswer.concat(options);
      question.options = options;
      console.log(question.key, question.options);

      if (res.listBalanceType.length === 1) {
        this.msxForm.get('balanceType').setValue(res.listBalanceType[0].balanceTypeCode);
      }

      this.cdr.detectChanges();
    })
  }

  setFocus(name) {
    const ele = this.formElement.nativeElement[name];
    if (ele) {
      ele.focus();
    }
  }

  parseDate(dateObj, returnType?: number) {
    const date  = new Date(dateObj['year'], dateObj['month'] - 1, dateObj['day']);
    return returnType === 1 ? date : moment(date).format('YYYY-MM-DD');
  }
}
