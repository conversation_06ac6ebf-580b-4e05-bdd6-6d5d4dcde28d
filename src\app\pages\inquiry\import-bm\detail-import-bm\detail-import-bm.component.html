<div *ngIf="!isMobile" class="row" style="margin-top: 15px; margin-bottom: 15px;">
    <div class="col-6">
      <div class="content-header" style="margin-top: 0 !important;" translate>Import Data Details</div>
    </div>
    <div class="col-6 text-right">
      <a class="btn btn-secondary mr-2" (click)="goBack()" translate>Back</a>
    </div>
  </div>
  
  <div *ngIf="isMobile">
    <div class="row" style="margin-top: 15px">
      <div class="col-12 text-left">
        <div class="content-header" style="margin-top: 0 !important;" translate>Import Data Details</div>
      </div>
    </div>
    <div class="row" style="margin-bottom: 15px;">
      <div class="col-12 text-right">
        <a class="btn btn-secondary mr-2" (click)="goToDashboard()" translate>Back</a>
      </div>
    </div>
  </div>
<app-msx-datatable [tableObj]="detailTable" [datasource]="datasource" (getPage)="getPage($event)" ></app-msx-datatable>