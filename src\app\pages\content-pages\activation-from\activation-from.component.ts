import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BaseResponse } from 'app/model/api/base.response';
import { GetUserActDataRequest } from 'app/model/api/getUserActData.request';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { SendOtpBySMSRequest } from 'app/shared/dto/otp/send-otp-by-sms.request';
import { ToastrService } from 'ngx-toastr';
import { ModalRequestOtpComponent } from '../modal-request-otp/modal-request-otp.component';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { GetAvailableOtpSendingPointsRequest } from 'app/model/api/get-available-otp-sendingpoints.request';
import { TenantService } from 'app/services/api/tenant.service';
import { OtpActivationResponse } from 'app/model/api/otp-activation.response';

@Component({
  selector: 'app-activation-from',
  templateUrl: './activation-from.component.html',
  styleUrls: ['./activation-from.component.scss']
})
export class ActivationFromComponent implements OnInit {
  formObj: FormModel<any>;
  state: any;
  routeData: any;
  activationForm: FormGroup;
  isShowPassword : boolean;
  isShowConfirmPassword : boolean;
  swal = swalFunction;
  isSms: boolean;
  isWa: boolean;

  fields: any;
  validationMessages: any;
  existEmailMessage: any;
  template: any;
  messages  = []; // Simpan validation message
  keys      = [];
  confimPasswordMessage: any;
  msg: any;
  email: any;
  phone: any;
  vendor:any;
  isNoPassword: boolean = false;

  constructor(private formBuilder: FormBuilder, private router: Router, private http: HttpClient,
              private cdr: ChangeDetectorRef, private global: GlobalService,  
              private activatedRoute: ActivatedRoute, private modalService: NgbModal,
              private tenantService: TenantService) {
  this.isShowPassword = false;
  this.isShowConfirmPassword = false;

  this.routeData = this.activatedRoute.snapshot.data;

    this.activatedRoute.queryParams.subscribe(() => {
      this.state = this.router.getCurrentNavigation().extras.state;
      console.log('state', this.state);
    })

    this.msg = this.state.msg;
    this.vendor = this.state.vendor;
    
  }
  
  async ngOnInit(): Promise<void> {
    await this.getUserActData();
    await this.getAvailableSendingPoint();
    this.initView();
  }

  initView() {
    this.setProperties();
    this.activationForm = this.formBuilder.group({
      email: ['', Validators.compose([
        Validators.required]),
      ],
      name: ['', Validators.compose([
        Validators.required])
      ],
      password: [' '],
      confirmPassword: [' '],
      sendMedia: ['', Validators.required]
    },
    {
      updateOn: 'change'
    });
  }

  confirmPasswordValidator: ValidatorFn = (fg: FormGroup) => {
    if (!this.isNoPassword) {
      const password = fg.get('password').value;
      const confirmPassword = fg.get('confirmPassword').value;
      if(confirmPassword != password){
        this.confimPasswordMessage = 'Kata Sandi dan Ketik Ulang Kata Sandi berbeda';
        fg.get('confirmPassword').setErrors({'differentPassword': true});
        return {differentPassword: true}
      }
      fg.get('confirmPassword').setErrors(null);
      this.confimPasswordMessage = null;
      return null;
    } else {
      return null;
    }
  };

  private setProperties() {
    // Message Template
    this.template = {
      required: '{x} harus diisi',
      maxlength: 'Maksimal jumlah karakter {x} adalah {v}',
      minlength: 'Minimal jumlah karakter {x} adalah {v}',
      pattern: 'Silahkan isi {x} dengan format yang benar.',
      patternNama: 'Silahkan isi {x} dengan format hanya huruf.',
      patternPassword: 'Kata sandi harus mengandung karakter huruf besar, huruf kecil, nomor, dan karakter spesial.',
    };

    this.fields = {
      name: {
        label: 'Nama Pengguna',
        prop: {
          maxlength: 50
        }
      },
      email: {
        label: 'Email',
        prop: {
          maxlength: 64
        }
      },
      password: {
        label: 'Kata Sandi',
        prop: {
          maxlength: 50,
          minlength: 8
        }
      },
      confirmPassword: {
        label: 'Konfirmasi Kata Sandi',
        prop: {
          maxlength: 50,
          minlength: 8
        }
      },
      sendMedia: {
        label: 'Media Pengiriman OTP'
      }
    };

    // Validation Custom Error Messages
    this.validationMessages = {
      name: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.name.label) },
        { type: 'pattern', message: this.template.patternNama.replace('{x}', this.fields.name.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.name.label)
            .replace('{v}', this.fields.name.prop.maxlength)
        },
      ],
      email: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.email.label) },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.email.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.email.label)
            .replace('{v}', this.fields.email.prop.maxlength)
        },
      ],
      password: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.password.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.password.label)
            .replace('{v}', this.fields.password.prop.maxlength)
        },
        {
          type: 'minlength', message: this.template.minlength.replace('{x}', this.fields.password.label)
            .replace('{v}', this.fields.password.prop.minlength)
        },
        { type: 'pattern', message: this.template.patternPassword }
      ],
      confirmPassword: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.confirmPassword.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.confirmPassword.label)
            .replace('{v}', this.fields.confirmPassword.prop.maxlength)
        },
        {
          type: 'minlength', message: this.template.minlength.replace('{x}', this.fields.confirmPassword.label)
            .replace('{v}', this.fields.confirmPassword.prop.minlength)
        }
      ],
      sendMedia: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.sendMedia.label) },
      ]
    };
  }

  get rf() {
    return this.activationForm.controls;
  }

  async getUserActData(){
    const request = new GetUserActDataRequest;
    request.msg = this.msg;
    this.http.post(URLConstant.getUserActData, request).subscribe(
      (response) => {
        if (response['status']['code'] == 0) {
          console.log(response);
          this.email = response['email'];
          this.phone = response['phoneNo'];
          
          this.isNoPassword = response['isNoPassword'] === '1';

          if (response['isNoPassword'] != '1') {
            this.activationForm.get('password').setValidators(Validators.compose(
              [Validators.required,
                Validators.minLength(8), 
              Validators.pattern(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()`~;:'"<,>.?|_+=}\/{\\\[\]-])[A-Za-z\d!@#$%^&*()`~;:'"<,>.?|_+=}\/{\\\[\]-]{8,}/
              ),
              Validators.maxLength(50)]));
            this.activationForm.get('confirmPassword').setValidators(Validators.compose([
              Validators.required,
              Validators.minLength(8), 
              Validators.maxLength(50),
            ]));

            this.activationForm.patchValue({
              password: '',
              confirmPassword: '',
            });

            this.activationForm.setValidators(this.confirmPasswordValidator);
          }

          this.activationForm.patchValue({
            email: response['email'],
            name: response['fullName'],
          });
          this.cdr.detectChanges();
        }
        console.log('Error', response);
        console.log('is form valid', this.activationForm.valid);
        console.log('errors', this.activationForm.errors);
      }
    )
  }

  async getAvailableSendingPoint() {
    const request = new GetAvailableOtpSendingPointsRequest();
    request.msg = this.msg;

    this.tenantService.getAvailableSendingPoint(request).subscribe(
      response => {
        if (response.status.code === 0) {
          const available = response.listAvailableOptionSendingPoint;
          if(available.length > 1) {
            if (available.includes('SMS')) {
              this.isSms = true;
            }
  
            if (available.includes('WA')) {
              this.isWa = true;
            }
  
            this.activationForm.patchValue({
              sendMedia: response.defaultAvailableOptionSendingPoint
            });
          } else {
            if (available[0] === 'SMS') {
              this.isSms = true;
            } else {
              this.isWa = true;
            }
            this.activationForm.patchValue({
              sendMedia: available[0]
            });
          }

          this.cdr.detectChanges();
        }
      }
    )
  }

  toggleIsShowPassword() {
    this.isShowPassword = !this.isShowPassword;
  }

  toggleIsShowConfirmPassword() {
    this.isShowConfirmPassword = !this.isShowConfirmPassword;
  }

  doReset() {
    this.activationForm.patchValue({
      password: '',
      confirmPassword:'',
    });
  }

  onProcess(){
    console.log('process');
    console.log(this.activationForm.controls['sendMedia'].value);
    const request = new SendOtpBySMSRequest();
    request.msg = this.msg;
    request.phoneNo = this.phone;
    request.audit.callerId = this.msg;
    request.sendingPointOption = this.activationForm.controls['sendMedia'].value;
    this.http.post<OtpActivationResponse>(URLConstant.sentOtpActivationUser, request).subscribe(
      (response) => {
        if (response.status.code!== 0) {
          this.swal.Error(response.status.message);
          console.log('Error', response.status.message);
          return;
        }
        else {
          const modal = this.modalService.open(ModalRequestOtpComponent, { backdrop: 'static', keyboard: false, size: 'l' });
          modal.componentInstance.msg = this.msg;
          modal.componentInstance.email = this.email;
          modal.componentInstance.phone = this.phone;
          modal.componentInstance.password = this.activationForm.controls['password'].value;
          modal.componentInstance.vendor = this.vendor;
          modal.componentInstance.sendMedia = this.activationForm.controls['sendMedia'].value;
          modal.componentInstance.isWa = this.isWa;
          modal.componentInstance.isSms = this.isSms;
          modal.componentInstance.resendDuration = response.durationResendOTP;
        }
      }
    );
  }

}
