import { Signer } from "app/model/signer";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { CommonConstant } from "app/shared/constant/common.constant";

export const SignerTable: Table<Signer> = {
    name: 'listSigner',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'signerType',
            label: 'Type',
            width: 90
        },
        {
            type: ColumnType.Text,
            prop: 'signerName',
            label: 'Name',
            width: 120
        },
        {
            type: ColumnType.Text,
            prop: 'signerEmail',
            label: 'Email',
            width: 300
        },
        {
            type: ColumnType.Text,
            prop: 'registerStatus',
            label: 'Register Status',
            width: 130
        },
        {
            type: ColumnType.Text,
            prop: 'signStatus',
            label: 'Status',
            width: 100
        },
        {
            type: ColumnType.Date,
            format: CommonConstant.FORMAT_DATE_WITHTIME,
            prop: 'signDate',
            label: 'Signed Date',
            width: 130
        }
    ]
}