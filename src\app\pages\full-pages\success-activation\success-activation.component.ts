import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { UpdateActStatusRequest } from 'app/model/api/update.act.status.request';
import { UpdateActStatusResponse } from 'app/model/api/update.act.status.response';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { MsxAlertComponent } from '../../../shared/components/msx-alert/msx-alert.component';
import {User} from '../../../model/user';
import {UserProfile} from '../../../model/user-profile';

@Component({
  selector: 'app-success-activation',
  templateUrl: './success-activation.component.html',
  styleUrls: ['./success-activation.component.scss']
})
export class SuccessActivationComponent implements OnInit {

  msg: string;
  tenantCode: string;
  isFromParent = false;
  isEmbed = false;

  constructor(private activatedRoute: ActivatedRoute, private http: HttpClient, private router: Router,
    private toastrService: ToastrService, private global: GlobalService, private ngModal: NgbModal) {
    this.activatedRoute.queryParams.subscribe(
      (params) => {
        if (params['msg'] != null) {
          this.msg = encodeURIComponent(params['msg']);
        }
        if (params['isFromParent'] != null) {
          this.isFromParent = params['isFromParent'];
        }
        if (params['isEmbed'] != null) {
          this.isEmbed = params['isEmbed'];
        }
      }
    );
    this.tenantCode = localStorage.getItem('tenantCode');
  }

  ngOnInit(): void {
    if (this.isEmbed) {
      localStorage.clear();
    }

    if (!this.global.user) {
      const user   = new UserProfile();
      user.pathSrc = '';
      this.global.user = user;
    }

    if (!this.isFromParent) {
      if (this.global.user.pathSrc && this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
        if (this.msg != null) {
          window.location.href = window.location.href + '&isFromParent=true&isEmbed=true';
        } else {
          window.location.href = window.location.href + '?isFromParent=true&isEmbed=true';
        }
        this.global.user.pathSrc = '';
      } else {
        if (this.msg != null) {
          window.top.location.href = window.location.href + '&isFromParent=true';
        } else {
          window.top.location.href = window.location.href + '?isFromParent=true';
        }
      }
    } else {
      if (this.msg != null) {
        const request: UpdateActStatusRequest = new UpdateActStatusRequest();
        request.audit.callerId = 'SYSTEM';
        request.msg = this.msg;
        request.tenantCode = this.tenantCode;
        this.http.post<UpdateActStatusResponse>(URLConstant.UpdateActStatus, request).subscribe(
          (response) => {
            if (response.status.code === 0) {
              console.log('response', response);
              localStorage.removeItem('vendorCode');
              if (response.activationRedirectClientURL != null && response.activationRedirectClientURL !== '') {
                window.open(response.activationRedirectClientURL, "_self");
              } else if (response.signLink != null && response.signLink !== '') {
                if (this.isEmbed) {
                  this.router.navigate([PathConstant.EMBED_SIGNATURE], { queryParams: { url: response.signLink } });
                } else {
                  this.router.navigate([PathConstant.SIGNATURE], { queryParams: { url: response.signLink } });
                }
              } else if (response.docs.length > 0) {
                localStorage.removeItem('isActivation');
                if (response.docs[0].documentId === '') {
                  // Route ke success registration
                  const modal = this.ngModal.open(MsxAlertComponent, { size: 'md', backdrop: 'static', keyboard: false });
                  modal.componentInstance.image = './assets/img/img_success.svg';
                  modal.componentInstance.title = 'Registrasi Berhasil';
                  modal.componentInstance.message = "User (" +response.fullName + " - " + '\n' + response.idNo + " - " + '\n' + response.phoneNum + " - " + '\n' + response.email + ")\n" + '\n' + ' Proses registrasi dan aktivasi Anda sudah selesai. Silahkan menunggu notifikasi permintaan tanda tangan untuk lanjut ke proses berikutnya.';
                } else {
                  const extras: NavigationExtras = {
                    state: {
                      data: response.docs
                    }
                  };
                  if (this.isEmbed) {
                    this.router.navigate([PathConstant.EMBED_BULK_SIGN], extras);
                  } else {
                    this.router.navigate([PathConstant.INQUIRY_BULK_SIGN], extras);
                  }
                }
              }
            } else {
              console.log('Error', response.status);
            }
          }, (err) => {
            this.toastrService.error(err.message, null, { positionClass: 'toast-top-right' });
          }
        );
      }
    }
  }

}
