import {AfterViewInit, ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {UserService} from '../../../services/api/user.service';
import {ActivationRequest} from '../../../model/api/activation.request';
import { GlobalService } from 'app/shared/data/global.service';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import {UserProfile} from '../../../model/user-profile';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { MsxAlertComponent } from 'app/shared/components/msx-alert/msx-alert.component';

@Component({
  selector: 'app-activation',
  templateUrl: './activation.component.html',
  styleUrls: ['./activation.component.scss']
})
export class ActivationComponent implements OnInit, AfterViewInit {
  email: string;
  url: string;
  tenantCode: string;
  vendorCode: string;
  msg: string;
  src = '';
  code: string;

  constructor(private readonly activateRoute: ActivatedRoute, private service: UserService,
              private cdr: ChangeDetectorRef, private readonly global: GlobalService,
              private ngModal: NgbModal, private router: Router) {
    this.activateRoute.queryParams.subscribe(params => {
      if (params['code']) {
        this.code = params['code'];
        return;
      }

      if (params['uri'] != null) {
        this.email = params['uri'];
      }
      this.src = params['src'] || '';
      this.tenantCode = params['tenantCode'] || localStorage.getItem('tenantCode');
      this.vendorCode = params['vendorCode'] || localStorage.getItem('vendorCode');
      if (params['msg'] != null) {
        this.msg = params['msg'] || this.global.msg;
      }
    })
  }

  ngOnInit(): void {
    let url;
    const request: ActivationRequest = new ActivationRequest();

    if (this.msg) {
      localStorage.clear();
    }

    if (this.code) {
      url = URLConstant.ActivationTekenAja;
      request.msg = this.code;
      this.service.activation(request, url).subscribe(response => {
        if (response.status.code === 0 && response['url']) {
          window.location.href = response.url;
        } else {
          this.router.navigate(['/pages/error'], {state: {msg: response.status.message}});
        }
      });
      return;
    }

    if (!this.global.user) {
      const user   = new UserProfile();
      user.pathSrc = '';
      this.global.user = user;
    }

    if (this.msg) {
      request.msg = this.msg;
      url = URLConstant.GetActivationLinkEmbed;
    } else {
      request.tenantCode = this.tenantCode;
      url = URLConstant.GetActivationLink;
    }

    if (this.src !== '') {
      url = URLConstant.GetActivationLinkInv;
    }

    request.email = this.email || null;
    request.vendorCode = this.vendorCode;

    console.log('Aktivasi Req:', request);
    console.log('Aktivasi Req:', url);

    this.service.activation(request, url).subscribe(response => {
      if (response.status.message !== undefined && response.status.message.includes('8131')) {
        const modal = this.ngModal.open(MsxAlertComponent, { size: 'md', backdrop: 'static', keyboard: false });
        modal.componentInstance.image = './assets/img/img_success.svg';
        modal.componentInstance.title = 'Registrasi Berhasil';
        if(""!=response.fullName){
          modal.componentInstance.message = "User (" +response.fullName + " " + response.idNo + ")" +' Proses registrasi dan aktivasi Anda sudah selesai. Silahkan menunggu notifikasi permintaan tanda tangan untuk lanjut ke proses berikutnya.';

        } else {
          modal.componentInstance.message = 'Proses registrasi dan aktivasi Anda sudah selesai. Silahkan menunggu notifikasi permintaan tanda tangan untuk lanjut ke proses berikutnya.';
        }
      }

      if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
        window.location.href = response.url;
      } else {
        this.url = response.url;
      }
      console.log(response);
      this.cdr.markForCheck();
    }, err => {
      // this.toastrService.error(err.message, null, {
      //   positionClass: 'toast-top-right'
      // })
    })
  }

  ngAfterViewInit(): void {

  }

}
