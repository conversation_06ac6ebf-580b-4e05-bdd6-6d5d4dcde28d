<form [formGroup]="settingForm" (ngSubmit)="onSubmit()">
  <div class="modal-header">
    <h4 class="modal-title">Add Signature</h4>
    <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">

    <div class="form-group row align-items-center">
      <div class="col-lg-4 col-4">
        <label for="params" class="col-form-label">Signature Params</label>
      </div>
      <div class="col-lg-8 col-8">
        <textarea formControlName="params" class="form-control" name="params" id="params" cols="5" placeholder="Type signature setting here"></textarea>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button *ngIf="!waiting" type="button" class="btn btn-secondary" (click)="activeModal.dismiss('Cross click')">Cancel</button>
    <button *ngIf="!waiting" type="submit" class="btn btn-primary" [disabled]="settingForm.invalid">Save</button>
    <div *ngIf="waiting" class="d-flex justify-content-center">
      <div class="spinner-border text-secondary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
  </div>
</form>
