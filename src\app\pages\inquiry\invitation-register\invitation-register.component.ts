import {Component, OnInit} from '@angular/core';
import {<PERSON>x<PERSON>iew} from '../../../shared/components/msx-view/models/MsxView';
import {URLConstant} from '../../../shared/constant/URLConstant';
import {ActivatedRoute, Router} from '@angular/router';
import {FormConstant} from '../../../shared/components/ms-form/constants/form.constant';
import {Table} from '../../../shared/components/msx-datatable/models/table';
import {ColumnType} from '../../../shared/components/msx-datatable/enums/column-type';
import {Act} from '../../../shared/components/msx-datatable/enums/act';
import {WidgetType} from '../../../shared/components/msx-view/models/WidgetType';
import {ActionModel} from '../../../shared/components/ms-form/models/action.model';
import {InquiryInvitation} from '../../../model/inquiry-invitation';
import {InvitationService} from '../../../services/api/invitation.service';
import {InvitationlinkRequest} from '../../../model/api/invitationlink.request';
import {GlobalService} from '../../../shared/data/global.service';
import {Confirm, Success} from '../../../shared/data/sweet-alerts';
import {ToastrService} from 'ngx-toastr';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {ViewInvitationLinkComponent} from './modal/view-invitation-link/view-invitation-link.component';
import {PathConstant} from '../../../shared/constant/PathConstant';
import {SentActivationLinkUserRequest} from '../../../model/api/sent.activation.link.user.request';
import {Role} from '../../../shared/constant/Role';
import { RegenerateInvitationRequest } from 'app/model/api/regenerateinvt.request';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { IpService } from 'app/services/api/ip.service';

@Component({
  selector: 'app-invitation-register',
  templateUrl: './invitation-register.component.html',
  styleUrls: ['./invitation-register.component.scss']
})
export class InvitationRegisterComponent implements OnInit {

  view: MsxView;
  serviceUrl = URLConstant.InquiryInvitationRegister;

  public userGroup: string;
  private routeData: any;

  ipAddress;

  constructor(private readonly activeRoute: ActivatedRoute, private readonly router: Router,
              private readonly global: GlobalService, private readonly service: InvitationService,
              private readonly toaster: ToastrService, private ngModal: NgbModal, private ipService: IpService) {
    this.routeData = this.activeRoute.snapshot.data;
    this.userGroup = this.routeData['userGroup'];
  }

  async ngOnInit(): Promise<void> {
    this.initView()
    await this.getIdAddress();
  }

  async getIdAddress(){
    await this.ipService.getIpAddress().toPromise().then(ipResponse =>{
      this.ipAddress = ipResponse.ip;
    })
  }

  initView() {
    const searchFilterObj = {
      name: 'InquiryInvitationSearchFilter',
      colSize: 6,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        {
          key: 'param',
          label: 'Email / No. Handphone / NIK',
          placeholder: 'Type Email / No. Handphone / NIK here',
          controlType: FormConstant.TYPE_TEXT
        }
      ],
      params: [
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        }
      ]
    };

    const userInquiryTable: Table<InquiryInvitation> = {
      name: 'listUser',
      list: [],
      columns: [
        {
          type: ColumnType.Text,
          prop: 'nama',
          label: 'Name',
          width: 150
        },
        {
          type: ColumnType.Text,
          prop: 'invBy',
          label: 'Invitation By',
          width: 100
        },
        {
          type: ColumnType.Text,
          prop: 'recieverDetail',
          label: 'Receiver',
          width: 150
        },
        {
          type: ColumnType.Text,
          prop: 'tlp',
          label: 'Phone',
          width: 100
        },
        {
          type: ColumnType.Text,
          prop: 'vendorName',
          label: 'Vendor',
          width: 100
        },
        {
          type: ColumnType.IsActive,
          prop: 'isActive',
          label: 'Status',
          width: 100
        },
        {
          type: ColumnType.Date,
          prop: 'invCrt',
          format: CommonConstant.FORMAT_DATE_WITHTIME_SECOND,
          label: 'Invitation Date',
          width: 120
        },
        {
          type: ColumnType.Action,
          label: 'Action',
          width: 100,
          action: [
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-edit',
              descr: 'Edit',
              type: Act.Edit,
              condition: true,
              conditionedClass: 'd-none',
              conditionVariable: 'isEditable',
              conditionExpected: '0'
            },
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-link',
              descr: 'View Link',
              type: Act.View,
              condition: true,
              conditionedClass: 'd-none',
              conditionVariable: 'isActive',
              conditionExpected: '0'
            },
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-mail',
              descr: 'Kirim Ulang Undangan',
              type: Act.Resend,
              condition: true,
              conditionedClass: 'd-none',
              conditionVariable: 'isActive',
              conditionExpected: '0'
            },
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-send',
              descr: 'Kirim Ulang Link Aktivasi',
              type: Act.ActInv,
              condition: true,
              conditionedClass: 'd-none',
              conditionVariable: 'resendActivationLinkStatus',
              conditionExpected: '0'
            },
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-rotate-cw',
              descr: 'Regenerate invitation link',
              type: Act.ActUser,
              condition: true,
              conditionedClass: 'd-none',
              conditionVariable: 'isRegenerable',
              conditionExpected: '0'
            }
          ]
        }
      ]
    };

    this.view = {
      title: 'Pencarian Link Undangan',
      components: [
        {
          component: searchFilterObj,
          type: WidgetType.SearchFilter
        },
        {
          component: userInquiryTable,
          type: WidgetType.Datatable
        }
      ]
    };
  }

  onItemClick(event: {act: ActionModel, data: InquiryInvitation}) {
    console.log('Item', event);
    switch (event.act.type) {
      case Act.Edit:
        if (Role.AdminError === this.global.user.role.roleCode) {
          this.toaster.error('Akses tidak di izinkan!', null, {positionClass: CommonConstant.TOAST_BOTTOM_RIGHT});
          return;
        }

        this.router.navigate([PathConstant.EDIT_INVITATION], {state: {param: event.data}});
        break;
      case Act.Resend:
        this.sentInvitationLink(event.data);
        break;
      case Act.View:
        this.getInvitationLink(event.data);
        break;
      case Act.ActInv:
        this.sentActivationLink(event.data);
        break;
      case Act.ActUser:
        this.regenerateInvtLink(event.data);
        break;
    }
  }

  getInvitationLink(data: InquiryInvitation) {
    const request: InvitationlinkRequest = new InvitationlinkRequest();
    request.receiverDetail = data.recieverDetail;
    request.tenantCode = this.global.user.role.tenantCode;
    request.vendorCode = data.vendorCode;
    console.log('Request', request);

    this.service.getInvitationLink(request).subscribe(res => {
      console.log('Response', res);
      const modal = this.ngModal.open(ViewInvitationLinkComponent, { size: 'md', backdrop: 'static', keyboard: false });
      modal.componentInstance.link = res.invitationLink;
    })
  }

  sentInvitationLink(data: InquiryInvitation) {
    Confirm('Apakah anda ingin mengirim ulang link undangan ini?').then(result => {
      if (result.isConfirmed) {
        const request: InvitationlinkRequest = new InvitationlinkRequest();
        request.receiverDetail = data.recieverDetail;
        request.tenantCode = this.global.user.role.tenantCode;
        request.vendorCode = data.vendorCode;
        console.log('Request', request);

        this.service.sentInvitation(request).subscribe(res => {
          if (res.status.code === 0) {
            this.toaster.success(res.status.message, null, {positionClass: CommonConstant.TOAST_BOTTOM_RIGHT});
          } 
        })
      }
    })
  }

  sentActivationLink(data: InquiryInvitation) {
    if (data.isRegistered === '0') {
      this.toaster.warning('Tidak bisa kirim link aktivasi, user belum register.', null, {positionClass: CommonConstant.TOAST_BOTTOM_RIGHT});
      return;
    }
    Confirm('Apakah anda ingin mengirim ulang link aktivasi?').then(result => {
      if (result.isConfirmed) {
        const request: SentActivationLinkUserRequest = new SentActivationLinkUserRequest();
        request.receiverDetail = data.recieverDetail;
        request.tenantCode = this.global.user.role.tenantCode;
        request.vendorCode = data.vendorCode;
        console.log('Request', request);

        this.service.resentActivationLink(request).subscribe(res => {
          if (res.status.code === 0) {
            this.toaster.success(res.status.message, null, {positionClass: CommonConstant.TOAST_BOTTOM_RIGHT});
          } else {
            this.toaster.error(res.status.message, null, {positionClass: CommonConstant.TOAST_BOTTOM_RIGHT});
          }
        })
      }
    })
  }
  regenerateInvtLink(data: InquiryInvitation){
    Confirm('Apakah anda ingin generate ulang link undangan?').then(result => {
      if(result.isConfirmed){
        const request: RegenerateInvitationRequest = new RegenerateInvitationRequest();
        request.audit = this.global.audit;
        request.receiverDetail = data.recieverDetail;
        request.vendorCode = data.vendorCode
        request.ipAddress = this.ipAddress;
        
        this.service.regenerateInvitation(request).subscribe(res => {
          if (res.status.code === 0) {
            Success(res.status.message).then(() => {
              window.location.reload()
            });
          }
        })
      }
    });
  }

}
