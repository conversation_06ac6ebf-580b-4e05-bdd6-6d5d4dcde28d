<form [formGroup]="templateForm" (ngSubmit)="onSubmit()">
    <div class="modal-body">
        <button type="button" class="close" aria-label="Close" (click)="dismiss()">
            <span aria-hidden="true">&times;</span>
        </button>

        <div class="mt-5">
            <div class="row justify-content-center align-items-center">
                <p class="font-large-1 text-center text-bold-700 px-5" translate>Enter verification code</p>
            </div>
        </div>

        <div class="row justify-content-center align-items-center">
            <p class="text-center mb-0" translate>We have sent a verification code to</p>
        </div>
        <div class="row justify-content-center align-items-center">
            <p class="text-center">{{ emailOrPhone }}</p>
        </div>
      
        <div class="row justify-content-center align-items-center">
            <input formControlName="otp" type="tel" class="letterotp mt-3" maxlength="6" id="otp" value="">
        </div>

        <div class="row justify-content-center align-items-center mt-4">
            <div *ngIf="timeLeft > 0">{{'Wait' | translate}} {{ timeLeft }} {{'seconds' | translate}}</div>
            <div *ngIf="timeLeft === -1" id="ketotp" translate>Didn't get the code?</div>
        </div>

        <div *ngIf="timeLeft === -1" class="row justify-content-center align-items-center mt-3">
            <div style="color: #FF7F37"><a (click)="sendOtp()" translate>Send code again</a></div>
        </div>

        <!-- <div class="row justify-content-center align-items-center mt-3">
            <div style="color: #FF7F37"><a (click)="dismiss()">Change email</a></div>
        </div> -->

        <button type="submit" class="btn btn-info btn-block mt-4" [disabled]="templateForm.invalid" translate>Verify</button>
    </div>
</form>
  