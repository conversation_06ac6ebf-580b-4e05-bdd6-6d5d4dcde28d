import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseResponse } from 'app/model/api/base.response';
import { ManualSignerRequest } from 'app/model/api/manual.signer.request';
import { BoxAnnotation } from 'app/model/box-annotation';
import { StampAnnotation } from 'app/model/stamp-annotation';
import { Signer } from 'app/shared/components/document-anotate/model/signer';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { SignerType } from 'app/shared/data/signer-type';
import * as moment from 'moment';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-setting-manual-sequential',
  templateUrl: './setting-manual-sequential.component.html',
  styleUrls: ['./setting-manual-sequential.component.scss']
})
export class SettingManualSequentialComponent implements OnInit {

  public state: any;
  public result: any;
  seqsigns: string[] = [];
  public signer: Signer[];
  public seqmaterai: number;
  public annotations: BoxAnnotation[];
  public users: Signer[];
  public isSubmitting: boolean = false;

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.seqsigns, event.previousIndex, event.currentIndex);
  }

  constructor(private router: Router, private location: Location,
    private http: HttpClient,private toastrService: ToastrService) {
    this.state = this.router.getCurrentNavigation().extras;
    if (this.state && this.state.state.ttd && Array.isArray(this.state.state.ttd)) {
      const arrayLength = this.state.state.ttd.length;
      for (let i = 0; i < arrayLength; i++) {
        const dynamicData = this.state.state.ttd[i];
        if (dynamicData && dynamicData.email && dynamicData.label !== "Meterai" && !this.seqsigns.includes(dynamicData.email)) {
          this.seqsigns.push(dynamicData.email);
        }
      }
    }
   }

  ngOnInit(): void {
  }

  onCancel() {
    this.location.back();
  }

  onSubmit() {
    if (this.isSubmitting) {
      return;
    }
    this.isSubmitting = true;

    const documentBase64 = this.state.state.data['rawTemplate'];
    const request: ManualSignerRequest = new ManualSignerRequest();

    request.psreCode      = this.state.state.data?.psreCode;
    request.referenceNo   = this.state.state.data?.referenceNo;
    request.documentName  = this.state.state.data?.documentName;
    request.documentDate  = this.parseDateValue(this.state.state.data?.documentDate);
    request.peruriDocType = this.state.state.data?.documentTypePeruri;
    request.isAutomaticStamp = this.state.state.data?.isAutomaticStamp;
    request.paymentType   = this.state.state.data?.paymentType;
    request.isSequence    = this.state.state.data?.isSequence;
    request.officeCode    = this.state.state.data?.officeCode;
    request.businessLineCode = this.state.state.data?.businessLineCode;
    request.regionCode    = this.state.state.data?.regionCode;
    request.useSignQr     = this.state.state.data?.useSignQr;
 
    request.signers = [];
    const usedLabels = new Map<string, boolean>();

    request.documentFile  = documentBase64.replace(CommonConstant.DATA_APPLICATION_PDF_BASE64, '');
   
    this.seqsigns.forEach((seqsign, index) => {
      if(seqsign !== 'Meterai') {      
      const matchingSigners = this.state.state.users.filter((data: any) => data.email === seqsign);
      if (matchingSigners.length > 0) {
        matchingSigners.forEach((signerData) => {
          const signer: Signer = signerData;
          const seqNo = (index + 1).toString(); // Mengupdate seqNo dengan urutan indeks dari 1
          signer.seqNo = seqNo;
          this.seqmaterai = (index + 2)
          request.signers.push(signer);
          usedLabels.set(signer.email, true);
        });
      }
    }
    });

 
    const stamps: StampAnnotation[] = [];
    const listStamp = this.state.state.ttd.filter(x => x.type === SignerType.SDT);
    listStamp.forEach(data => {
      stamps.push(StampAnnotation.fromSigner(this.validateSigner(new Signer(data))));
    });
    request.stampingLocations = stamps;
    this.http.post<BaseResponse>(URLConstant.ManualUploadSigner, request).subscribe(response => {
      if (response.status.code === 0) {
        this.toastrService.success('Permintaan tanda tangan berhasil dibuat.', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        this.router.navigate(['master', 'documents', 'manual-upload']);
      }
    }).add(() => { 
      this.isSubmitting = false;
    });
  }

  parseDateValue(dateObj) {
    const date  = new Date(dateObj['year'], dateObj['month'] - 1, dateObj['day']);
    return moment(date).format('YYYY-MM-DD');
  }

  validateSigner(mSginer: Signer) {
    if (!this.signer) {
      return mSginer;
    }

    console.log('validate signer');
    if (mSginer.signTypeCode === 'SDT') {
      return this.validateSdt(mSginer);
    } else {
      const check = this.signer.find(x => x.signerTypeCode === mSginer.signerTypeCode &&
        x.signPage === mSginer.signPage);

      if (!check) {
        return mSginer;
      }

      if (mSginer.signLocation.llx < 0 && mSginer.signLocation.urx < 0) {
        return check;
      } else {
        return mSginer;
      }
    }
  }

  validateSdt(mSginer: Signer) {
    const sdt = this.signer.find(x => x.signTypeCode === mSginer.signTypeCode &&
      x.signPage === mSginer.signPage);

    if (!sdt) {
      return mSginer;
    }

    if (mSginer.signLocation.llx < 0 && mSginer.signLocation.urx < 0) {
      return sdt;
    } else {
      return mSginer;
    }
  }
}
