import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DocumentTemplate } from 'app/model/template';
import { Signer } from 'app/shared/components/document-anotate/model/signer';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { QuestionDate, QuestionDropdown, QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { QuestionFile } from 'app/shared/components/ms-form/questions/question-file';
import { PreFormsubmitPromptComponent } from 'app/shared/components/pre-formsubmit-prompt/pre-formsubmit-prompt.component';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GlobalService } from 'app/shared/data/global.service';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject } from 'rxjs';

@Component({
  selector: 'app-manual-stamp',
  templateUrl: './manual-stamp.component.html',
  styleUrls: ['./manual-stamp.component.scss']
})
export class ManualStampComponent implements OnInit {

  vForm: FormGroup;
  // mForm: FormModel<any>;
  formObj: FormModel<any>;
  rawFile: string;

  private documentTemplate: string;
  refs: any[];
  signers: Signer[];
  dataSubj: BehaviorSubject<DocumentTemplate> = new BehaviorSubject<DocumentTemplate>(null);



  constructor(private toastrService: ToastrService, private router: Router, private fcs: MsxFormControlService,private ngModal: NgbModal) { }

  ngOnInit(): void {
    // this.initView();
    this.refs = [];
    this.setupForm();
    const controls = [...this.formObj.params, ...this.formObj.components];
    this.vForm = this.fcs.toFormGroup(controls);
  }

  setupForm(){
    this.formObj = {
      mode: CommonConstant.MODE_ADD,
      name: 'manualStamp',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        new QuestionTextbox(
          {
            key: 'documentNo',
            label: CommonConstant.LABEL_DOCUMENT_NUMBER,
            placeholder: 'Type document number here',
            maxLength: 50,
            required: true,
            validations: [
              {type: 'required', message: 'Document Number harus di isi.'},
              {type: 'maxlength', message: 'Maksimal jumlah karakter Nomor Dokumen adalah 50'}
            ]
          }),
        new QuestionTextbox(
          {
            key: 'documentName',
            label: 'Document Name',
            placeholder: 'Type document name here',
            maxLength: 100,
            required: true,
            validations: [
              {type: 'required', message: 'Document Name harus di isi.'},
              {type: 'maxlength', message: 'Maksimal jumlah karakter Document Name adalah 100'}
            ]
          }),
        new QuestionDate({
          key: 'documentDate',
          label: 'Document Date',
          placeholder: CommonConstant.FORMAT_DATE,
          width: 140,
          required: true,
          validations: [
            {type: 'required', message: 'Document Date harus di isi.'}
          ]
        }),new QuestionDropdown({
          key: 'documentType',
          label: 'Document Type',
          placeholder: 'Select Document Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'DOC_TYPE'
          },
          required: true,
        }),
        new QuestionDropdown({
          key: 'documentTypePeruri',
          label: 'Document Peruri Type',
          placeholder: 'Select Document Type Peruri',
          serviceUrl: URLConstant.ListDocTypePeruri,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'documentEMateraiList',
            key: 'peruriDocId',
            value: 'documentName'
          },
          params: {
          },
          required: true,
        }),
        new QuestionFile({
          key: 'documentExample',
          label: 'Document',
          placeholder: 'Choose File',
          accept: 'application/pdf',
          required: true
        }),
      ],
      params: [
      ]
    }
  }
  onSelect($event) {
    console.log('select', $event);

    if (this.refs.indexOf($event['data']) === -1) {
      this.refs.push($event['data']);
    }

    console.log('refs', this.refs);
  }

  onInput(event) {
    console.log('input', event);
    if (event.target && event.target['files']) {
      const files = event.target.files;
      if (files[0].type !== 'application/pdf') {
        this.vForm.get('documentExample').reset();
        this.toastrService.warning('Silahkan pilih dokumen dengan format pdf!', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }

      if (files[0].size > 10e+6) {
        this.vForm.get('documentExample').reset();
        this.toastrService.warning('Ukuran file tidak boleh dari 10MB', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }
    }
  }
  onBase64Data($event) {
    console.log('rawFile', $event);
    this.rawFile = $event;
  }


  onNext(data) {
    const modal = this.ngModal.open(PreFormsubmitPromptComponent);
    modal.result.then((result) => {
      if (result === 'submit') {
        console.log('add Form', this.vForm.getRawValue());
    
      data = this.vForm.getRawValue()
    this.router.navigate([PathConstant.MANUAL_STAMP_SETTING], {
      state: {...data, ...{rawTemplate: this.rawFile, extras: this.refs, sdt: true, signer: this.signers, manualStamp: true}},
      queryParams: {mode: CommonConstant.MODE_ADD}
    });
  }});
}
  getQuestion(key: string) {
    return this.formObj.components.find(q => q.key === key);
  }


  onForm(event) {
    this.vForm = event;
  }

  onTemplateFile(data) {
    this.documentTemplate = data;
  }

    onCancel() {
      window.location.reload();
    }
}
