import { NgModule } from "@angular/core";
import { AddEditRoleManagementComponent } from "./add-edit-role-management/add-edit-role-management.component";
import { ListRoleManagementComponent } from "./list-role-management/list-role-management.component";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { NgSelectModule } from "@ng-select/ng-select";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { PipeModule } from "app/shared/pipes/pipe.module";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { SharedModule } from "app/shared/shared.module";
import { MsFormModule } from "app/shared/components/ms-form/ms-form.module";
import { NgxSpinnerModule } from "ngx-spinner";
import { RoleManagementRoutingModule } from "./role-management-routing.module";
import { ModifyMenuOfRoleComponent } from './modify-menu-of-role/modify-menu-of-role.component';

@NgModule({
    declarations : [
        AddEditRoleManagementComponent,
        ListRoleManagementComponent,
        ModifyMenuOfRoleComponent
    ],

    imports : [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        NgSelectModule,
        NgbModule,
        PipeModule,
        NgxDatatableModule,
        SharedModule,
        MsFormModule,
        NgxSpinnerModule,
        RoleManagementRoutingModule
    ],
    entryComponents: [
        ListRoleManagementComponent
    ]
})
export class RoleManagementModule { }