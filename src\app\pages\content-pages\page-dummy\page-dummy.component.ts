import { Component, OnInit } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';


@Component({
  selector: 'app-page-dummy',
  templateUrl: './page-dummy.component.html',
  styleUrls: ['./page-dummy.component.scss']
})
export class PageDummyComponent implements OnInit {
  
  
  pageUrl: string = '';
  showEmbeddedPage: boolean = false;
  embeddedPageUrl: SafeResourceUrl;


  
  constructor(private sanitizer: DomSanitizer) { }

  ngOnInit(): void {
    this.openEmbeddedPage();
  }

  openEmbeddedPage() {
    this.showEmbeddedPage = true;
    this.embeddedPageUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.pageUrl);
  }
  
  
}
