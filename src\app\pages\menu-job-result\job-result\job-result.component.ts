import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { JobResult } from 'app/model/job-result';
import { QuestionDate } from 'app/shared/components/ms-form/questions';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { JobResultListView } from '../job-result.view';
import { ViewRequestParamComponent } from '../view-request-param/view-request-param.component';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { HttpClient } from '@angular/common/http';
import { CancelJobProcessRequest } from 'app/model/api/cancel.job.process.request';
import { AuditContext } from 'app/model/audit.context';

@Component({
  selector: 'app-job-result',
  templateUrl: './job-result.component.html',
  styleUrls: ['./job-result.component.scss']
})
export class JobResultComponent implements OnInit {

  view: MsxView;
  buttonList: Button[];
  routeData: any;
  serviceUrl = URLConstant.GetListJobResult;
  swal = swalFunction;

  @Input()
  question: QuestionDate;

  constructor(private activatedRoute: ActivatedRoute, private router: Router, private http: HttpClient, 
    private modalService: NgbModal) {
    this.routeData = this.activatedRoute.snapshot.data;
  }

  ngOnInit(): void {
    this.view = JobResultListView;
  }
  
  onItemClickListener(result: any) {
    const data = result['data'];

    switch (result['act']['type']) {
      case Act.View:
        return this.openViewRequestParamModal(data);
      
      case Act.Cancel:
        return this.cancelJobProcess(data);

    }
  }

  //view data without API
  openViewRequestParamModal(data: JobResult){
    const modal = this.modalService.open(ViewRequestParamComponent, { size: 'lg' });
    modal.componentInstance.idJobResult = data.idJobResult;
  };

  cancelJobProcess(data: JobResult){
    this.swal.Confirm('Apakah Anda yakin akan membatalkan proses ini?').then(
      (result) => {
        if (result.isConfirmed === true) {
          const request: CancelJobProcessRequest = new CancelJobProcessRequest();
          request.idJobResult = data.idJobResult;
          request.tenantCode = data.tenantCode;
          request.audit = new AuditContext();
          request.audit.callerId = data.requestBy;
          this.http.post(URLConstant.CancelJobProcess,request).subscribe(
            (response) => {
              if (response['status']['code'] !== 0) {
                this.swal.Error(response["status"]["message"]);
                console.log('cancelJobProcess Error', response);
              } else {
                this.swal.Success(response["status"]["message"]);
              }
            }
          );
        }
      }
    );
  };
  
}
