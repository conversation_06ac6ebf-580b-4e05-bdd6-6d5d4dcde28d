import { Component, OnInit } from '@angular/core';
import { InquiryAuditTrail } from 'app/model/InquiryAuditTrail';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { QuestionTextbox, QuestionDropdown, QuestionDate } from 'app/shared/components/ms-form/questions';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ModalAuditTrailDetailComponent } from '../modal-audit-trail-detail/modal-audit-trail-detail.component';
import { DownloadExcelAuditTrail } from 'app/model/downloadExcelAuditTrail';
import { HttpClient } from '@angular/common/http';
import { saveAs } from 'file-saver';
import { ModalAuditRelatedDocumentComponent } from '../modal-audit-related-document/modal-audit-related-document.component';

@Component({
  selector: 'app-inquiry-audit-trail',
  templateUrl: './inquiry-audit-trail.component.html',
  styleUrls: ['./inquiry-audit-trail.component.scss']
})

export class InquiryAuditTrailComponent implements OnInit {
    view: MsxView;
    buttonList: Button[];
    serviceUrl = URLConstant.GetListAuditTrail;
    
    constructor(private modalService: NgbModal, private http: HttpClient) {
      
    }
  
    ngOnInit(): void {
      this.initiateView();
      this.buttonList = []
    }

    b64toBlob(b64Data, contentType = '', sliceSize = 512) {
      const byteCharacters = atob(b64Data);
      const byteArrays = [];
  
      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        const slice = byteCharacters.slice(offset, offset + sliceSize);
  
        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }
  
        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }
  
      const blob = new Blob(byteArrays, { type: contentType });
      return blob;
    }

    downloadDocumentReport(params) {
      const request = new DownloadExcelAuditTrail();
      request.inquiryEndDate = params['inquiryEndDate'];
      request.inquiryStartDate = params['inquiryStartDate'];
      request.tenantCode = params['tenantCode'];
      request.processType = params['processType'];
      request.refNumber = params['refNumber'];
      request.nik = params['nik'];
  
      this.http.post<any>(URLConstant.DownloadAuditTrail, request).subscribe(response => {
          if (response.status.code === 0) {
            const blob = this.b64toBlob(response.base64ExcelReport);
            saveAs(blob, response.filename);
          }
      })
    }

    onBtnClickListener($event: any) {
      throw new Error('Method not implemented.');
    }
  
    onItemClick(result: any) {
      const data = result['data'];
      console.log('On click', data);
  
      switch (result['act']['type']) {
        case Act.ViewDetail:
          return this.openDetailModal(data);
      }
      switch (result['act']['type']) {
        case Act.ViewRelatedDocument:
          return this.openRelatedDocumentModal(data);
      }
    }
  
    openDetailModal(data) {
      const modal = this.modalService.open(ModalAuditTrailDetailComponent, { backdrop: 'static', keyboard: false, size: 'xl' });
      modal.componentInstance.data = data;

    }

    openRelatedDocumentModal(data) {
      const modal = this.modalService.open(ModalAuditRelatedDocumentComponent, { backdrop: 'static', keyboard: false, size: 'xl' });
      modal.componentInstance.data = data;
    }
  
    initiateView() {
      const searchFilter = {
        name: 'listinquiryAuditTrailSignProcessSearchForm',
        colSize: 6,
        direction: FormConstant.DIRECTION_HORIZONTAL,
        exportExcel: true,
        exportExcelLabel: 'Export Audit Trail Excel',
        components: [
          new QuestionTextbox({
            key : 'nik',
            label : "Email / NIK / No Hp",
            placeholder: "Please type Email / NIK / No Hp here",
            required : true,
            validations: [
              {type: 'required', message: 'Email / NIK / No Hp harus di isi.'}
            ],
          }),
          new QuestionTextbox({
            key : 'refNumber',
            label : "Ref Number",
            placeholder: "Type ref number here",
          }),
          new QuestionDropdown({
            key: 'processType',
            label: 'Process Type',
            placeholder: 'Select Process Type',
            serviceUrl: URLConstant.GetLov,
            options: [
              { key: '', value: 'All' }
            ],
            value: '',
            args: {
              list: 'lovList',
              key: 'code',
              value: 'description'
            },
            params: {
              lovGroup: 'SIGNING_PROCESS_TYPE'
            }
          }), 
          new QuestionDropdown({
            key: 'tenantCode',
            label: 'Tenant',
            placeholder: 'Select Tenant',
            serviceUrl: URLConstant.GetTenantList,
            options: [
              { key: '', value: 'All' }
            ],
            value: '',
            args: {
              list: 'tenantList',
              key: 'tenantCode',
              value: 'tenantName'
            },
          }),
          new QuestionDate({
            key: 'inquiryStartDate',
            label: 'Start Date',
            placeholder: CommonConstant.FORMAT_DATE
          }),
          new QuestionDate({
            key: 'inquiryEndDate',
            label: 'End Date',
            placeholder: CommonConstant.FORMAT_DATE
          }),
        ],
        params: [
          {
              key: 'page',
              controlType: FormConstant.TYPE_HIDDEN,
              value: 1
          }
        ]
      };
  
      const inquiryAuditTrail: Table<InquiryAuditTrail> = {
        name: 'listinquiryAuditTrailSignProcess',
        list: [],
        columns: [
          {
            type: ColumnType.Text,
            prop: 'email',
            label: "Email",
            width: 170
          },
          {
            type: ColumnType.Text,
            label: 'Tenant',
            prop: 'tenantCode',
            width: 100
          },
          {
            type: ColumnType.Text,
            label: 'PSrE',
            prop: 'psre',
            width: 80
          },
          {
            type: ColumnType.Date,
            format: CommonConstant.FORMAT_DATE_WITHTIME,
            prop: 'date',
            label: CommonConstant.LABEL_COMPLETE_DATE,
            width: 150
          },
          {
            type: ColumnType.Text,
            label: 'Process Type',
            prop: 'processType',
            width: 170
          },
          {
            type: ColumnType.Text,
            label: 'Phone No',
            prop: 'noHP',
            width: 120
          },
          {
            type: ColumnType.Text,
            label: 'NIK',
            prop: 'nik',
            width: 120
          },
          {
            type: ColumnType.Text,
            label: 'Result Status',
            prop: 'resultStatus',
            width: 90
          },
          {
            type: ColumnType.Action,
            label: 'Action',
            width: 100,
            action: [
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-eye',
                type: Act.ViewDetail,
                descr: 'View Detail'
              },
              {
                class: CommonConstant.TEXT_PRIMARY,
                icon: 'ft-file-text',
                type: Act.ViewRelatedDocument,
                descr: 'Related Document',
                condition: true,
                conditionedClass: 'd-none',
                conditionVariable: 'documentDetailStatus',
                conditionExpected: "0",
              }
            ]
          }
        ]
      };
  
      this.view = {
        title: 'Inquiry Audit Trail',
        components: [
          {
            type: WidgetType.SearchFilter,
            component: searchFilter
          },
          {
            type: WidgetType.Datatable,
            component: inquiryAuditTrail
          }
        ]
      }
  
    }
  
  }
  