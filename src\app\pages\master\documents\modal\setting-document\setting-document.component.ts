import {Component, Input, OnInit} from '@angular/core';
import {Form<PERSON>uilder, FormGroup, Validators} from '@angular/forms';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import {TemplateService} from '../../../../../services/api/template.service';
import {SettingTemplateRequest} from '../../../../../model/api/setting.template.request';
import {DocumentTemplate} from '../../../../../model/template';
import {ToastrService} from 'ngx-toastr';
import { CommonConstant } from 'app/shared/constant/common.constant';

@Component({
  selector: 'app-setting-document',
  templateUrl: './setting-document.component.html',
  styleUrls: ['./setting-document.component.scss']
})
export class SettingDocumentComponent implements OnInit {
  @Input()
  document: DocumentTemplate;

  public waiting: boolean;
  public settingForm: FormGroup;

  constructor(private formBuilder: FormBuilder, public activeModal: NgbActiveModal,
              public service: TemplateService, private toastr: ToastrService) {
    this.waiting = false;
    console.log('Document', this.document);
  }

  ngOnInit(): void {
    console.log('Document', this.document);
    this.settingForm = this.formBuilder.group({
      params: [JSON.stringify({
        documentTemplateCode: '',
        signerTypeCode: 'MF',
        signTypeCode: 'TTD',
        signPage: '1',
        signLocation: ''
      }), Validators.required]
    });
  }

  onSubmit() {
    console.log('Data', this.settingForm.value);
    const formDat = this.settingForm.value;
    const jsonDat = JSON.parse(formDat.params);
    console.log('Parse', jsonDat);

    const request: SettingTemplateRequest = new SettingTemplateRequest();
    request.documentTemplateCode = jsonDat.documentTemplateCode;
    request.signerTypeCode = jsonDat.signerTypeCode;
    request.signLocation = jsonDat.signLocation;
    request.signPage = jsonDat.signPage;
    request.signTypeCode = jsonDat.signTypeCode;

    console.log('Request', request);
    this.service.setting(request).subscribe(response => {
      console.log('Setting Document', response);
      this.activeModal.dismiss({data: request});
    }, err => {
      this.toastr.error(err.message, null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      })
    })
  }

}
