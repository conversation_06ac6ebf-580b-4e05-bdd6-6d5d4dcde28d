<div class="row" style="margin-top: 15px">
  <div class="col-6">
    <div class="content-header" style="margin-top: 0 !important" translate>
      Setting PSrE
    </div>
  </div>
</div>

<div class="row">
  <div class="col-6">
    <div class="card">
      <div class="card-content">
        <div class="card-body">
          <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
            <div class="row">
              <div class="col-{{ formObj.colSize }}">
                <app-text
                  [form]="userForm"
                  [question]="getQuestion('vendorCode')"
                  [direction]="formObj.direction"
                >
                </app-text>
              </div>
            </div>
            <div class="row">
              <div class="col-{{ formObj.colSize }}">
                <app-text
                  [form]="userForm"
                  [question]="getQuestion('vendorName')"
                  [direction]="formObj.direction"
                >
                </app-text>
              </div>
            </div>
            <div class="row">
              <div class="col-{{ formObj.colSize }}">
                <app-select
                  [form]="userForm"
                  [question]="getQuestion('status')"
                  [direction]="formObj.direction"
                ></app-select>
              </div>
            </div>
            <div class="row">
              <div class="col-{{ formObj.colSize }}">
                <app-select
                  [form]="userForm"
                  [question]="getQuestion('statusOperating')"
                  [direction]="formObj.direction"
                ></app-select>
              </div>
            </div>
            <div class="row">
              <div class="col-{{ formObj.colSize }}">
                <app-select
                  [form]="userForm"
                  [question]="getQuestion('paymentSignType')"
                  [direction]="formObj.direction"
                ></app-select>
              </div>
            </div>
            <div class="row">
              <div class="col-12 text-center">
                <button
                  class="btn btn-info mr-5"
                  type="submit"
                  [disabled]="userForm.invalid"
                  translate
                >
                  Save
                </button>
                <button
                  class="btn btn-light"
                  type="button"
                  (click)="onCancel()"
                  translate
                >
                  Cancel
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
