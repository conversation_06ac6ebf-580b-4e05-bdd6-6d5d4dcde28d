import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { MsxDatatableComponent } from "app/shared/components/msx-datatable/msx-datatable.component";
import { PipeModule } from "app/shared/pipes/pipe.module";
import { SharedModule } from "app/shared/shared.module";
import { PdfJsViewerModule } from "ngx-pdfjs-viewer";
import { NgxSpinnerModule } from "ngx-spinner";
import { ErrorReportDetailComponent } from "./error-report-detail/error-report-detail.component";
import { ErrorReportRoutingModule } from "./error-report-routing.module";
import { ErrorReportComponent } from "./error-report.component";
import { ActStatusComponent } from "./modal/act-status/act-status.component";

@NgModule({
    exports: [
      MsxDatatableComponent
    ],
    declarations: [
      ErrorReportComponent,
      ErrorReportDetailComponent,
      ActStatusComponent
    ],
    imports: [
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      NgSelectModule,
      NgbModule,
      PipeModule,
      NgxDatatableModule,
      SharedModule,
      PdfJsViewerModule,
      NgxSpinnerModule,
      ErrorReportRoutingModule
    ],
    entryComponents: [
      ActStatusComponent
    ]
  })
  export class ErrorReportModule { }