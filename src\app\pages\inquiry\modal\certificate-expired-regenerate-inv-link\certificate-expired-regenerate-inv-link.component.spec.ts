import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CertificateExpiredRegenerateInvLinkComponent } from './certificate-expired-regenerate-inv-link.component';

describe('CertificateExpiredRegenerateInvLinkComponent', () => {
  let component: CertificateExpiredRegenerateInvLinkComponent;
  let fixture: ComponentFixture<CertificateExpiredRegenerateInvLinkComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CertificateExpiredRegenerateInvLinkComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CertificateExpiredRegenerateInvLinkComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
