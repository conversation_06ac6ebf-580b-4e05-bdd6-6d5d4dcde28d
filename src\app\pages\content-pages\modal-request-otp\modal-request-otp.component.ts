import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { NavigationExtras, Router } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { BaseResponse } from 'app/model/api/base.response';
import { UpdateActivationUserRequest } from 'app/model/api/updateActivationUser.request';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { SendOtpBySMSRequest } from 'app/shared/dto/otp/send-otp-by-sms.request';
import { VerifyOtpBySMSRequest } from 'app/shared/dto/otp/verify-otp-by-sms.request';
import { environment } from 'environments/environment';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { CountlyService } from 'app/services/api/countly.service';
import { OtpActivationResponse } from 'app/model/api/otp-activation.response';

@Component({
  selector: 'app-modal-request-otp',
  templateUrl: './modal-request-otp.component.html',
  styleUrls: ['./modal-request-otp.component.scss']
})
export class ModalRequestOtpComponent implements OnInit {

  @Input() msg: string;
  @Input() phone: string;
  @Input() email: string;
  @Input() password: string;
  @Input() vendor: string;
  @Input() verificationType: string;
  @Input() sendMedia: string;
  @Input() listSendPoints: string[];
  @Input() isWa: boolean;
  @Input() isSms: boolean;
  @Input() resendDuration;

  nik :any;
  name :any;
  registeredDate :any;
  templateForm: any;
  resendOtpForm: any;
  redirectUrl: any;
  redirectCountDown: any;

  timeLeft;
  interval: any;

  swal = swalFunction;

  constructor(public activeModal: NgbActiveModal, private formBuilder: FormBuilder, private toastrService: ToastrService,
      private http: HttpClient, private router: Router, private countlyService: CountlyService) {
  }

  ngOnInit(): void {
    //this.getUserphone();
    this.templateForm = this.formBuilder.group({
      otp: ['', Validators.required]
    });

    this.resendOtpForm = this.formBuilder.group({
      sendMedia: ['', Validators.required]
    });

    this.resendOtpForm.patchValue({
      sendMedia: this.sendMedia
    });

    if (localStorage.getItem('timeLeft') != null) {
      this.timeLeft = Number(localStorage.getItem('timeLeft'));
      this.startTimer();
    }
    this.timeLeft = this.resendDuration;
    clearInterval(this.interval);
    this.startTimer();
    this.countlyService.initiate();
  }

  dismiss() {
    this.activeModal.dismiss('0');
    clearInterval(this.interval);
    localStorage.setItem('timeLeft', this.timeLeft.toString());
    
  }
  openSuccessPopup() {
    this.toastrService.success('Aktivasi berhasil', null, {
      positionClass: 'toast-top-right'
    });
  }

  openWrongCodePopup() {
    this.swal.Error('Kode OTP tidak sesuai!');
  }

  onSubmit() {
      const request = new VerifyOtpBySMSRequest();
      request.msg = this.msg;
      request.phoneNo = this.phone;
      request.otpCode = this.templateForm.controls.otp.value;

      this.http.post<BaseResponse>(URLConstant.verifyOtpActivationUser, request).subscribe(
        async (response) => {
          if (response.status.code!== 0) {
            this.swal.Error(response.status.message);
            console.log('Error', response.status.message);
            return;
          }
          
          this.activeModal.close(request.otpCode);
              
          await this.updateActivationUser();
        }
      );
  }

  async updateActivationUser(){
    const request = new UpdateActivationUserRequest;
    request.msg = this.msg;
    request.phoneNo = this.phone;
    request.password = this.password;
    request.audit = {
      callerId: this.email
    };
    this.http.post(URLConstant.updateActivationUser, request).subscribe(
      (response) => {
        if (response['status']['code'] == 0) {
          console.log(response);
          this.nik= response['idNo'];
          this.name  = response['fullName'];
          this.registeredDate = response['registeredDate'];
          this.redirectUrl = response['redirectUrl'] || null;
          this.redirectCountDown = response['redirectCountDown'];
          this.openSuccessPopup();
          this.goToSuccessPage();
        }
        console.log('Error', response);
      }
    )
  }

  goToSuccessPage(){
    if(this.vendor === 'VIDA'){
      this.router.navigate([PathConstant.SUCCESS_REGISTER_VIDA],
        {state: {
          nik: this.nik,
          name: this.name,
          email: this.email,
          phone: this.phone,
          redirectCountDown : this.redirectCountDown,
          redirectUrl : this.redirectUrl,
          registeredDate:this.registeredDate
       }});
    } else{
      this.router.navigate([PathConstant.SUCCESS_REGISTER],
        {state: {
          nik: this.nik,
          name: this.name,
          email: this.email,
          phone: this.phone,
          redirectCountDown : this.redirectCountDown,
          redirectUrl : this.redirectUrl,
       }});
    }
  }

  sendOtp() {
      const request = new SendOtpBySMSRequest();
      request.msg = this.msg;
      request.phoneNo = this.phone;
      request.audit.callerId = this.msg;
      request.sendingPointOption = this.resendOtpForm.controls.sendMedia.value;
      this.http.post<OtpActivationResponse>(URLConstant.sentOtpActivationUser, request).subscribe(
        (response) => {
          if (response.status.code!== 0) {
            this.swal.Error(response.status.message);
            console.log('Error', response.status.message);
            return;
          }
          else {
            this.timeLeft = response.durationResendOTP;
            clearInterval(this.interval);
            this.startTimer();
          }
        }
      );

    
  }

  startTimer() {
    this.interval = setInterval(() => {
      if (this.timeLeft > 1) {
        this.timeLeft--;
      } else {
        this.timeLeft = -1;
      }
    }, 1000);
  }
}
