import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseRequest } from 'app/model/api/base.request';
import { DataService } from 'app/services/api/data.service';
import { MenuService } from 'app/services/api/menu.service';
import { GlobalService } from 'app/shared/data/global.service';
import { GetListMenuOfRoleRequest } from 'app/shared/dto/menu/get-list-menu-of-role.request';
import { UpdateMenuOfRoleRequest } from 'app/shared/dto/menu/update-menu-of-role.request';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-modify-menu-of-role',
  templateUrl: './modify-menu-of-role.component.html',
  styleUrls: ['./modify-menu-of-role.component.scss']
})
export class ModifyMenuOfRoleComponent implements OnInit {

  state: any; //tenantCode, roleCode
  routeData: any;
  menus: any[];
  menusOfRole: any[];
  tenantCode: string;
  roleCode: string;
  roleName: string;
  menusOfRoleCode: string[];

  constructor(private activatedRoute: ActivatedRoute, private router: Router, private dataService: DataService, private location: Location,
      private toastrService: ToastrService, private spinner: NgxSpinnerService, private global: GlobalService, private menuService: MenuService) 
      {
        spinner.show();
        this.routeData = this.activatedRoute.snapshot.data;

        this.activatedRoute.queryParams.subscribe(() => {
          this.state = this.router.getCurrentNavigation().extras.state;
          console.log('state', this.state);
        });

        this.tenantCode = this.state.tenantCode;
        this.roleCode = this.state.roleCode;
        this.roleName = this.state.roleName;

        this.getMenus();
        this.getMenusOfRole();
        spinner.hide();
      }

  ngOnInit(): void {
    // Currently empty. Only fill necessary 
  }

  getMenus() {
    const request = new BaseRequest();
    request.audit = this.global.audit;
    this.menuService.getListManageableMenu(request).subscribe(
      (response) => {
        if (response.status.code === 0) {
          this.menus = response.menus;
        } else {
          this.toastrService.error(response.status.message);
        }
      }
    );
  }

  async getMenusOfRole() {
    const request = new GetListMenuOfRoleRequest();
    request.audit = this.global.audit;
    request.roleCode = this.roleCode;
    request.tenantCode = this.tenantCode;
    this.menuService.getListMenuOfRole(request).subscribe(
      (response) => {
        if (response.status.code === 0) {
          this.menusOfRole = response.menus;
          response.menus.forEach(
            function (item) {
              this.menusOfRoleCode.push(item.code);
            }
          );
        } else {
          this.toastrService.error(response.status.message);
        }
      }
    );
  }

  save() {
    var menus = [] as string[];
    this.menus.forEach(
      (menu) => {
        const id = menu.code;
        const active = (<HTMLInputElement> document.getElementById(id)).checked;
        if (active) {
          menus.push(menu.code);
        }
      }
    );

    const request = new UpdateMenuOfRoleRequest();
    request.menus = menus;
    request.roleCode = this.roleCode;
    request.tenantCode = this.tenantCode;
    request.audit = this.global.audit;
    this.menuService.updateMenuOfRole(request).subscribe(
      (response) => {
        if (response['status']['code'] != 0) {
          this.toastrService.error(response['status']['message']);
        } else {
          this.toastrService.success('Update Menu of Role berhasil.')
        }
      }
    );

    this.location.back();
  }

  isChecked(menu: any) {
    const isChecked = this.menusOfRole.some(e => e.code === menu);
    console.log("menu" + menu + " isChecked" + isChecked)
    return isChecked;
  }

  goBack() {
    this.location.back();
  }

}
