import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';

@Component({
    selector: 'app-error-page',
    templateUrl: './error-page.component.html',
    styleUrls: ['./error-page.component.scss']
})

export class ErrorPageComponent {
  messages: string;

  constructor(private router: Router) {
    const state = this.router.getCurrentNavigation().extras.state;
    this.messages = state?.msg || 'Akses tidak di ijinkan!';
  }
}
