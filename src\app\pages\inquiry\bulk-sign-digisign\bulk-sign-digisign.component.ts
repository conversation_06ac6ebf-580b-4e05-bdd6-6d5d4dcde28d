import { AfterViewInit, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { GlobalService } from 'app/shared/data/global.service';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-bulk-sign-digisign',
  templateUrl: './bulk-sign-digisign.component.html',
  styleUrls: ['./bulk-sign-digisign.component.scss']
})
export class BulkSignDigisignComponent implements OnInit, AfterViewInit {
  url: string;

  constructor(private activateRoute: ActivatedRoute, private spinner: NgxSpinnerService, private global: GlobalService) {
    this.activateRoute.queryParams.subscribe(params => {
      if (params['url'] != null) {
        this.url = params['url'];
      }
    })
  }

  ngAfterViewInit(): void {
  }

  ngOnInit(): void {
    if (this.global.user.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      localStorage.clear();
      window.location.href = this.url;
    }
  }

}
