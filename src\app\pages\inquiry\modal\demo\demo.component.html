<!--<div class="text-center align-item-center">-->
<!--  <img [src]="image" alt="Request OTP" class="img-responsive" (click)="dismiss()" />-->
<!--</div>-->
<form [formGroup]="templateForm" (ngSubmit)="onSubmit()">
  <div class="modal-header">
    <h4 class="modal-title">Proses Tanda Tangan Dokumen</h4>
    <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <div class="row justify-content-center align-items-center">
      <label>Anda <b>setuju</b> untuk tandatangan dokumen ini ?</label>
    </div>

    <div class="form-group">
      <div class="radio">
        <div class="row justify-content-center align-items-center mb-2">
          <div class="p-2">
            <input formControlName="isOtp" name="isOtp" class="form-check-input" type="radio" id="p1" value="Iya">
            <label class="form-check-label" for="p1"> Iya </label>
          </div>
          <div class="p-2">
            <input formControlName="isOtp" name="isOtp" class="form-check-input" type="radio" id="p2" value="Tidak">
            <label class="form-check-label" for="p2"> Tidak </label>
          </div>
        </div>
      </div>
    </div>

    <textarea *ngIf="templateForm.controls['isOtp'].value === 'Tidak'" formControlName="reason" id="reason" rows="3" style="resize: none;" placeholder=" Alasan tidak menyetujui dokumen ini..." class="form-control mb-2"></textarea>

    <div class="m-3">
      <div class="row justify-content-center align-items-center">
        <label><b>Masukkan OTP</b></label>
      </div>
      <div class="row justify-content-center align-items-center">
        <div *ngIf="isOtpSms" id="ketsms" style="">
          <p> OTP telah dikirim ke 0812****5580  </p>
        </div>
        <div *ngIf="isOtpEmail" id="ketmail" style="">
          <p> OTP telah dikirim ke *an*ra@m*il.c*m  </p>
        </div>
      </div>
      <input formControlName="otp" type="tel" class="letterotp" maxlength="6" id="otp" value="">
    </div>

    <div class="row justify-content-center align-items-center mt-2">
      <div *ngIf="timeLeft > 0">Tunggu {{ timeLeft }} detik</div>
      <div *ngIf="timeLeft === -1" id="ketotp" style="color: #F55252">Tidak terima, Kirim kembali OTP ?</div>
    </div>

    <div class="row justify-content-center align-items-center mt-2 mb-2">
      <div class="col-sm-6 d-flex justify-content-center">
        <div class="btn-group">
          <button id="btnotp" type="button" class="btn btn-success" (click)="sendOtpSms()" [disabled]="isOtp">
            <img style="max-width: 25px;" src="./assets/demo/ttd_files/mobile.png"> SMS
          </button>
          <button id="otemail" type="button" class="btn btn-info" (click)="sendOtpEmail()" [disabled]="isOtp">
            <img style="max-width: 25px;" src="./assets/demo/ttd_files/mail.png"> Email
          </button>
        </div>
      </div>
    </div>

  </div>

  <div class="modal-footer">
    <button type="submit" class="btn btn-info btn-block" [disabled]="templateForm.invalid">Proses Dokumen</button>
  </div>
</form>

