import { ErrorReport } from './../../../model/error-report';
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";

export const ErrorReportDetailTable: Table<ErrorReport> = {
    name: 'listErrorHistory',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'errorMessage',
            label: 'Message',
            width: 120
        }
    ]
}

export const ErrorReportDetailListView: MsxView = {
    title: 'Error Report Detail',
    components: [
        {
            type: WidgetType.Datatable,
            component: ErrorReportDetailTable
        }
    ]
}