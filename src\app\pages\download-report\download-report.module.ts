import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DownloadReportComponent } from './download-report.component';
import { DownloadReportRoutingModule } from './download-report-routing.module';
import { TranslateModule } from '@ngx-translate/core';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ReactiveFormsModule } from '@angular/forms';
import { MsFormModule } from 'app/shared/components/ms-form/ms-form.module';
import { SharedModule } from 'app/shared/shared.module';
import { ContentPagesModule } from '../content-pages/content-pages.module';



@NgModule({
  declarations: [DownloadReportComponent],
  imports: [
    CommonModule,
    DownloadReportRoutingModule,
    TranslateModule,
    NgbModule,
    ReactiveFormsModule,
    MsFormModule,
    SharedModule,
    ContentPagesModule

  ]
})
export class DownloadReportModule { }
