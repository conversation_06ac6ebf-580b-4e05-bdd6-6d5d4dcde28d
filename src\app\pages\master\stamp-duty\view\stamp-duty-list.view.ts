import { StampDuty } from "app/model/stamp-duty";
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { MsxView } from "app/shared/components/msx-view/models/MsxView";
import { WidgetType } from "app/shared/components/msx-view/models/WidgetType";
import {QuestionDate, QuestionDropdown, QuestionTextbox} from '../../../../shared/components/ms-form/questions';
import {URLConstant} from '../../../../shared/constant/URLConstant';
import { CommonConstant } from "app/shared/constant/common.constant";

const StampDutySearchFilter: FormModel<string> = {
    name: 'stampDutySearchForm',
    direction: FormConstant.DIRECTION_HORIZONTAL,
    colSize: 6,
    exportExcel: true,
    components: [
        new QuestionTextbox({
          key: 'invoiceNo',
          label: 'Reference Number',
          placeholder: 'Type reference number here',
          value: ''
        }),
        new QuestionDropdown({
          key: 'stampDutyStatus',
          label: 'Stamp Duty Status',
          placeholder: 'Select stamp duty status',
          serviceUrl: URLConstant.GetLov,
          options: [
            {key: '', value: 'All'}
          ],
          params: {
            lovGroup: 'STAMP_DUTY_STATUS'
          },
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          value: ''
        }),
        new QuestionDropdown({
            key: 'businessLineCode',
            label: 'Business Line',
            placeholder: 'Select business line',
            serviceUrl: URLConstant.BusinessLineList,
            options: [
              {key: '', value: 'All'}
            ],
            args: {
              list: 'businessLineList',
              key: 'businessLineCode',
              value: 'businessLineName'
            },
            value: ''
        }),
        new QuestionDropdown({
            key: 'regionCode',
            label: 'Region',
            placeholder: 'Select region',
            serviceUrl: URLConstant.RegionList,
            options: [
              {key: '', value: 'All'}
            ],
            args: {
              list: 'regionList',
              key: 'regionCode',
              value: 'regionName'
            },
            value: ''
        }),
        new QuestionDropdown({
            key: 'officeCode',
            label: 'Office',
            placeholder: 'Select office',
            serviceUrl: URLConstant.OfficeList,
            options: [
              {key: '', value: 'All'}
            ],
            args: {
              list: 'officeList',
              key: 'officeCode',
              value: 'officeName'
            },
            value: ''
        }),
        new QuestionDate({
          key: 'stampDutyUsedDtStart',
          label: 'Used Date From',
          placeholder: CommonConstant.FORMAT_DATE,
          isDateStart: true
        }),
        new QuestionDate({
          key: 'stampDutyUsedDtEnd',
          label: 'Used Date To',
          placeholder: CommonConstant.FORMAT_DATE,
          isDateEnd: true
        }),
        {
            key: 'stampDutyNo',
            label: 'Stamp Duty No',
            placeholder: 'Type stamp duty number here',
            controlType: FormConstant.TYPE_TEXT,
            value: ''
        }
    ],
    params: [
        {
            key: 'page',
            controlType: FormConstant.TYPE_HIDDEN,
            value: 1
        }
    ]
};

const StampDutyTable: Table<StampDuty> = {
    name: 'listStampDuty',
    list: [],
    columns: [
        {
            type: ColumnType.Link,
            prop: 'stampDutyNo',
            label: 'Stamp Duty No',
            width: 150
        },
        {
            type: ColumnType.Text,
            prop: 'invoiceNo',
            label: 'Reference Number',
            width: 100
        },
        {
          type: ColumnType.Date,
          format: 'DD-MMM-YYYY',
          prop: 'stampDutyUsedDt',
          label: 'Used Date',
          width: 80
        },
        {
            type: ColumnType.Currency,
            prop: 'stampDutyFee',
            label: 'Fee',
            width: 50
        },
        {
            type: ColumnType.Text,
            prop: 'officeName',
            label: 'Office',
            width: 80
        },
        {
            type: ColumnType.Text,
            prop: 'regionName',
            label: 'Region',
            width: 80
        },
        {
            type: ColumnType.Text,
            prop: 'businessLineName',
            label: 'Business Line',
            width: 150
        },
        {
            type: ColumnType.Text,
            prop: 'stampDutyStatusCode',
            label: 'Status',
            width: 70
        }
    ]
};

export const StampDutyListView: MsxView = {
    title: 'Stamp Duty',
    components: [
        {
            type: WidgetType.SearchFilter,
            component: StampDutySearchFilter
        },
        {
            type: WidgetType.Datatable,
            component: StampDutyTable
        }
    ]
};
