export class BoxAnnotation {
  id: number;
  top: number;
  left: number;
  width: number;
  height: number;
  label: string;
  name?: string;
  phone?: string;
  email?: string;
  signerType?: string;
  type?: string;
  page?: number;
  transform?: string;
  notes?: string;
  coordinate?: string;
  coordinateVida?: string;
  coordinatePrivy?: string;
  position?: {
    left: number,
    top: number,
    right: number,
    bottom: number
  };
  viewport?: any;
  lx?: number;
  ly?: number;
  rx?: number;
  ry?: number;

  x?: number;
  y?: number;
  w?: number;
  h?: number;

}
