<div class="modal-body">
    <button type="button" class="close" aria-label="close" (click)="activeModal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
    </button>
    <p class="modal-title" translate>Signer Verification</p>
    <div class="modal-body">

        <form [formGroup]="verificationForm" (ngSubmit)="signerVerification()">
            <div class="form-group">
              <label for="Email" class="form-title">Email</label>
              <input type="Email" formControlName="Email" id="Email" class="form-control mb-2" placeholder="Email" value="{{email}}"
                    
              required disabled>
            </div>

            <div class="form-group">
                <label for="phoneNo" class="form-title" translate>Phone Number</label>
                <input type="phoneNo" formControlName="PhoneNo" id="phoneNo" class="form-control mb-2" placeholder="No Handphone" value="{{phoneNo}}"
                      
                required disabled>
              </div>

            <div class="form-group" *ngIf="this.isNoPassword === '0'" >
                <label for="name" class="form-title" translate>Password<span class="mandatory text-danger">*</span></label>
                <div class="input">
                  <i class="ft-lock"></i>
                  <input [type]="isShowPassword ? 'text' : 'password'" formControlName="password" class="form-control" [placeholder]="'Password' | translate"
                         required>
                  <i style="cursor: pointer;" [ngClass]="{'ft-eye-off': !isShowPassword, 'ft-eye': isShowPassword}" (click)="toggleIsShowPassword()"></i>
                </div>
             </div> 
            
             <div class="row">
              <div class="col-6"  *ngIf="this.live === 1 || this.live === 2" >  
                <span class="border rounded border-primary d-block" (click)="onClickBio()" >
                  <div class="form-check form-check-inline align-middle p-3">
                    <input class="form-check-input" type="radio" id="biometric"  value="Biometric" formControlName="verification" [(ngModel)]="default" [checked]="BioChecked">
                    <label class="form-check-label label-radio-button" for="biometric" translate>Biometric [Liveness]</label>
                  </div>
                </span>
            </div>
            
             <div class="col-6" *ngIf="this.live === 0 || this.live === 2" >
                <span class="border rounded border-primary d-block" (click)="onClickOTP()" [ngStyle]="isMobile ? { 'height.px': '62.4' } : {}">
                  <div class="form-check form-check-inline align-middle p-3" [ngStyle]="isMobile ? { 'display': 'flex', 'align-items': 'center', 'margin-top.px': '5' } : {}">
                    <input class="form-check-input" type="radio" id="OTP" value="OTP" formControlName="verification" [(ngModel)]="default" [checked]="OTPChecked">
                    <label class="form-check-label label-radio-button" for="OTP" translate>OTP</label>
                  </div>
                </span>
             </div>
            </div> 

             <!-- otp media option -->
             <div class="form-group" *ngIf="isOtp && !isPrivy">
              <label for="gender" class="form-title" translate>OTP Delivery Medium<span class="mandatory text-danger">*</span></label>
                <div class="row">
                  <div class="col-12" *ngIf="isSms" [ngStyle]="{ 'margin-top.px': '0' }">
                    <span class="border rounded border-primary d-block">
                      <div class="form-check form-check-inline align-middle p-3">
                        <input class="form-check-input" type="radio" id="sms" formControlName="sendMedia" value="SMS">
                        <label class="form-check-label label-radio-button" for="sms" translate>SMS</label>
                      </div>
                    </span>
                  </div>
                  <div class="col-12" *ngIf="isWa" [ngStyle]="{ 'margin-top.px': '10' }">
                    <span class="border rounded border-primary d-block">
                      <div class="form-check form-check-inline align-middle p-3">
                        <input class="form-check-input" type="radio" id="wa" formControlName="sendMedia" value="WA">
                        <label class="form-check-label label-radio-button" for="wa" translate>WhatsApp</label>
                      </div>
                    </span>
                  </div>
                  <div class="col-12" *ngIf="isEmail" [ngStyle]="{ 'margin-top.px': '10' }">
                    <span class="border rounded border-primary d-block">
                      <div class="form-check form-check-inline align-middle p-3">
                        <input class="form-check-input" type="radio" id="email" formControlName="sendMedia" value="EMAIL">
                        <label class="form-check-label label-radio-button" for="email" translate>Email</label>
                      </div>
                    </span>
                  </div>
                </div>
              </div>

            <div class="row-1">
            <div class="checkbox auth-checkbox">
              <input type="checkbox" formControlName="check" class="form-control" id="rememberMe" [(ngModel)]="isChecked">
              <label for="rememberMe"><span class="font-small-2 mb-3 font-weight-normal" translate>&nbsp;I agree to sign this document</span></label>
            </div>
          </div>

            <div class="row">
                <div class="col-12 text-center">
                  <button class="btn btn-success" type="submit" [disabled]="verificationForm.invalid || isSubmitting" (onClick)="signerVerification()" translate>Next</button>
                </div>
            </div>
        </form>

    </div>
</div>