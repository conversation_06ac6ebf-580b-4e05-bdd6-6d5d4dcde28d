import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import * as Chartist from 'chartist';
import { ChartType, ChartEvent } from 'ng-chartist';
import ChartistTooltip from 'chartist-plugin-tooltips-updated';
import { DashboardDocument } from './data/dashboard.data';
import { ColumnMode } from '@swimlane/ngx-datatable';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DashboardTableBranchManager, DashboardTableBranchManagerMobile, DashboardTableCustomer, DashboardTableCustomerMobile } from './view/dashboard1.view';
import { BehaviorSubject } from 'rxjs';
import { InquiryRequest } from 'app/shared/dto/inquiry/inquiry.request';
import { GlobalService } from 'app/shared/data/global.service';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { HttpClient } from '@angular/common/http';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { Page } from 'app/shared/components/msx-datatable/models/page';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { Document } from 'app/model/document';
import { ViewDocumentRequest } from 'app/model/api/view.document.request';
import { ViewDocumentResponse } from 'app/model/api/view.document.response';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { SignerComponent } from 'app/pages/inquiry/modal/signer/signer.component';
import { InquiryResponse } from 'app/shared/dto/inquiry/inquiry.response';
import { UserProfile } from 'app/model/user-profile';
import { DeviceDetectorService } from 'ngx-device-detector';
import { TenantSettingRequest } from 'app/shared/dto/tenant-setting/tenant-setting.request';
import { ToastrService } from 'ngx-toastr';
import { ColumnType } from 'app/shared/components/msx-card-datatable/enums/column-type';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { SignDocumentRequest } from 'app/model/api/sign.document.request';
import { CheckStatusDocumentService } from 'app/services/check-status-document.service';
import { SignDocumentResponse } from 'app/model/api/sign.document.response';
import { Vendor } from 'app/shared/constant/vendor';
import { CountlyService } from 'app/services/api/countly.service';

declare var require: any;


const data: any = require('../../../shared/data/chartist.json');

export interface Chart {
  type: ChartType;
  data: Chartist.IChartistData;
  options?: any;
  responsiveOptions?: any;
  events?: ChartEvent;
  // plugins?: any;
}

@Component({
  selector: 'app-dashboard1',
  templateUrl: './dashboard1.component.html',
  styleUrls: ['./dashboard1.component.scss', '/assets/sass/libs/datatables.scss'],
  encapsulation: ViewEncapsulation.None
})

export class Dashboard1Component implements OnInit {

  // Line area chart configuration Starts
  lineArea: Chart = {
    type: 'Line',
    data: data['lineAreaDashboard'],
    options: {
      low: 0,
      showArea: true,
      fullWidth: true,
      onlyInteger: true,
      axisY: {
        low: 0,
        scaleMinSpace: 50,
      },
      plugins: [
        ChartistTooltip({
          appendToBody: true,
          pointClass: CommonConstant.CT_POINT_REGULER
        })
      ],
      axisX: {
        showGrid: false
      }
    },
    events: {
      created(data: any): void {
        const defs = data.svg.elem('defs');
        defs.elem('linearGradient', {
          id: 'gradient',
          x1: 0,
          y1: 1,
          x2: 1,
          y2: 0
        }).elem('stop', {
          offset: 0,
          'stop-color': 'rgba(0, 201, 255, 1)'
        }).parent().elem('stop', {
          offset: 1,
          'stop-color': 'rgba(146, 254, 157, 1)'
        });

        defs.elem('linearGradient', {
          id: 'gradient1',
          x1: 0,
          y1: 1,
          x2: 1,
          y2: 0
        }).elem('stop', {
          offset: 0,
          'stop-color': 'rgba(132, 60, 247, 1)'
        }).parent().elem('stop', {
          offset: 1,
          'stop-color': 'rgba(56, 184, 242, 1)'
        });
      },
      draw(data: any): void {
        if (data.type === 'point') {
          const circle = new Chartist.Svg('circle', {
            cx: data.x,
            cy: data.y,
            r: 4,
            'ct:value': data.value.y,
            'ct:meta': data.meta,
            style: CommonConstant.POINTER_EVENTALL_IMPORTANT,
            class: CommonConstant.CT_POINT_REGULER
          });
          data.element.replace(circle);
        }
      }
    },
  };
  // Line area chart configuration Ends

  // Stacked Bar chart configuration Starts
  Stackbarchart: Chart = {
    type: 'Bar',
    data: data['Stackbarchart'],
    options: {
      stackBars: true,
      fullWidth: true,
      axisX: {
        showGrid: false,
      },
      axisY: {
        showGrid: false,
        showLabel: false,
        offset: 0
      },
      chartPadding: 30
    },
    events: {
      created(data: any): void {
        const defs = data.svg.elem('defs');
        defs.elem('linearGradient', {
          id: 'linear',
          x1: 0,
          y1: 1,
          x2: 0,
          y2: 0
        }).elem('stop', {
          offset: 0,
          'stop-color': '#7441DB'
        }).parent().elem('stop', {
          offset: 1,
          'stop-color': '#C89CFF'
        });
      },
      draw(data: any): void {
        if (data.type === 'bar') {
          data.element.attr({
            style: 'stroke-width: 5px',
            x1: data.x1 + 0.001
          });

        } else if (data.type === 'label') {
          data.element.attr({
            y: 270
          })
        }
      }
    },
  };
  // Stacked Bar chart configuration Ends

  // Line area chart 2 configuration Starts
  lineArea2: Chart = {
    type: 'Line',
    data: data['lineArea2'],
    options: {
      showArea: true,
      fullWidth: true,
      lineSmooth: Chartist.Interpolation.none(),
      axisX: {
        showGrid: false,
      },
      axisY: {
        low: 0,
        scaleMinSpace: 50
      },
      plugins: [
        ChartistTooltip({
          appendToBody: true,
          pointClass: CommonConstant.CT_POINT_CIRCLE
        })
      ],
    },
    responsiveOptions: [
      ['screen and (max-width: 640px) and (min-width: 381px)', {
        axisX: {
          labelInterpolationFnc: function (value, index) {
            return index % 2 === 0 ? value : null;
          }
        }
      }],
      ['screen and (max-width: 380px)', {
        axisX: {
          labelInterpolationFnc: function (value, index) {
            return index % 3 === 0 ? value : null;
          }
        }
      }]
    ],
    events: {
      created(data: any): void {
        const defs = data.svg.elem('defs');
        defs.elem('linearGradient', {
          id: 'gradient2',
          x1: 0,
          y1: 1,
          x2: 0,
          y2: 0
        }).elem('stop', {
          offset: 0,
          'stop-opacity': '0.2',
          'stop-color': 'transparent'
        }).parent().elem('stop', {
          offset: 1,
          'stop-opacity': '0.2',
          'stop-color': '#60AFF0'
        });

        defs.elem('linearGradient', {
          id: 'gradient3',
          x1: 0,
          y1: 1,
          x2: 0,
          y2: 0
        }).elem('stop', {
          offset: 0.3,
          'stop-opacity': '0.2',
          'stop-color': 'transparent'
        }).parent().elem('stop', {
          offset: 1,
          'stop-opacity': '0.2',
          'stop-color': '#6CD975'
        });
      },
      draw(data: any): void {
        const circleRadius = 4;
        if (data.type === 'point') {

          const circle = new Chartist.Svg('circle', {
            cx: data.x,
            cy: data.y,
            r: circleRadius,
            'ct:value': data.value.y,
            'ct:meta': data.meta,
            style: CommonConstant.POINTER_EVENTALL_IMPORTANT,
            class: CommonConstant.CT_POINT_CIRCLE
          });
          data.element.replace(circle);
        } else if (data.type === 'label') {
          // adjust label position for rotation
          const dX = data.width / 2 + (30 - data.width)
          data.element.attr({ x: data.element.attr('x') - dX })
        }
      }
    },
  };
  // Line area chart 2 configuration Ends

  // Line chart configuration Starts
  lineChart: Chart = {
    type: 'Line', data: data['LineDashboard'],
    options: {
      axisX: {
        showGrid: false
      },
      axisY: {
        showGrid: false,
        showLabel: false,
        low: 0,
        high: 100,
        offset: 0,
      },
      plugins: [
        ChartistTooltip({
          appendToBody: true,
          currency: '$',
          pointClass: CommonConstant.CT_POINT_CIRCLE
        })
      ],
      fullWidth: true,
      offset: 0,
    },
    events: {
      draw(data: any): void {
        const circleRadius = 4;
        if (data.type === 'point') {
          const circle = new Chartist.Svg('circle', {
            cx: data.x,
            cy: data.y,
            r: circleRadius,
            'ct:value': data.value.y,
            'ct:meta': data.meta,
            style: CommonConstant.POINTER_EVENTALL_IMPORTANT,
            class: CommonConstant.CT_POINT_CIRCLE
          });

          data.element.replace(circle);
        } else if (data.type === 'label') {
          // adjust label position for rotation
          const dX = data.width / 2 + (30 - data.width)
          data.element.attr({ x: data.element.attr('x') - dX })
        }
      }
    },

  };
  // Line chart configuration Ends

  // Donut chart configuration Starts
  DonutChart: Chart = {
    type: 'Pie',
    data: data['donutDashboard'],
    options: {
      donut: true,
      startAngle: 0,
      labelInterpolationFnc: function (value) {
        const total = data['donutDashboard'].series.reduce(function (prev, series) {
          return prev + series.value;
        }, 0);
        return total + '%';
      }
    },
    events: {
      draw(data: any): void {
        if (data.type === 'label') {
          if (data.index === 0) {
            data.element.attr({
              dx: data.element.root().width() / 2,
              dy: data.element.root().height() / 2
            });
          } else {
            data.element.remove();
          }
        }

      }
    }
  };
  // Donut chart configuration Ends

  //  Bar chart configuration Starts
  BarChart: Chart = {
    type: 'Bar', data: data['DashboardBar'], options: {
      axisX: {
        showGrid: false,
      },
      axisY: {
        showGrid: false,
        showLabel: false,
        offset: 0
      },
      low: 0,
      high: 60, // creative tim: we recommend you to set the high sa the biggest value + something for a better look
    },
    responsiveOptions: [
      ['screen and (max-width: 640px)', {
        seriesBarDistance: 5,
        axisX: {
          labelInterpolationFnc: function (value) {
            return value[0];
          }
        }
      }]
    ],
    events: {
      created(data: any): void {
        const defs = data.svg.elem('defs');
        defs.elem('linearGradient', {
          id: 'gradient4',
          x1: 0,
          y1: 1,
          x2: 0,
          y2: 0
        }).elem('stop', {
          offset: 0,
          'stop-color': '#8E1A38'
        }).parent().elem('stop', {
          offset: 1,
          'stop-color': '#FAA750'
        });
        defs.elem('linearGradient', {
          id: 'gradient5',
          x1: 0,
          y1: 1,
          x2: 0,
          y2: 0
        }).elem('stop', {
          offset: 0,
          'stop-color': '#1750A5'
        }).parent().elem('stop', {
          offset: 1,
          'stop-color': '#40C057'
        });

        defs.elem('linearGradient', {
          id: 'gradient6',
          x1: 0,
          y1: 1,
          x2: 0,
          y2: 0
        }).elem('stop', {
          offset: 0,
          'stop-color': '#3B1C93'
        }).parent().elem('stop', {
          offset: 1,
          'stop-color': '#60AFF0'
        });
        defs.elem('linearGradient', {
          id: 'gradient7',
          x1: 0,
          y1: 1,
          x2: 0,
          y2: 0
        }).elem('stop', {
          offset: 0,
          'stop-color': '#562DB7'
        }).parent().elem('stop', {
          offset: 1,
          'stop-color': '#F55252'
        });

      },
      draw(data: any): void {
        let barHorizontalCenter, barVerticalCenter, label, value;
        if (data.type === 'bar') {

          data.element.attr({
            y1: 195,
            x1: data.x1 + 0.001
          });

        }
      }
    },

  };
  // Bar chart configuration Ends

  // line chart configuration Starts
  WidgetlineChart: Chart = {
    type: 'Line', data: data['Dashboard1_WidgetlineChart'],
    options: {
      axisX: {
        showGrid: false,
        showLabel: false,
        offset: 0
      },
      axisY: {
        showGrid: false,
        low: 40,
        showLabel: false,
        offset: 0
      },
      plugins: [
        ChartistTooltip({
          appendToBody: true,
          currency: '$',
          pointClass: CommonConstant.CT_POINT_REGULER
        })
      ],
      lineSmooth: Chartist.Interpolation.cardinal({
        tension: 0
      }),
      fullWidth: true
    },
    events: {
      draw(data: any): void {
        if (data.type === 'point') {
          const circle = new Chartist.Svg('circle', {
            cx: data.x,
            cy: data.y,
            r: 4,
            'ct:value': data.value.y,
            'ct:meta': data.meta,
            style: CommonConstant.POINTER_EVENTALL_IMPORTANT,
            class: CommonConstant.CT_POINT_REGULER
          });
          data.element.replace(circle);
        }
      }
    }
  };
  // Line chart configuration Ends

  // line chart configuration Starts
  WidgetlineChart1: Chart = {
    type: 'Line', data: data['Dashboard1_WidgetlineChart1'],
    options: {
      axisX: {
        showGrid: false,
        showLabel: false,
        offset: 0
      },
      axisY: {
        showGrid: false,
        low: 40,
        showLabel: false,
        offset: 0
      },
      plugins: [
        ChartistTooltip({
          appendToBody: true,
          currency: '$',
          pointClass: CommonConstant.CT_POINT_REGULER
        })
      ],
      lineSmooth: Chartist.Interpolation.cardinal({
        tension: 0
      }),
      fullWidth: true
    },
    events: {
      draw(data: any): void {
        if (data.type === 'point') {
          const circle = new Chartist.Svg('circle', {
            cx: data.x,
            cy: data.y,
            r: 4,
            'ct:value': data.value.y,
            'ct:meta': data.meta,
            style: CommonConstant.POINTER_EVENTALL_IMPORTANT,
            class: CommonConstant.CT_POINT_REGULER
          });
          data.element.replace(circle);
        }
      }
    }
  };
  // Line chart configuration Ends

  // line chart configuration Starts
  WidgetlineChart2: Chart = {
    type: 'Line', data: data['Dashboard1_WidgetlineChart2'],
    options: {
      axisX: {
        showGrid: false,
        showLabel: false,
        offset: 0
      },
      axisY: {
        showGrid: false,
        low: 40,
        showLabel: false,
        offset: 0
      },
      plugins: [
        ChartistTooltip({
          appendToBody: true,
          currency: '$',
          pointClass: CommonConstant.CT_POINT_REGULER
        })
      ],
      lineSmooth: Chartist.Interpolation.cardinal({
        tension: 0
      }),
      fullWidth: true
    },
    events: {
      draw(data: any): void {
        if (data.type === 'point') {
          const circle = new Chartist.Svg('circle', {
            cx: data.x,
            cy: data.y,
            r: 4,
            'ct:value': data.value.y,
            'ct:meta': data.meta,
            style: CommonConstant.POINTER_EVENTALL_IMPORTANT,
            class: CommonConstant.CT_POINT_REGULER
          });
          data.element.replace(circle);
        }
      }
    }
  };
  // Line chart configuration Ends

  // line chart configuration Starts
  WidgetlineChart3: Chart = {
    type: 'Line', data: data['Dashboard1_WidgetlineChart3'],
    options: {
      axisX: {
        showGrid: false,
        showLabel: false,
        offset: 0
      },
      axisY: {
        showGrid: false,
        low: 40,
        showLabel: false,
        offset: 0
      },
      plugins: [
        ChartistTooltip({
          appendToBody: true,
          currency: '$',
          pointClass: CommonConstant.CT_POINT_REGULER
        })
      ],
      lineSmooth: Chartist.Interpolation.cardinal({
        tension: 0
      }),
      fullWidth: true
    },
    events: {
      draw(data: any): void {
        if (data.type === 'point') {
          const circle = new Chartist.Svg('circle', {
            cx: data.x,
            cy: data.y,
            r: 4,
            'ct:value': data.value.y,
            'ct:meta': data.meta,
            style: CommonConstant.POINTER_EVENTALL_IMPORTANT,
            class: CommonConstant.CT_POINT_REGULER
          });
          data.element.replace(circle);
        }
      }
    }
  };

  // Line chart configuration Ends

  public ColumnMode = ColumnMode;
  public tempData = [];
  dashboardTable;
  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  documents: Document[] = [];
  isReload = false;
  refNumLabel: string;
  customerColumnLength = DashboardTableCustomerMobile.columns.length;
  bmColumnLength = DashboardTableBranchManager.columns.length;
  runTable = false;
  swal = swalFunction;
  private pathSrc: string;
  msg: string;
  tenantCode: string;
  tenantGlobal: string; 
  isMobile = false; 
  isOffice = true;
  totalResult: number;

  constructor(private router: Router, private activeRoute: ActivatedRoute, private modalService: NgbModal,
    private global: GlobalService, private http: HttpClient, private ngZone: NgZone, private cdr: ChangeDetectorRef,
    private deviceService: DeviceDetectorService, private toastrService: ToastrService, private checkStatusDocument: CheckStatusDocumentService, private countlyService: CountlyService) {
    if (deviceService.isMobile()) {
      this.isMobile = true;
    }
    const snapshot = this.activeRoute.snapshot.data;
    this.pathSrc = snapshot.path;
    this.tempData = DashboardDocument;
    this.activeRoute.queryParams.subscribe(params => {
      this.isReload = params?.isReload;

      if (params['msg']) {
        this.msg = params['msg'];
      }
      if (params['isOffice']) {
        this.isOffice = JSON.parse(params['isOffice']);
        console.log('isOffice', this.isOffice);

      }
      if (params['tenantCode'] && this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
        this.tenantCode = params['tenantCode'];
        this.tenantGlobal = params['tenantCode'];
      } else{
        this.tenantGlobal = '';
      }
    });
  }

  async ngOnInit() {
    localStorage.removeItem('msg');
    if (this.msg && this.router.url.includes('/embed/')) {
      localStorage.removeItem('token');
      localStorage.clear();
    }

    if (this.isReload) {
      this.isReload = null;
      window.top.location.href = window.location.pathname;
    }
    
    if (this.msg) {
      const user: UserProfile = new UserProfile();
      user.pathSrc = this.pathSrc;
      user.loginId = 'CONFINS';
      user.role = {
        tenantCode: this.tenantGlobal,
        tenantName: '',
        roleCode: '',
        roleName: ''
      };
      user.roles = [{
        tenantCode: this.tenantGlobal,
        tenantName: '',
        roleCode: '',
        roleName: ''
      }];
      this.global.msg = this.msg;
      this.global.user = user;
    }

   await this.getRefNumLabel();

    if (this.global.user.role.roleCode === CommonConstant.CUSTOMER ||
      this.global.user.role.roleCode === CommonConstant.GUARANTOR ||
      this.global.user.role.roleCode === CommonConstant.SPOUSE) {
        if (this.isMobile) {
          this.customerColumnLength = DashboardTableCustomerMobile.columns.length;
          if (this.customerColumnLength < 9) {
            this.customerColumnLength = DashboardTableCustomerMobile.columns.unshift(
              {
                type: ColumnType.Text,
                cardType: ColumnType.Title,
                prop: 'refNumber',
                label: this.refNumLabel,
                width: 140
              }
            );
          }
          this.dashboardTable = DashboardTableCustomerMobile; 
        } else {
          this.customerColumnLength = DashboardTableCustomer.columns.length;
          if (this.customerColumnLength < 9) {
            this.customerColumnLength = DashboardTableCustomer.columns.unshift(
              {
                  type: ColumnType.Text,
                  prop: 'refNumber',
                  label: this.refNumLabel,
                  width: 140
              }
            );
          }
          this.dashboardTable = DashboardTableCustomer;
        }
    } else if (this.global.user.role.roleCode === CommonConstant.BRANCH_MANAGER || this.msg) {
      if (this.isMobile) {
        this.bmColumnLength = DashboardTableBranchManagerMobile.columns.length;
        if (this.bmColumnLength < 10){
          DashboardTableBranchManagerMobile.columns.unshift(
            {
              type: ColumnType.Text,
              cardType: ColumnType.Title,
              prop: 'refNumber',
              label: this.refNumLabel,
              width: 120
            }
          );
        }
        this.dashboardTable = DashboardTableBranchManagerMobile;
      } else {
        this.bmColumnLength = DashboardTableBranchManager.columns.length;
        if (this.bmColumnLength < 10){
          DashboardTableBranchManager.columns.unshift(
            {
              type: ColumnType.Text,
              prop: 'refNumber',
              label: this.refNumLabel,
              width: 120
          }
          );
        }
        this.dashboardTable = DashboardTableBranchManager;
      }
    }

    await this.getDashboardData().then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });

    this.runTable = true;
    this.countlyService.initiate();

  }

  async getRefNumLabel() {
    const tenantSettingReq: TenantSettingRequest = new TenantSettingRequest();
    let url;
    if (this.msg && !this.router.url.includes('/V2/')) {
      tenantSettingReq.msg = this.msg;
      url = URLConstant.GetTenantSettingEmbed;
    } else if(this.msg && this.router.url.includes('/V2/')){
      tenantSettingReq.msg = this.msg;
      tenantSettingReq.tenantCode = this.tenantCode;
      url = URLConstant.GetTenantSettingEmbedV2;
    } else {
      tenantSettingReq.tenantCode = this.global.user.role.tenantCode;
      url = URLConstant.GetTenantSetting;
    }
    tenantSettingReq.param = 'REF_NO_LABEL';
    await this.http.post(url, tenantSettingReq).toPromise().then(
      (response) => {
        if (response["status"]["code"] != 0) {
          this.toastrService.error(response["status"]["message"]);
        } else {
          this.refNumLabel = response["refNumberLabel"];
          if (this.refNumLabel.includes(":")) {
            this.refNumLabel = this.refNumLabel.substring(0,this.refNumLabel.length-2);
          }
        }
      }
    );
  }

  async getDashboardData(pageNumber: number = 1) {
    const request = new InquiryRequest();
    let url: string;
    if (this.msg && !this.router.url.includes('/V2/')) {
      request.msg = this.msg;
      url = URLConstant.InquiryDocumentEmbed;
    }  else if(this.msg && this.router.url.includes('/V2/')){
      request.msg = this.msg;
      request.tenantCode = this.tenantCode;
      request.isActive = "1";
      url = URLConstant.InquiryDocumentEmbedV2;
    }
    else {
      request.tenantCode = this.global.user.role.tenantCode;
      request.roleCode = this.global.user.role.roleCode;
      url = URLConstant.InquiryDocument;
    }
    request.transactionStatus = CommonConstant.NEED_SIGN;
    request.page = pageNumber;
    request.inquiryType = CommonConstant.INQUIRY_TYPE_INBOX;
    request.isOffice = this.isOffice;
    await this.http.post<InquiryResponse>(url, request).toPromise().then(
      (response) => {
        if (response.status.code === 0) {
          this.datasource.next(response);
          this.totalResult = response['totalResult'];
        }
      }
    );
  }

  async getPage(page: Page) {
    console.log('requestPage', page);
    await this.getDashboardData(page.pageNumber + 1).then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  onItemClickListener(event) {
    const data = event['data'];

    switch (event['act']['type']) {
      case Act.ViewSigner:
        return this.openSignerListModal(data);

      case Act.Download:
        return this.gotoViewOrDownload(data, true);

      case Act.Sign:
        if(data.vendorCode === Vendor.VIDA){
          return this.gotoSignVida(data);
        } else if(data.vendorCode === Vendor.PRIVY) {
          return this.gotoSignPrivy(data);
        }  
        // else if(this.msg && this.router.url.includes('/V2/')){
        //   return this.swal.Error('Tidak dapat melakukan tanda tangan dokumen Digisign !');
        // }
        else{
          return this.gotoSign(data);
        }

        
      
      default:
        return this.gotoViewOrDownload(data, false);
    }
  }

  gotoViewOrDownload(data: Document, download: boolean) {
    console.log('Data', data);
    const request: ViewDocumentRequest = new ViewDocumentRequest();
    request.documentId = data.documentId;
    let url;
    if (this.msg && this.router.url.includes('/embed/') && !this.router.url.includes('/V2/')){
      request.msg = this.msg;
      url = URLConstant.ViewDocumentEmbed;
    } else if(this.msg && this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
      request.msg = this.msg;
      request.tenantCode = this.tenantCode;
      url =URLConstant.ViewDocumentEmbedV2;
    } 
    else{
      url =URLConstant.ViewDocument;
    }
    
    this.http.post<ViewDocumentResponse>(url, request).subscribe(
      (response) => {
        if (response.status.code === 0) {
          if (download) {
            const downloadLink = document.createElement('a');
            const fileName = data.docTemplateName + '.pdf';

            downloadLink.href = `data:application/pdf;base64,${response.pdfBase64}`;
            downloadLink.download = fileName;
            downloadLink.click();
          } else {
            const extras = { 
              pdfBase64: response.pdfBase64, 
              refNumber: data.refNumber,
              msg: this.msg,
              tenantCode: this.tenantCode,
              isOffice: this.isOffice,
              isSign: true,
             };
            data = { ...data, ...extras };
            this.navigateAfterView(data);
          }
        }
      }
    );
  }

  navigateAfterView(data) {
    if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      this.router.navigate([PathConstant.EMBED_VIEW_DOCUMENT_INQUIRY], { state: data });
    } 
    else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
      this.router.navigate([PathConstant.EMBED_V2_VIEW_DOCUMENT_INQUIRY], { state: data });
    } 
    else {
      this.router.navigate([PathConstant.VIEW_DOCUMENT_INQUIRY], { state: data });
    }
  }

  gotoSign(data: Document) {
    const extras: NavigationExtras = {
      state: {
        refNumber: data.refNumber
      },
      queryParams: {
        id: data.documentId
      }
    };
    if (this.pathSrc === CommonConstant.PATH_SRC_EMBED ) {
      this.router.navigate([PathConstant.EMBED_SIGNATURE], extras);
    }  else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2){
      this.router.navigate([PathConstant.EMBED_V2_SIGNATURE], extras);
      }
    else {
      this.router.navigate([PathConstant.SIGNATURE], extras);
    }
  }

  openSignerListModal(data: Document) {
    const modal = this.modalService.open(SignerComponent, { size: 'lg' });
    modal.componentInstance.documentId = data.documentId;
    modal.componentInstance.isOffice = this.isOffice;
    if (this.msg) {
      modal.componentInstance.msg = this.msg;
    }
    if (this.tenantCode) {
      modal.componentInstance.tenantCode = this.tenantCode;
    }
  }


  onSelected(datas: Document[]) {
    console.log('Data', datas);
    this.documents = datas;
  }

  async gotoSignVida(data) {
    const request: SignDocumentRequest = new SignDocumentRequest();
      let signDocUrl;
      request.documentId = data.documentId;

      if (this.msg && !this.router.url.includes('/V2/')) {
        request.msg = this.msg;
        signDocUrl = URLConstant.SignDocumentEmbed;
      } 
      else if(this.msg && this.router.url.includes('/V2/')){
        request.msg = this.msg;
        request.tenantCode = this.tenantCode;
        signDocUrl = URLConstant.SignDocumentEmbedV2;
      }
      else {
        request.email = this.global.user.loginId;
        signDocUrl = URLConstant.SignDocument;
      }

       //check send document status
      // const checkDocStatus = await this.checkStatusDocument.validate(data.documentId, this.global.msg, this.router.url, data.refNumber);
      // if (checkDocStatus.documentSendStatus === '1') {
        await this.http.post<SignDocumentResponse>(signDocUrl, request).toPromise().then(
          async (response) => {
            if (response.status.code === 0 && response.vendorCode === Vendor.VIDA) {
              
              if (response.docs != null && response.docs.length !== 0) {
                const extras: NavigationExtras = {
                  state: {
                    data: response.docs,
                    vendorCode:response.vendorCode,
                    msg: this.msg,
                    tenantCode: this.tenantCode,
                  }
                };
                this.navigateSignVida(extras);
              } else{
                return this.goToBulkSign(data);
              }
            }  

          }, (err) => {
            this.toastrService.error(err.message, null, { positionClass: 'toast-top-right' });
          }
        );
      //}
  }

  navigateSignVida(extras: any) {
    if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      this.router.navigate([PathConstant.EMBED_BULK_SIGN], extras);
    } 
    else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
      this.router.navigate([PathConstant.EMBED_V2_BULK_SIGN], extras);
    } else {
      this.router.navigate([PathConstant.INQUIRY_BULK_SIGN], extras);
    }
  }

  async gotoSignPrivy(data) {
    const request: SignDocumentRequest = new SignDocumentRequest();
      let signDocUrl;
      request.documentId = data.documentId;

      if (this.msg && !this.router.url.includes('/V2/')) {
        request.msg = this.msg;
        signDocUrl = URLConstant.SignDocumentEmbed;
      } 
      else if(this.msg && this.router.url.includes('/V2/')){
        request.msg = this.msg;
        request.tenantCode = this.tenantCode;
        signDocUrl = URLConstant.SignDocumentEmbedV2;
      }
      else {
        request.email = this.global.user.loginId;
        signDocUrl = URLConstant.SignDocument;
      }

       //check send document status
      // const checkDocStatus = await this.checkStatusDocument.validate(data.documentId, this.global.msg, this.router.url, data.refNumber);
      // if (checkDocStatus.documentSendStatus === '1') {
        await this.http.post<SignDocumentResponse>(signDocUrl, request).toPromise().then(
          async (response) => {
            if (response.status.code === 0 && response.vendorCode === Vendor.PRIVY) {
              
              if (response.docs != null && response.docs.length !== 0) {
                const extras: NavigationExtras = {
                  state: {
                    data: response.docs,
                    vendorCode:response.vendorCode,
                    msg: this.msg,
                    tenantCode: this.tenantCode,
                  }
                };
                if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
                  this.router.navigate([PathConstant.EMBED_BULK_SIGN], extras);
                } 
                else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
                  this.router.navigate([PathConstant.EMBED_V2_BULK_SIGN], extras);
                } else{
                this.router.navigate([PathConstant.INQUIRY_BULK_SIGN], extras);
                }
              } else{
                return this.goToBulkSign(data);
              }
            }  

          }, (err) => {
            this.toastrService.error(err.message, null, { positionClass: 'toast-top-right' });
          }
        );
      //}
  }

  goToBulkSign(datas) {
    //penjagaan untuk jika pilih dokumen dengan beda vendor, digi bersama vida
    let totalDigi = 0;
    let totalVida = 0;
    let totalPrivy = 0;
    let totalTknAj = 0;
    if(this.documents.length === 0){
      this.documents.push(datas);
    }

    for(let j = 0; j < this.documents.length; j++) {
      if(this.documents[j].vendorCode === Vendor.DigiSign){
        totalDigi ++;
      } else if(this.documents[j].vendorCode === Vendor.VIDA){
        totalVida ++;
      } else if(this.documents[j].vendorCode === Vendor.PRIVY){
        totalPrivy ++;
      } else if(this.documents[j].vendorCode === Vendor.TekenAja){
        totalTknAj ++;
      }

      if (this.documents[j].isCurrentTopPriority === '0') {
        return this.swal.Error('Terdapat Dokumen dengan urutan prioritas yang lebih tinggi yang perlu di tanda tangan terlebih dahulu. Silahkan pilih dokumen ulang.');
      }
    }
  
     if(totalDigi !== this.documents.length && totalVida !== this.documents.length && totalPrivy !== this.documents.length && totalTknAj !== this.documents.length){
       return this.swal.Error('Tidak dapat melakukan bulksign dengan vendor lebih dari 1! Silahkan pilih dokumen lain.');
     } 
    //  else if(totalDigi === this.documents.length && this.msg && this.router.url.includes('/V2/')){
    //    return this.swal.Error('Tidak dapat melakukan tanda tangan dokumen Digisign !');
    //  }

    
    //this.documents = datas;
    const extras: NavigationExtras = {
      state: {
        msg: this.msg,
        tenantCode: this.tenantCode,
        data: this.documents
      }
    };

    // START: Workaround bulksign page kosong
    if (this.documents.length === 1 && this.documents[0].vendorCode !== Vendor.VIDA && this.documents[0].vendorCode !== Vendor.PRIVY) {

      const singleSignExtras: NavigationExtras = {
        state: {
          refNumber: this.documents[0].refNumber,
          skipBulksignCheck: '1'
        },
        queryParams: {
          id: this.documents[0].documentId
        }
      };

      if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
        this.router.navigate([PathConstant.EMBED_SIGNATURE], singleSignExtras);
      } else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2){
        this.router.navigate([PathConstant.EMBED_V2_BULK_SIGN], extras);
      }
      else {
        console.log('Single sign, skip bulksign check');
        this.router.navigate([PathConstant.SIGNATURE], singleSignExtras);
      }
      return;
    }
    // END: Workaround bulksign page kosong

    if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
      this.router.navigate([PathConstant.EMBED_BULK_SIGN], extras);
    } 
    else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
      this.router.navigate([PathConstant.EMBED_V2_BULK_SIGN], extras);
    }
    else {
      this.router.navigate([PathConstant.INQUIRY_BULK_SIGN], extras);
    }
  }

}
