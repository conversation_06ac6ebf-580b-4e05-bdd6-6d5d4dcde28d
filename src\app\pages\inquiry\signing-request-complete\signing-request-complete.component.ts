import { Component, Input, OnInit } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Console } from 'console';

@Component({
  selector: 'app-signing-request-complete',
  templateUrl: './signing-request-complete.component.html',
  styleUrls: ['./signing-request-complete.component.scss']
})
export class SigningRequestCompleteComponent implements OnInit {

  @Input() msg: string;
  @Input() tenantCode: string;
  @Input() isRedirect: boolean;
  @Input() redirectUrl: string;
  @Input() redirectCountDown: number;

  interval;

  constructor(public activeModal: NgbActiveModal, private router: Router) { }

  ngOnInit(): void {
    if (this.isRedirect) {
      this.startTimer();
    }
    console.log(this.redirectUrl);
  }

  ok() {
    this.activeModal.close();
    if(this.msg && this.router.url.includes('/V2/')) {
      const extras: NavigationExtras = {
        queryParams: {
          msg: this.msg,
          tenantCode: this.tenantCode
        }
      };
      this.router.navigate([PathConstant.EMBED_V2_DASHBOARD], extras);
    }
    else {
      this.router.navigate([PathConstant.DASHBOARD]);
    }
  }

  startTimer() {
    this.interval = setInterval(() => {
      if (this.redirectCountDown > 0) {
        this.redirectCountDown--;
      } else {
        this.redirectCountDown = 0;
        window.location.href = this.redirectUrl;
      }
    }, 1000);
  }

}
