import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, Input, NgZone, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { JobResult } from 'app/model/job-result';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { BehaviorSubject } from 'rxjs';
import { RequestParamTable } from './view-request/request-param.view';

@Component({
  selector: 'app-view-request-param',
  templateUrl: './view-request-param.component.html',
  styleUrls: ['./view-request-param.component.scss']
})
export class ViewRequestParamComponent implements OnInit {
  @Input() idJobResult : any;
  
  requestParamTable = RequestParamTable;

  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  
  constructor(public activeModal: NgbActiveModal, public http: HttpClient,
    private ngZone: NgZone, private cdr: ChangeDetectorRef) { }

  async ngOnInit() {
    
    await this.getRequestParam().then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  async getRequestParam() {
    const url = URLConstant.getRequestParamJobResult;

    await this.http.post(url, { jobResultId: this.idJobResult}).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
          this.datasource.next(response);
        }
      }
    )
  }
}
