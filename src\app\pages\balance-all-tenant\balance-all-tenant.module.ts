import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { BalanceAllTenantRoutingModule } from './balance-all-tenant-routing.module';
import { BalanceAllTenantComponent } from './balance-all-tenant.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { PipeModule } from 'app/shared/pipes/pipe.module';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { SharedModule } from 'app/shared/shared.module';
import { PdfJsViewerModule } from 'ngx-pdfjs-viewer';
import { MsFormModule } from 'app/shared/components/ms-form/ms-form.module';
import { NgxSpinnerModule } from 'ngx-spinner';


@NgModule({
  declarations: [BalanceAllTenantComponent],
  imports: [
    CommonModule,    
    BalanceAllTenantRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgbModule,
    PipeModule,
    NgxDatatableModule,
    SharedModule,
    PdfJsViewerModule,
    MsFormModule,
    NgxSpinnerModule
  ]
})
export class BalanceAllTenantModule { }
