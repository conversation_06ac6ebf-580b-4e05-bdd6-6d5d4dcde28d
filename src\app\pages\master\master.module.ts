import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MasterRoutingModule } from './master-routing.module';
import { DocumentsComponent } from './documents/documents.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {NgSelectModule} from '@ng-select/ng-select';
import {PipeModule} from '../../shared/pipes/pipe.module';
import {NgxDatatableModule} from '@swimlane/ngx-datatable';
import { CreateDocumentComponent } from './documents/modal/create-document/create-document.component';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap';
import {SharedModule} from '../../shared/shared.module';
import { SettingSignerComponent } from './documents/setting-signer/setting-signer.component';
import {PdfJsViewerModule} from 'ngx-pdfjs-viewer';
import { SettingDocumentComponent } from './documents/modal/setting-document/setting-document.component';
import { ViewDocumentComponent } from './view-document/view-document.component';
import {DragDropModule} from '@angular/cdk/drag-drop';
import {MsFormModule} from '../../shared/components/ms-form/ms-form.module';
import { AddDocumentTemplateComponent } from './documents/add-document-template/add-document-template.component';
import { StampDutyComponent } from './stamp-duty/stamp-duty.component';
import { TopupStampDutyComponent } from './stamp-duty/topup-stamp-duty/topup-stamp-duty.component';
import { MateraiDetailComponent } from './stamp-duty/materai-detail/materai-detail.component';
import { ReversalTopupComponent } from './stamp-duty/reversal-topup/reversal-topup.component';
import { EmeteraiComponent } from './emeterai/emeterai.component';
import { ManualUploadComponent } from './documents/manual-upload/manual-upload.component';
import { AddSignerComponent } from './documents/modal/add-signer/add-signer.component';
import { SettingSequentialSignerComponent } from './documents/setting-sequential-signer/setting-sequential-signer.component';
import { ManualStampComponent } from './documents/manual-stamp/manual-stamp.component';
import { SettingManualSequentialComponent } from './documents/setting-manual-sequential/setting-manual-sequential.component';


@NgModule({
  declarations: [
    DocumentsComponent,
    CreateDocumentComponent,
    SettingSignerComponent,
    SettingDocumentComponent,
    ViewDocumentComponent,
    AddDocumentTemplateComponent,
    StampDutyComponent,
    TopupStampDutyComponent,
    MateraiDetailComponent,
    ReversalTopupComponent,
    EmeteraiComponent,
    ManualUploadComponent,
    AddSignerComponent,
    SettingSequentialSignerComponent,
    ManualStampComponent,
    SettingManualSequentialComponent,
  ],
  imports: [
    CommonModule,
    MasterRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgbModule,
    PipeModule,
    NgxDatatableModule,
    SharedModule,
    PdfJsViewerModule,
    DragDropModule,
    MsFormModule
  ],
  entryComponents: [
    CreateDocumentComponent,
    SettingDocumentComponent
  ]
})
export class MasterModule { }
