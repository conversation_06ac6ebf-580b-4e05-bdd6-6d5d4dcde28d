
import { RequestParamJobResult } from "app/model/request-param-job-result";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";

export const RequestParamTable: Table<RequestParamJobResult> = {
    name: 'listRequestParam',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'vendorCode',
            label: 'Vendor Code',
            width: 90
        },
        {
            type: ColumnType.Text,
            prop: 'tenantCode',
            label: 'Tenant Code',
            width: 120
        },
        {
            type: ColumnType.Text,
            prop: 'transactionDateStart',
            label: 'Start Date',
            width: 300
        },
        {
            type: ColumnType.Text,
            prop: 'transactionDateEnd',
            label: 'End Date',
            width: 130
        },
        {
            type: ColumnType.Text,
            prop: 'balanceType',
            label: 'Saldo Type',
            width: 100
        }
    ]
}