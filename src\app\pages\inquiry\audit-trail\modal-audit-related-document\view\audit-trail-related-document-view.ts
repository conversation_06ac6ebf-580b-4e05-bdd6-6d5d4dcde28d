import { ListInquiryAuditTrailRelatedDocument } from "app/model/api/AuditTrailRelatedDocument";
import { Signer } from "app/model/signer";
import { ColumnType } from "app/shared/components/msx-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-datatable/models/table";
import { CommonConstant } from "app/shared/constant/common.constant";

export const listInquiryAuditTrailRelatedDocument: Table<ListInquiryAuditTrailRelatedDocument> = {
    name: 'listSigningProcessAuditTrailDetailBean',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'refNumber',
            label: "Ref Number",
            width: 150
          },
          {
            type: ColumnType.Text,
            prop: 'documentId',
            label: 'Document Id',
            width: 150
          }
    ]
}