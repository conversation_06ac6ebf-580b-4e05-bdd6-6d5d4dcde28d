import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import {SharedModule} from '../../shared/shared.module';
import {MsFormModule} from '../../shared/components/ms-form/ms-form.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { PipeModule } from 'app/shared/pipes/pipe.module';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { PdfJsViewerModule } from 'ngx-pdfjs-viewer';
import { NgxSpinnerModule } from 'ngx-spinner';
import { MonitoringComponent } from './monitoring-embed/monitoring.component';
import { MonitoringRoutingModule } from './monitoring-routing.module';
import { StampdutyErrorComponent } from './monitoring-embed/stampduty-error/stampduty-error.component';
import { MonitoringNormalComponent } from './monitoring-normal/monitoring-normal.component';



@NgModule({
  declarations: [MonitoringComponent, StampdutyErrorComponent, MonitoringNormalComponent],
  imports: [
    MonitoringRoutingModule,
    CommonModule,
    SharedModule,
    MsFormModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgbModule,
    PipeModule,
    NgxDatatableModule,
    SharedModule,
    PdfJsViewerModule,
    MsFormModule,
    NgxSpinnerModule
  ]
})
export class MonitoringModule { }