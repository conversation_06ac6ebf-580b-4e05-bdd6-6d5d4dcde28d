<div class="row" style="margin-top: 15px">
    <div class="col-6">
      <div class="content-header" style="margin-top: 0 !important;">Tenant Service Balance</div>
    </div>
</div>
<div class="row match-height">
  <div class="col-6">
      <div class="card">
          <div class="card-content">
              <div class="card-body">
                <table class="table table-sm table-border center">
                  <tr>
                    <th></th>
                    <th class="header" *ngFor="let vendor of vendors">{{vendor.name}}</th>
                  </tr>
                  <tr *ngFor="let balance of balances">
                    <th class="header">{{balance.description}}</th>
                    <th *ngFor="let vendor of vendors">
                      <div *ngFor="let balanceTenant of balanceTenants">
                        <input *ngIf="balanceTenant.balanceTypeCode === balance.code && balanceTenant.vendorCode === vendor.code"
                        class="form-check-input" type="checkbox" value="" id="{{balance.code}}@{{vendor.code}}" [checked]="balanceTenant.isActive === '1'">
                      </div>
                    </th>
                  </tr>
                </table>

                <div class="row">
                  <div class="col-12 text-center">
                      <button class="btn btn-light mr-2" (click)="goBack()" translate>Cancel</button>
                      <button class="btn btn-info" (click)="saveBalanceTenant()" translate>Save</button>
                  </div>
              </div>
              </div>
          </div>
      </div>
  </div>
</div>

<ngx-spinner style="visibility: hidden;"></ngx-spinner>