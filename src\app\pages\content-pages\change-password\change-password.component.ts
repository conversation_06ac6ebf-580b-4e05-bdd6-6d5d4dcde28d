import { ChangePasswordModalComponent } from './change-password-modal/change-password-modal.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ChangePasswordRequest } from 'app/shared/dto/password/change-password.request';
import { ChangePasswordResponse } from 'app/shared/dto/password/change-password.response';
import { ToastrService } from 'ngx-toastr';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { HttpService } from 'app/services/http.service';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss']
})
export class ChangePasswordComponent implements OnInit {

  formObj: FormModel<any>;
  xForm: FormGroup;
  redirect: string;
  swal = swalFunction;
  showNavbar = false;

  constructor(private global: GlobalService, private http: HttpClient,
    private toastService: ToastrService, private readonly location: Location,
    private router: Router, private activatedRoute: ActivatedRoute,
    private modalService: NgbModal, private httpService: HttpService) {
      this.redirect = this.router.getCurrentNavigation().extras.state?.redirect;
      if (this.activatedRoute.snapshot.data['showNavbar'] != null) {
        this.showNavbar = this.activatedRoute.snapshot.data['showNavbar'];
      }
    }

  ngOnInit(): void {
    this.initView();
    console.log('Redirect', this.redirect);
  }

  initView() {
    this.formObj = {
      name: 'changePassword',
      colSize: 12,
      direction: FormConstant.DIRECTION_VERTICAL,
      components: [
        {
          key: 'password',
          label: 'Access Code',
          placeholder: 'Enter your current Access Code',
          controlType: FormConstant.TYPE_PASSWORD,
          icon: 'ft-lock',
          required: true
        },
        {
          key: 'newPassword',
          label: 'New Access Code',
          placeholder: 'Type your new Access Code here',
          controlType: FormConstant.TYPE_PASSWORD,
          icon: 'ft-lock',
          required: true
        },
        {
          key: 'confirmNewPassword',
          label: 'Confirm New Access Code',
          placeholder: 'Confirm New Access Code',
          controlType: FormConstant.TYPE_PASSWORD,
          icon: 'ft-lock',
          required: true
        }
      ],
      params: [
      ]
    }
  }

  onForm(form: FormGroup) {
    this.xForm = form;
  }


  onNext(data: any) {
    if (data['password']) {
      if (data['newPassword'] !== data['confirmNewPassword']) {
        this.toastService.error('Input kode akses baru berbeda', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        return;
      }

      const request = new ChangePasswordRequest();
      request.audit = {
        callerId: this.global.user.loginId
      };
      request.loginId = this.global.user.loginId;
      request.oldPassword = data['password'];
      request.newPassword = data['newPassword'];

      this.httpService.post<ChangePasswordResponse>(URLConstant.ChangePassword, request).subscribe((response) => {
        if (response.status.code !== 0) {
          return;
        }
        
        // Update flag change password
        const user = this.global.user;
        user.changePwdLogin = '0';
        this.global.user = user;
        
        this.swal.Success('Kode Akses anda berhasil diganti.');
        
        if (this.redirect) {
          this.router.navigateByUrl(this.redirect);
        } else {
          this.location.back();
        }
      });
    }
  }

  openModal() {
    const modal = this.modalService.open(ChangePasswordModalComponent, { backdrop: 'static', keyboard: false });
  }

}
