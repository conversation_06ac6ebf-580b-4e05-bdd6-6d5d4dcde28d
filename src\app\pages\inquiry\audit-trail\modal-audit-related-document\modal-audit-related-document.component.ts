import { Component, Input, OnInit } from '@angular/core';
import { BehaviorSubject } from 'rxjs/internal/BehaviorSubject';
import { listInquiryAuditTrailRelatedDocument } from './view/audit-trail-related-document-view';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { GetListAuditTrailRelatedDocument } from 'app/model/getAuditTrailRelatedDocument';
import { HttpClient } from '@angular/common/http';
import { URLConstant } from 'app/shared/constant/URLConstant';

@Component({
  selector: 'app-modal-audit-related-document',
  templateUrl: './modal-audit-related-document.component.html',
  styleUrls: ['./modal-audit-related-document.component.scss']
})
export class ModalAuditRelatedDocumentComponent implements OnInit {

  @Input() data: any;

  ListInquiryAuditTrailRelatedDocument = listInquiryAuditTrailRelatedDocument;

  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);

  constructor(public activeModal: NgbActiveModal, private http: HttpClient) {
  
  }

  ngOnInit(): void {
    this.getAuditTrailRelatedDocument(this.data);
  }

  getAuditTrailRelatedDocument (params) {
    const request = new GetListAuditTrailRelatedDocument();
    request.idSigningProcessAuditTrail = params['idSigningProcessAuditTrail'];
    request.email = params['email'];

    this.http.post<any>(URLConstant.GetAuditTrailRelatedDocument, request).subscribe(response => {
      if (response.status.code === 0) {
        this.datasource.next(response);
      }
    })
  }
}
