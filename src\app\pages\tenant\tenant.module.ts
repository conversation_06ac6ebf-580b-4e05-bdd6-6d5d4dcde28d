import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { MsFormModule } from "app/shared/components/ms-form/ms-form.module";
import { PipeModule } from "app/shared/pipes/pipe.module";
import { SharedModule } from "app/shared/shared.module";
import { PdfJsViewerModule } from "ngx-pdfjs-viewer";
import { NgxSpinnerModule } from "ngx-spinner";
import { TenantRoutingModule } from "./tenant-routing.module";
import { TenantComponent } from "./tenant.component";
import { AddTenantComponent } from './add-tenant/add-tenant.component';
import { ServiceBalanceComponent } from './service-balance/service-balance.component';
import { TenantSettingsListComponent } from './tenant-settings/tenant-settings-list/tenant-settings-list.component';
import { EditTenantSettingsComponent } from './tenant-settings/edit-tenant-settings/edit-tenant-settings.component';

@NgModule({
    declarations: [TenantComponent, AddTenantComponent, ServiceBalanceComponent, TenantSettingsListComponent, EditTenantSettingsComponent],
    imports: [
      CommonModule,
      TenantRoutingModule,
      FormsModule,
      ReactiveFormsModule,
      NgSelectModule,
      NgbModule,
      PipeModule,
      NgxDatatableModule,
      SharedModule,
      PdfJsViewerModule,
      MsFormModule,
      NgxSpinnerModule
    ],
    entryComponents: [
      TenantComponent,
      AddTenantComponent,
      ServiceBalanceComponent
    ]
  })
  export class TenantModule { }