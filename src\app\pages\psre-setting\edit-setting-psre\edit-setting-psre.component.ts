import { Component, OnInit } from "@angular/core";
import { Location } from "@angular/common";
import { FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { FormConstant } from "app/shared/components/ms-form/constants/form.constant";
import { FormModel } from "app/shared/components/ms-form/models";
import { MsxFormControlService } from "app/shared/components/ms-form/msx-form-control.service";
import { QuestionDropdown, QuestionTextbox } from "app/shared/components/ms-form/questions";
import { CommonConstant } from "app/shared/constant/common.constant";
import { EditPSrESettingRequest } from "app/model/api/edit-setting-psre.request";
import { Success } from "app/shared/data/sweet-alerts";
import { URLConstant } from "app/shared/constant/URLConstant";
import { PsreSetting } from "app/model/psre-setting";
import { PathConstant } from "app/shared/constant/PathConstant";
import { HttpClient } from "@angular/common/http";

@Component({
  selector: "app-edit-setting-psre",
  templateUrl: "./edit-setting-psre.component.html",
  styleUrls: ["./edit-setting-psre.component.scss"],
})
export class EditSettingPsreComponent implements OnInit {
  data: PsreSetting;
  formObj: FormModel<any>;
  userForm: FormGroup;
  status: any;

  constructor(
    private fcs: MsxFormControlService,
    private router: Router,
    private location: Location,
    private http: HttpClient
  ) {
    this.data = <PsreSetting>this.router.getCurrentNavigation().extras.state;
  }

  ngOnInit(): void {
    this.setupQuestions();
    const controls = [...this.formObj.params, ...this.formObj.components];
    this.userForm = this.fcs.toFormGroup(controls);
  }

  setupQuestions() {
    this.formObj = {
      name: "Edit PSrE",
      mode: CommonConstant.MODE_EDIT,
      colSize: 12,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        new QuestionTextbox({
          key: "vendorCode",
          label: "Vendor Code",
          required: true,
          readonly: true,
          value: this.data?.vendorCode,
        }),
        new QuestionTextbox({
          key: "vendorName",
          label: "Vendor Name",
          required: true,
          value: this.data?.vendorName,
        }),
        new QuestionDropdown({
          key: "status",
          label: "Status",
          required: true,
          options: [
            {
              key: "1",
              value: "Aktif",
            },
            {
              key: "0",
              value: "Tidak Aktif",
            },
          ],
          value: this.data?.status,
        }),
        new QuestionDropdown({
          key: "statusOperating",
          label: "Status Operating",
          required: true,
          options: [
            {
              key: "1",
              value: "Aktif",
            },
            {
              key: "0",
              value: "Tidak Aktif",
            },
          ],
          value: this.data?.statusOperating,
        }),
        new QuestionDropdown({
          key: "paymentSignType",
          label: "Payment Sign Type",
          required: true,
          serviceUrl: URLConstant.GetLov,
          options: [{ key: "", value: "Select Payment" }],
          args: {
            list: "lovList",
            key: "code",
            value: "description",
          },
          validations: [
            { type: "required", message: "Payment Sign Type harus di isi." },
          ],
          params: {
            lovGroup: "VENDOR_SIGN_PAYMENT_TYPE",
          },
          value: this.data?.code,
        }),
      ],
      params: [],
    };
  }

  getQuestion(key: string) {
    return this.formObj.components.find((x) => x.key === key);
  }

  onCancel() {
    this.location.back();
  }

  onSubmit() {
    const formData = this.userForm.getRawValue();

    const request = new EditPSrESettingRequest();

    request.vendorCode = formData.vendorCode;
    request.vendorName = formData.vendorName;
    request.status = formData.status;
    request.statusOperating = formData.statusOperating;
    request.paymentSignType = formData.paymentSignType;

    this.http.post(URLConstant.UpdatePSrESetting, request).subscribe(resp => {
      if (resp['status']['code'] === 0) {
        return Success('Success').then(() => {
          this.doBack();
        });
      } else {
        console.log("Error");
      }
    })
  }

  doBack() {
    this.router.navigate([PathConstant.PSRE_SETTING]);
  }
}
