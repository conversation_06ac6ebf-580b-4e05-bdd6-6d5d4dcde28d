import {ChangeDetectorRef, Component, Input, NgZone, OnInit, ViewEncapsulation} from '@angular/core';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import { HttpClient } from '@angular/common/http';
import { GlobalService } from 'app/shared/data/global.service';
import { BehaviorSubject } from 'rxjs';
import { SignerTable } from './view/signer.view';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { ViewSignerRequest } from 'app/shared/dto/signer/view-signer.request';
import { Router } from '@angular/router';
import { DeviceDetectorService } from 'ngx-device-detector';

@Component({
  selector: 'app-signer',
  templateUrl: './signer.component.html',
  styleUrls: ['./signer.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SignerComponent implements OnInit {
  @Input() documentId : string;
  @Input() msg : string;
  @Input() tenantCode : string;
  @Input() isMonitoring : boolean;
  @Input() isOffice : boolean;

  signerTable = SignerTable;
  stampDutyHist: {}[] = [];

  listSigner: any[] = [];
  isMobile = false;
  isSigned = false;

  datasource: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  
  constructor(public activeModal: NgbActiveModal, public http: HttpClient, private global: GlobalService,
    private deviceService: DeviceDetectorService, private ngZone: NgZone, private cdr: ChangeDetectorRef, private router: Router,) { 
      if (deviceService.isMobile()) {
        this.isMobile = true;
      }
    }

  async ngOnInit() {
    if (this.isMonitoring) {
      localStorage.clear();
    }
    
    await this.getSigner().then(() => {
      this.ngZone.run(() => {
        this.cdr.markForCheck();
      })
    });
  }

  async getSigner() {
    let url = URLConstant.ViewSigner;
    const request: ViewSignerRequest = new ViewSignerRequest();
    request.documentId = this.documentId;
    
    if (this.msg) {
      request.msg = this.msg;
      url = URLConstant.ViewSignerEmbed;
      request.isOffice = this.isOffice;
    }
    if (this.msg && this.tenantCode && this.router.url.includes('/V2/') ) {
      request.msg = this.msg;
      request.tenantCode = this.tenantCode;
      url = URLConstant.ViewSignerEmbedV2;
      request.isOffice = this.isOffice;
    }
    if (this.isMonitoring) {
      request.isMonitoring = this.isMonitoring;
    }
    await this.http.post(url, request).toPromise().then(
      (response) => {
        if (response['status']['code'] == 0) {
          this.datasource.next(response);
          this.listSigner = response['listSigner'];
        }
      }
    )
  }

  getSignStatus(signer:any){
    if(signer.signStatus == 'Signed'){
      return "text-success";
    } else {
      return "text-danger";
    }
  }

}
